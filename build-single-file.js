const fs = require('fs');
const path = require('path');
const https = require('https');

async function downloadCSS(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => resolve(data));
    }).on('error', reject);
  });
}

async function buildSingleFile(outputName = 'image-to-pdf-standalone.html') {
  try {
    console.log('正在创建单文件HTML...');
    
    // 自动查找构建后的文件
    const htmlPath = path.join(__dirname, 'dist/index.html');
    const jsFiles = fs.readdirSync(path.join(__dirname, 'dist/assets')).filter(f => f.startsWith('index-') && f.endsWith('.js'));
    
    if (jsFiles.length === 0) {
      throw new Error('未找到构建后的JS文件，请先运行 npm run build');
    }
    
    const jsPath = path.join(__dirname, 'dist/assets', jsFiles[0]);
    console.log(`找到JS文件: ${jsFiles[0]}`);
    
    const htmlContent = fs.readFileSync(htmlPath, 'utf8');
    const jsContent = fs.readFileSync(jsPath, 'utf8');
    
    console.log('正在下载外部CSS...');
    
    // 下载外部CSS
    const bootstrapCSS = await downloadCSS('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
    const cropperCSS = await downloadCSS('https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.1/cropper.min.css');
    
    console.log('正在合并文件...');
    
    // 创建单文件HTML
    const singleFileHTML = `<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>批量截图裁剪与PDF生成工具</title>
    <style>
/* Bootstrap CSS */
${bootstrapCSS}

/* Cropper CSS */
${cropperCSS}
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module">
${jsContent}
    </script>
  </body>
</html>`;

    // 写入单文件
    const outputPath = path.join(__dirname, outputName);
    fs.writeFileSync(outputPath, singleFileHTML, 'utf8');
    
    console.log(`✅ 单文件HTML已创建: ${outputPath}`);
    console.log(`📁 文件大小: ${(fs.statSync(outputPath).size / 1024 / 1024).toFixed(2)} MB`);
    console.log('\n🎉 现在你可以将这个HTML文件发布给其他人了！');
    
  } catch (error) {
    console.error('❌ 创建单文件失败:', error);
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const outputName = args.find(arg => arg.startsWith('--output='))?.split('=')[1] || 'image-to-pdf-standalone.html';
const helpFlag = args.includes('--help') || args.includes('-h');

if (helpFlag) {
  console.log(`
使用方法:
  node build-single-file.js [选项]

选项:
  --output=<filename>    指定输出文件名 (默认: image-to-pdf-standalone.html)
  --help, -h            显示此帮助信息

示例:
  node build-single-file.js
  node build-single-file.js --output=my-tool.html
`);
  process.exit(0);
}

buildSingleFile(outputName);