declare global {
  interface Window {
    webkit?: {
      messageHandlers?: {
        saveFile?: {
          postMessage: (message: any) => void;
        };
        permission?: {
          postMessage: (type: string) => void;
        };
      };
    };
    Android?: {
      saveFile: (base64Data: string, fileName: string) => void;
    };

    __permission_ios?: boolean;
    handleFlutterResult: (num: number) => void;
  }
}

export {};