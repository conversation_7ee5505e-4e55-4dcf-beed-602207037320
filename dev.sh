#!/bin/bash

# Development Environment Quick Start Script
echo "🚀 Starting Image to PDF Development Environment..."

# Check for HTTPS mode argument (default is HTTPS)
USE_HTTPS=true
if [[ "$1" == "--http" || "$1" == "-h" ]]; then
    USE_HTTPS=false
    echo "🌐 HTTP mode enabled (HTTPS disabled)"
else
    echo "🔒 HTTPS mode enabled (default)"
fi

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if environment files exist
if [ ! -f "backend/.env" ]; then
    print_warning "Backend .env file not found. Creating from example..."
    cp backend/.env.example backend/.env
    echo ""
    print_warning "⚠️  Configuration Required:"
    echo "   Option 1 (Recommended): Configure Nacos"
    echo "   - Set NACOS_SERVER_ADDR in backend/.env"
    echo "   - Run: ./scripts/setup-nacos-config.sh"
    echo ""
    echo "   Option 2 (Development): Use local environment variables"
    echo "   - Set TEXTIN_APP_ID and TEXTIN_SECRET in backend/.env"
    echo ""
    echo "   Press any key to continue..."
    read -n 1
fi

# Function to start backend
start_backend() {
    print_status "Starting backend server..."
    cd backend
    # Set development environment variables for enhanced logging and security testing
    export NODE_ENV=development
    export LOG_LEVEL=debug
    export DISABLE_CSRF=false
    print_status "Environment: NODE_ENV=$NODE_ENV, LOG_LEVEL=$LOG_LEVEL, DISABLE_CSRF=$DISABLE_CSRF"
    pnpm run dev &
    BACKEND_PID=$!
    cd ..
    echo $BACKEND_PID > .backend.pid
    print_success "Backend started (PID: $BACKEND_PID)"
}

# Function to start frontend
start_frontend() {
    if [ "$USE_HTTPS" = true ]; then
        print_status "Starting frontend server with HTTPS..."
        npm run dev:https &
    else
        print_status "Starting frontend server..."
        npm run dev &
    fi
    FRONTEND_PID=$!
    echo $FRONTEND_PID > .frontend.pid
    print_success "Frontend started (PID: $FRONTEND_PID)"
}

# Function to cleanup processes
cleanup() {
    print_status "Shutting down development servers..."
    
    if [ -f .backend.pid ]; then
        BACKEND_PID=$(cat .backend.pid)
        kill $BACKEND_PID 2>/dev/null
        rm .backend.pid
        print_success "Backend server stopped"
    fi
    
    if [ -f .frontend.pid ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        kill $FRONTEND_PID 2>/dev/null
        rm .frontend.pid
        print_success "Frontend server stopped"
    fi
    
    echo "👋 Development environment shut down"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start services
start_backend
sleep 2
start_frontend

# Wait for servers to start
sleep 3

# Get local network IP
LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | head -1 | awk '{print $2}')

# Set protocol based on HTTPS mode
if [ "$USE_HTTPS" = true ]; then
    PROTOCOL="https"
else
    PROTOCOL="http"
fi

echo ""
echo "🎉 Development environment is running!"
echo ""
echo "🌐 Application URLs:"
if [ "$USE_HTTPS" = true ]; then
    echo "   Local:    https://localhost:5173  ⚠️  Accept certificate warning"
    echo "   Network:  https://${LOCAL_IP}:5173  (may not work due to SSL)"
else
    echo "   Local:    http://localhost:5173"
    echo "   Network:  http://${LOCAL_IP}:5173  (for mobile/other devices)"
fi
echo "   Backend:  http://localhost:3001"
echo "   Health:   http://localhost:3001/health"
echo ""
echo "📱 Mobile Access:"
echo "   Use http://${LOCAL_IP}:5173 on your mobile device"
echo "   Ensure your device is on the same network"
echo ""
echo "📱 Features available:"
echo "   🤖 AI Smart Mode - Automatic document detection and cropping"
echo "   🔧 Hybrid Mode - AI processing with manual adjustment option"
echo "   ✂️  Manual Mode - Traditional manual cropping"
echo "   📄 PDF Generation - High-quality PDF output"
if [ "$USE_HTTPS" = true ]; then
    echo "   💾 File Save Dialog - Native save dialog (HTTPS required)"
fi
echo ""
echo "💡 Usage:"
echo "   sh dev.sh        - Start with HTTPS (default, enables save dialog)"
echo "   sh dev.sh --http - Start with HTTP only"
echo ""
echo "Press Ctrl+C to stop all servers"
echo ""

# Keep script running
while true; do
    sleep 1
done