import crypto from 'crypto';
import { logger } from './logger';

const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 12; // For GCM, 12 bytes is recommended
const AUTH_TAG_LENGTH = 16;

/**
 * Encrypts a plaintext string using AES-256-GCM.
 * The encryption key must be 32 bytes long.
 * @param plaintext The string to encrypt.
 * @param key The 32-byte encryption key, derived from a secret.
 * @returns A base64-encoded string representing [IV][Ciphertext][AuthTag].
 */
export function encrypt(plaintext: string, key: Buffer): string {
  logger.debug('[Crypto] Starting encryption...');
  const iv = crypto.randomBytes(IV_LENGTH);
  logger.debug(`[Crypto] Generated IV (hex): ${iv.toString('hex')}`);

  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

  const encrypted = Buffer.concat([cipher.update(plaintext, 'utf8'), cipher.final()]);
  const authTag = cipher.getAuthTag();
  
  logger.debug(`[Crypto] Ciphertext (hex): ${encrypted.toString('hex')}`);
  logger.debug(`[Crypto] Auth Tag (hex): ${authTag.toString('hex')}`);

  // Concatenate IV, encrypted data, and auth tag for storage or transmission.
  // This format is self-contained and allows for easy decryption.
  const combined = Buffer.concat([iv, encrypted, authTag]);
  logger.debug(`[Crypto] Combined data (hex): ${combined.toString('hex')}`);
  
  const result = combined.toString('base64');
  logger.debug(`[Crypto] Final Base64 output: ${result}`);

  return result;
}

/**
 * Decrypts a base64-encoded string that was encrypted with AES-256-GCM.
 * The decryption key must be the same 32-byte key used for encryption.
 * @param ciphertextBase64 The base64-encoded string in the format [IV][Ciphertext][AuthTag].
 * @param key The 32-byte decryption key.
 * @returns The original plaintext string.
 */
export function decrypt(ciphertextBase64: string, key: Buffer): string {
  const combined = Buffer.from(ciphertextBase64, 'base64');

  // Extract the IV, auth tag, and ciphertext from the combined buffer.
  const iv = combined.slice(0, IV_LENGTH);
  const authTag = combined.slice(combined.length - AUTH_TAG_LENGTH);
  const ciphertext = combined.slice(IV_LENGTH, combined.length - AUTH_TAG_LENGTH);

  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
  decipher.setAuthTag(authTag);

  const decrypted = Buffer.concat([decipher.update(ciphertext), decipher.final()]);

  return decrypted.toString('utf8');
}

/**
 * Generates a 32-byte key from a secret string using SHA-256.
 * This ensures the key is always the correct length for AES-256.
 * @param secret The secret string to derive the key from.
 * @returns A 32-byte buffer suitable for use as an AES-256 key.
 */
export function getKeyFromSecret(secret: string): Buffer {
    return crypto.createHash('sha256').update(String(secret)).digest();
}
