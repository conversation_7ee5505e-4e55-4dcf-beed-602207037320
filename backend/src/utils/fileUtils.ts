import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { logger } from './logger';
import config from './config';

export class FileUtils {
  static ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      logger.info(`Created directory: ${dirPath}`);
    }
  }

  static generateUniqueFileName(originalName: string): string {
    const ext = path.extname(originalName);
    const baseName = path.basename(originalName, ext);
    const uuid = uuidv4();
    return `${baseName}_${uuid}${ext}`;
  }

  static async cleanupTempFiles(): Promise<void> {
    try {
      const tempDir = config.storage.tempDir;
      if (!fs.existsSync(tempDir)) {
        return;
      }

      const files = fs.readdirSync(tempDir);
      const now = Date.now();
      const maxAge = config.storage.cleanupInterval;

      for (const file of files) {
        const filePath = path.join(tempDir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtimeMs > maxAge) {
          fs.rmSync(filePath, { recursive: true, force: true });
          logger.info(`Cleaned up temp item: ${file}`);
        }
      }
    } catch (error) {
      logger.error('Error during temp file cleanup', error);
    }
  }

  static startCleanupScheduler(): void {
    setInterval(() => {
      FileUtils.cleanupTempFiles();
    }, config.storage.cleanupInterval);
    
    logger.info('Started temp file cleanup scheduler');
  }

  static validateFileType(mimetype: string): boolean {
    const allowedTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/bmp',
      'image/webp',
      'image/tiff',
      'image/gif',
      'application/pdf'
    ];
    
    return allowedTypes.includes(mimetype);
  }

  static convertToBase64(buffer: Buffer): string {
    return buffer.toString('base64');
  }

  static base64ToBuffer(base64: string): Buffer {
    return Buffer.from(base64, 'base64');
  }
}

export default FileUtils;