import crypto from 'crypto';
import { logger } from './logger';

export interface DynamicKey {
  key: string;
  version: string;
  sessionId: string;
  createdAt: number;
  expiresAt: number;
}

export interface KeyDerivationConfig {
  masterSecret: string;
  keyRotationInterval: number; // 密钥轮换间隔(毫秒)
  sessionKeyTTL: number;       // 会话密钥生存时间(毫秒)
  maxVersions: number;         // 保持的最大密钥版本数
}

/**
 * 动态HMAC密钥管理器
 * 提供基于时间窗口和会话的动态密钥生成、轮换和管理
 */
export class DynamicKeyManager {
  private config: KeyDerivationConfig;
  private sessionKeys = new Map<string, DynamicKey>();
  private timeWindowKeys = new Map<string, { key: string; createdAt: number; expiresAt: number }>();
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<KeyDerivationConfig> = {}) {
    this.config = {
      masterSecret: config.masterSecret || process.env.HMAC_MASTER_SECRET || this.generateMasterSecret(),
      keyRotationInterval: config.keyRotationInterval || 60 * 60 * 1000, // 1小时
      sessionKeyTTL: config.sessionKeyTTL || 2 * 60 * 60 * 1000, // 2小时
      maxVersions: config.maxVersions || 3,
      ...config
    };

    // 启动清理定时器
    this.startCleanupTimer();
    
    logger.info('Dynamic key manager initialized', {
      keyRotationIntervalMinutes: this.config.keyRotationInterval / (60 * 1000),
      sessionKeyTTLMinutes: this.config.sessionKeyTTL / (60 * 1000),
      maxVersions: this.config.maxVersions
    });
  }

  /**
   * 生成主密钥（如果未提供）
   */
  private generateMasterSecret(): string {
    const secret = crypto.randomBytes(64).toString('hex');
    logger.warn('Generated temporary master secret - should be configured in production', {
      secretLength: secret.length
    });
    return secret;
  }

  /**
   * 获取当前时间窗口版本
   */
  private getCurrentTimeWindow(): string {
    const now = Date.now();
    const windowStart = Math.floor(now / this.config.keyRotationInterval) * this.config.keyRotationInterval;
    return windowStart.toString();
  }

  /**
   * 获取时间窗口密钥
   */
  private getTimeWindowKey(version: string): string {
    let windowKey = this.timeWindowKeys.get(version);
    
    if (!windowKey) {
      // 生成新的时间窗口密钥
      const windowStart = parseInt(version);
      const keyMaterial = `${this.config.masterSecret}:timewindow:${version}`;
      const key = crypto.createHmac('sha256', this.config.masterSecret)
        .update(keyMaterial)
        .digest('hex');
      
      windowKey = {
        key,
        createdAt: windowStart,
        expiresAt: windowStart + this.config.keyRotationInterval + (30 * 60 * 1000) // 加30分钟宽限期
      };
      
      this.timeWindowKeys.set(version, windowKey);
      
      logger.debug('Generated new time window key', {
        version,
        createdAt: new Date(windowKey.createdAt).toISOString(),
        expiresAt: new Date(windowKey.expiresAt).toISOString()
      });
      
      // 清理过期的时间窗口密钥
      this.cleanupExpiredTimeWindowKeys();
    }
    
    return windowKey.key;
  }

  /**
   * 生成会话专属的HMAC密钥
   */
  generateSessionKey(sessionId: string): DynamicKey {
    const now = Date.now();
    const timeWindow = this.getCurrentTimeWindow();
    const timeWindowKey = this.getTimeWindowKey(timeWindow);
    
    // 基于时间窗口密钥 + sessionId 派生会话密钥
    const keyMaterial = `${timeWindowKey}:session:${sessionId}:${timeWindow}`;
    const sessionKey = crypto.createHmac('sha256', timeWindowKey)
      .update(keyMaterial)
      .digest('hex');
    
    const dynamicKey: DynamicKey = {
      key: sessionKey,
      version: timeWindow,
      sessionId,
      createdAt: now,
      expiresAt: now + this.config.sessionKeyTTL
    };
    
    // 缓存会话密钥
    this.sessionKeys.set(sessionId, dynamicKey);
    
    logger.debug('Generated session HMAC key', {
      sessionId: sessionId.slice(0, 8),
      version: timeWindow,
      createdAt: new Date(dynamicKey.createdAt).toISOString(),
      expiresAt: new Date(dynamicKey.expiresAt).toISOString()
    });
    
    return dynamicKey;
  }

  /**
   * 获取会话的HMAC密钥（如果不存在则生成新的）
   */
  getSessionKey(sessionId: string): DynamicKey {
    let sessionKey = this.sessionKeys.get(sessionId);
    
    // 检查是否过期或不存在
    if (!sessionKey || Date.now() > sessionKey.expiresAt) {
      if (sessionKey) {
        logger.debug('Session key expired, generating new one', {
          sessionId: sessionId.slice(0, 8),
          expiredAt: new Date(sessionKey.expiresAt).toISOString()
        });
      }
      sessionKey = this.generateSessionKey(sessionId);
    }
    
    return sessionKey;
  }

  /**
   * 验证HMAC密钥是否有效
   * 支持多版本密钥验证（当前版本 + 前几个版本）
   */
  validateKey(sessionId: string, providedKey: string): boolean {
    const currentKey = this.getSessionKey(sessionId);
    
    // 检查当前密钥
    if (providedKey === currentKey.key) {
      return true;
    }
    
    // 检查前几个版本的密钥（支持密钥轮换期间的宽限）
    const currentWindow = parseInt(currentKey.version);
    const versionsToCheck = Math.min(this.config.maxVersions, 3);
    
    for (let i = 1; i <= versionsToCheck; i++) {
      const previousWindow = (currentWindow - (i * this.config.keyRotationInterval)).toString();
      const previousWindowKey = this.getTimeWindowKey(previousWindow);
      
      // 重新生成该版本的会话密钥进行比较
      const keyMaterial = `${previousWindowKey}:session:${sessionId}:${previousWindow}`;
      const previousSessionKey = crypto.createHmac('sha256', previousWindowKey)
        .update(keyMaterial)
        .digest('hex');
      
      if (providedKey === previousSessionKey) {
        logger.debug('HMAC key validated with previous version', {
          sessionId: sessionId.slice(0, 8),
          usedVersion: previousWindow,
          currentVersion: currentKey.version,
          versionDiff: i
        });
        return true;
      }
    }
    
    logger.warn('HMAC key validation failed', {
      sessionId: sessionId.slice(0, 8),
      currentVersion: currentKey.version,
      checkedVersions: versionsToCheck
    });
    
    return false;
  }

  /**
   * 将HMAC密钥编码到CSRF token中（隐蔽传递）
   */
  embedKeyInToken(sessionId: string, csrfToken: string): string {
    const sessionKey = this.getSessionKey(sessionId);

    // 生成密钥载荷
    const keyData = { k: sessionKey.key, v: sessionKey.version, e: sessionKey.expiresAt };
    const keyDataJson = JSON.stringify(keyData);
    const keyDataEncoded = Buffer.from(keyDataJson).toString('base64url'); // ASCII

    // v2 打包格式：base64url( [0x02][32-byte csrf raw][xor(keyDataEncoded, csrfBytes)] )
    const csrfBytes = Buffer.from(csrfToken, 'hex'); // 32 bytes
    const payloadBytes = Buffer.from(keyDataEncoded, 'utf8');
    const xored = Buffer.alloc(payloadBytes.length);
    for (let i = 0; i < payloadBytes.length; i++) {
      xored[i] = payloadBytes[i] ^ csrfBytes[i % csrfBytes.length];
    }
    const packed = Buffer.concat([Buffer.from([0x02]), csrfBytes, xored]);
    const enhancedTokenV2 = Buffer.from(packed).toString('base64url');

    logger.debug('HMAC key embedded in CSRF token (v2)', {
      sessionId: sessionId.slice(0, 8),
      originalTokenLength: csrfToken.length,
      enhancedTokenLength: enhancedTokenV2.length,
      keyVersion: sessionKey.version
    });

    return enhancedTokenV2;
  }

  /**
   * 从CSRF token中提取HMAC密钥
   */
  extractKeyFromToken(sessionId: string, enhancedToken: string): DynamicKey | null {
    try {
      // 支持两种格式：
      // v1: <hex_csrf>.<base64url_obfuscated>
      // v2: base64url( [0x02][csrfRaw(32)][xor(payload, csrf)] )
      let keyData: any = null;
      let csrfTokenHex: string | null = null;

      if (enhancedToken.includes('.')) {
        // v1
        const parts = enhancedToken.split('.');
        if (parts.length !== 2) return null;
        const [csrfToken, obfuscatedKey] = parts;
        csrfTokenHex = csrfToken;
        const keyDataEncoded = this.xorObfuscate(obfuscatedKey, csrfToken);
        const keyDataJson = Buffer.from(keyDataEncoded, 'base64url').toString('utf8');
        keyData = JSON.parse(keyDataJson);
      } else {
        // v2
        const packed = Buffer.from(enhancedToken, 'base64url');
        if (packed.length < 1 + 32 + 1) return null;
        const version = packed[0];
        if (version !== 0x02) return null;
        const csrfBytes = packed.subarray(1, 33);
        const xored = packed.subarray(33);
        csrfTokenHex = Buffer.from(csrfBytes).toString('hex');
        const unxored = Buffer.alloc(xored.length);
        for (let i = 0; i < xored.length; i++) {
          unxored[i] = xored[i] ^ csrfBytes[i % csrfBytes.length];
        }
        const keyDataEncoded = Buffer.from(unxored).toString('utf8');
        const keyDataJson = Buffer.from(keyDataEncoded, 'base64url').toString('utf8');
        keyData = JSON.parse(keyDataJson);
      }
      
      // 验证数据结构
      if (!keyData.k || !keyData.v || !keyData.e) {
        return null;
      }
      
      // 检查是否过期
      if (Date.now() > keyData.e) {
        logger.debug('Extracted HMAC key expired', {
          sessionId: sessionId.slice(0, 8),
          expiredAt: new Date(keyData.e).toISOString()
        });
        return null;
      }
      
      return {
        key: keyData.k,
        version: keyData.v,
        sessionId,
        createdAt: Date.now(),
        expiresAt: keyData.e
      };
    } catch (error) {
      logger.warn('Failed to extract HMAC key from token', {
        sessionId: sessionId.slice(0, 8),
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 从增强token中提取原始CSRF（用于校验），支持v1/v2
   */
  static extractOriginalCSRFFromEnhanced(enhancedToken: string): string | null {
    try {
      if (enhancedToken.includes('.')) {
        return enhancedToken.split('.')[0];
      }
      const packed = Buffer.from(enhancedToken, 'base64url');
      if (packed.length < 33) return null;
      if (packed[0] !== 0x02) return null;
      return Buffer.from(packed.subarray(1, 33)).toString('hex');
    } catch {
      return null;
    }
  }

  /**
   * XOR混淆工具函数
   */
  private xorObfuscate(data: string, key: string): string {
    const dataBuffer = Buffer.from(data, 'base64url');
    const keyBuffer = Buffer.from(key, 'utf8');
    const resultBuffer = Buffer.alloc(dataBuffer.length);

    for (let i = 0; i < dataBuffer.length; i++) {
      resultBuffer[i] = dataBuffer[i] ^ keyBuffer[i % keyBuffer.length];
    }

    return resultBuffer.toString('base64url');
  }

  /**
   * 清理过期的时间窗口密钥
   */
  private cleanupExpiredTimeWindowKeys(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [version, windowKey] of this.timeWindowKeys.entries()) {
      if (now > windowKey.expiresAt) {
        this.timeWindowKeys.delete(version);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      logger.debug('Cleaned up expired time window keys', {
        cleanedCount,
        remainingCount: this.timeWindowKeys.size
      });
    }
  }

  /**
   * 清理过期的会话密钥
   */
  private cleanupExpiredSessionKeys(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [sessionId, sessionKey] of this.sessionKeys.entries()) {
      if (now > sessionKey.expiresAt) {
        this.sessionKeys.delete(sessionId);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      logger.debug('Cleaned up expired session keys', {
        cleanedCount,
        remainingCount: this.sessionKeys.size
      });
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    const cleanupInterval = Math.min(this.config.keyRotationInterval / 4, 15 * 60 * 1000); // 最多15分钟
    
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredTimeWindowKeys();
      this.cleanupExpiredSessionKeys();
    }, cleanupInterval);
    
    logger.debug('Key cleanup timer started', {
      cleanupIntervalMinutes: cleanupInterval / (60 * 1000)
    });
  }

  /**
   * 停止密钥管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    
    this.sessionKeys.clear();
    this.timeWindowKeys.clear();
    
    logger.info('Dynamic key manager destroyed');
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    activeSessionKeys: number;
    activeTimeWindowKeys: number;
    currentTimeWindow: string;
    nextRotationAt: string;
  } {
    const currentTimeWindow = this.getCurrentTimeWindow();
    const currentWindowStart = parseInt(currentTimeWindow);
    const nextRotationAt = new Date(currentWindowStart + this.config.keyRotationInterval).toISOString();
    
    return {
      activeSessionKeys: this.sessionKeys.size,
      activeTimeWindowKeys: this.timeWindowKeys.size,
      currentTimeWindow,
      nextRotationAt
    };
  }
}

// 单例实例
let dynamicKeyManager: DynamicKeyManager | null = null;

/**
 * 获取动态密钥管理器实例
 */
export const getDynamicKeyManager = (): DynamicKeyManager => {
  if (!dynamicKeyManager) {
    dynamicKeyManager = new DynamicKeyManager();
  }
  return dynamicKeyManager;
};

/**
 * 初始化动态密钥管理器（用于自定义配置）
 */
export const initializeDynamicKeyManager = (config: Partial<KeyDerivationConfig>): DynamicKeyManager => {
  if (dynamicKeyManager) {
    dynamicKeyManager.destroy();
  }
  
  dynamicKeyManager = new DynamicKeyManager(config);
  return dynamicKeyManager;
};

export default DynamicKeyManager;
