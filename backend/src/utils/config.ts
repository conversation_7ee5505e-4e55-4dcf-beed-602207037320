import dotenv from 'dotenv';
import path from 'path';

dotenv.config();

export const config = {
  // Server Configuration
  port: parseInt(process.env.PORT || '3001', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  apiPrefix: process.env.API_PREFIX || '/api',

  // Nacos Configuration
  nacos: {
    serverAddr: process.env.NACOS_SERVER_ADDR || 'localhost:8848',
    namespace: process.env.NACOS_NAMESPACE || 'public',
    username: process.env.NACOS_USERNAME,
    password: process.env.NACOS_PASSWORD,
    accessKey: process.env.NACOS_ACCESS_KEY,
    secretKey: process.env.NACOS_SECRET_KEY,
  },

  // Textin API Configuration (fallback for development)
  textin: {
    appId: process.env.TEXTIN_APP_ID || '',
    secret: process.env.TEXTIN_SECRET || '',
    apiUrl: process.env.TEXTIN_API_URL || 'https://api.textin.com/ai/service/v1/crop_enhance_image',
  },

  // File Storage Configuration
  storage: {
    tempDir: process.env.TEMP_DIR || './temp',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800', 10), // 50MB
    cleanupInterval: parseInt(process.env.CLEANUP_INTERVAL || '3600000', 10), // 1 hour
    maxFilesPerRequest: parseInt(process.env.MAX_FILES_PER_REQUEST || '50', 10),
  },

  // CORS Configuration
  cors: {
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'https://all-pdf-editor.apusai.com',
      'https://image-to-pdf.apusai.com',
      'https://test-image-to-pdf.apuscn.com'
    ],
  },

  // Rate Limiting Configuration
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },

  // PDF Generation Configuration
  pdf: {
    margin: parseInt(process.env.PDF_MARGIN || '0', 10), // 边距 (mm)
    dpi: parseInt(process.env.PDF_DPI || '96', 10), // DPI设置
    quality: parseInt(process.env.PDF_QUALITY || '90', 10), // 图片质量
    defaultPageSize: process.env.PDF_DEFAULT_PAGE_SIZE || 'A4', // 默认页面尺寸
  },

};

// Validate required configuration
export const validateConfig = () => {
  const required = [
    'NACOS_SERVER_ADDR',
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length === 0) {
    // If Nacos is configured, we don't need local Textin API keys
    return;
  }

  // Fallback to local environment variables for development
  const texinRequired = [
    'TEXTIN_APP_ID',
    'TEXTIN_SECRET',
  ];

  const texinMissing = texinRequired.filter(key => !process.env[key]);
  
  if (texinMissing.length > 0) {
    console.warn('⚠️  Neither Nacos nor local Textin API configuration found.');
    console.warn('   Either configure Nacos or set environment variables:');
    console.warn(`   Missing: ${texinMissing.join(', ')}`);
    console.warn('   The application will attempt to use Nacos configuration at runtime.');
  }
};

export default config;