/**
 * 前端HMAC签名工具库
 * 此文件为前端提供HMAC签名生成功能的参考实现
 * 实际使用时应将此逻辑移植到前端项目中
 */

import crypto from 'crypto';

export interface HMACClientConfig {
  secretKey: string;
}

/**
 * HMAC客户端签名工具类
 */
export class HMACClient {
  private secretKey: string;

  constructor(config: HMACClientConfig) {
    this.secretKey = config.secretKey;
  }

  /**
   * 生成随机nonce
   */
  generateNonce(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 参数值标准化为字符串
   */
  private stringify(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  /**
   * 构建签名字符串
   */
  private buildSignString(params: Record<string, any>, timestamp: number, method: string, path: string): string {
    // 1. 获取所有参数
    const allParams = { ...params };
    
    // 2. 添加系统参数
    allParams._timestamp = timestamp;
    allParams._method = method.toUpperCase();
    allParams._path = path;
    
    // 3. 按键名排序
    const sortedKeys = Object.keys(allParams).sort();
    
    // 4. 构建键值对字符串
    const paramString = sortedKeys
      .map(key => `${key}=${this.stringify(allParams[key])}`)
      .join('&');
    
    return paramString;
  }

  /**
   * 生成HMAC签名
   * @param params 请求参数
   * @param method HTTP方法
   * @param path 请求路径
   * @param nonce 随机数
   */
  generateSignature(
    params: Record<string, any>,
    method: string,
    path: string,
    nonce: string
  ): { signature: string; timestamp: number } {
    const timestamp = Math.floor(Date.now() / 1000);
    
    // 添加nonce到参数中
    const paramsWithNonce = {
      ...params,
      _nonce: nonce
    };
    
    // 1. 构建签名字符串
    const signString = this.buildSignString(paramsWithNonce, timestamp, method, path);
    
    // 2. 使用HMAC-SHA256生成签名
    const signature = crypto
      .createHmac('sha256', this.secretKey)
      .update(signString)
      .digest('hex');
    
    return { signature, timestamp };
  }

  /**
   * 为请求添加HMAC认证头
   * @param params 请求参数
   * @param method HTTP方法
   * @param path 请求路径
   * @returns 包含HMAC认证信息的请求头
   */
  addAuthHeaders(
    params: Record<string, any>,
    method: string,
    path: string
  ): Record<string, string> {
    const nonce = this.generateNonce();
    const { signature, timestamp } = this.generateSignature(params, method, path, nonce);
    
    return {
      'x-auth-token': signature,
      'x-timestamp': timestamp.toString(),
      'x-trace-id': nonce
    };
  }
}

// 导出前端可用的工具函数
export const createHMACClient = (secretKey: string) => new HMACClient({ secretKey });

/**
 * 前端使用示例 (JavaScript/TypeScript)
 * 
 * // 1. 创建HMAC客户端
 * const hmacClient = createHMACClient('your-secret-key');
 * 
 * // 2. 准备请求参数
 * const requestParams = {
 *   files: ['image1.jpg', 'image2.jpg'],
 *   options: { autoProcess: true }
 * };
 * 
 * // 3. 生成认证头
 * const authHeaders = hmacClient.addAuthHeaders(requestParams, 'POST', '/api/images/upload');
 * 
 * // 4. 发起请求
 * const response = await fetch('/api/images/upload', {
 *   method: 'POST',
 *   headers: {
 *     'Content-Type': 'application/json',
 *     'x-session-id': sessionId,
 *     'x-csrf-token': csrfToken,
 *     ...authHeaders  // 添加HMAC认证头
 *   },
 *   body: JSON.stringify(requestParams)
 * });
 */

/**
 * Fetch API集成示例
 */
export interface AuthenticatedRequestOptions extends RequestInit {
  hmacSecretKey: string;
  sessionId: string;
  csrfToken: string;
  params?: Record<string, any>;
}

/**
 * 封装的认证请求函数
 */
export async function authenticatedFetch(
  url: string, 
  options: AuthenticatedRequestOptions
): Promise<Response> {
  const { hmacSecretKey, sessionId, csrfToken, params = {}, ...fetchOptions } = options;
  
  // 解析URL路径
  const urlObj = new URL(url, window.location.origin);
  const path = urlObj.pathname;
  const method = (fetchOptions.method || 'GET').toUpperCase();
  
  // 创建HMAC客户端并生成认证头
  const hmacClient = createHMACClient(hmacSecretKey);
  const authHeaders = hmacClient.addAuthHeaders(params, method, path);
  
  // 合并请求头
  const headers = {
    'Content-Type': 'application/json',
    'x-session-id': sessionId,
    'x-csrf-token': csrfToken,
    ...authHeaders,
    ...(fetchOptions.headers as Record<string, string> || {})
  };
  
  return fetch(url, {
    ...fetchOptions,
    method,
    headers
  });
}

/**
 * 使用示例
 * 
 * // 上传图片
 * const response = await authenticatedFetch('/api/images/upload', {
 *   method: 'POST',
 *   hmacSecretKey: 'your-secret-key',
 *   sessionId: 'session-123',
 *   csrfToken: 'csrf-token-456',
 *   params: {
 *     files: ['image1.jpg', 'image2.jpg'],
 *     options: { autoProcess: true }
 *   },
 *   body: formData
 * });
 * 
 * // 生成PDF
 * const pdfResponse = await authenticatedFetch('/api/pdf/generate', {
 *   method: 'POST',
 *   hmacSecretKey: 'your-secret-key',
 *   sessionId: 'session-123',
 *   csrfToken: 'csrf-token-456',
 *   params: {
 *     imageIds: ['img1', 'img2', 'img3'],
 *     options: { pageSize: 'A4', orientation: 'portrait' }
 *   },
 *   body: JSON.stringify({
 *     imageIds: ['img1', 'img2', 'img3'],
 *     options: { pageSize: 'A4', orientation: 'portrait' }
 *   })
 * });
 */