type LogLevel = 'info' | 'warn' | 'error' | 'debug' | 'trace';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  service: string;
  version: string;
  environment: string;
  requestId?: string;
  sessionId?: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
  duration?: number;
  statusCode?: number;
  error?: {
    name?: string;
    message?: string;
    stack?: string;
    code?: string | number;
  };
  metadata?: any;
  performance?: {
    memoryUsage?: NodeJS.MemoryUsage;
    cpuUsage?: NodeJS.CpuUsage;
  };
}

class Logger {
  private service: string = 'image-to-pdf-backend';
  private version: string = process.env.npm_package_version || '1.0.0';
  private environment: string = process.env.NODE_ENV || 'development';
  private logLevel: LogLevel = (process.env.LOG_LEVEL as LogLevel) || 
    (this.environment === 'production' ? 'info' : 'debug');

  /**
   * 检查日志级别是否应该输出
   */
  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4
    };
    
    return levels[level] <= levels[this.logLevel];
  }

  /**
   * 创建结构化日志条目
   */
  private createLogEntry(level: LogLevel, message: string, meta?: any): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      service: this.service,
      version: this.version,
      environment: this.environment
    };

    // 添加元数据
    if (meta) {
      // 提取常用字段到顶级
      if (meta.requestId) entry.requestId = meta.requestId;
      if (meta.sessionId) entry.sessionId = meta.sessionId;
      if (meta.userId) entry.userId = meta.userId;
      if (meta.ip) entry.ip = meta.ip;
      if (meta.userAgent) entry.userAgent = meta.userAgent;
      if (meta.duration) entry.duration = meta.duration;
      if (meta.statusCode) entry.statusCode = meta.statusCode;
      
      // 错误信息处理
      if (meta.error || (level === 'error' && typeof meta === 'object')) {
        const errorObj = meta.error || meta;
        entry.error = {
          name: errorObj.name,
          message: errorObj.message,
          stack: errorObj.stack,
          code: errorObj.code
        };
      }

      // 性能信息
      if (meta.includePerformance) {
        entry.performance = {
          memoryUsage: process.memoryUsage(),
          cpuUsage: process.cpuUsage()
        };
      }

      // 其余信息放入metadata
      const { requestId, sessionId, userId, ip, userAgent, duration, 
              statusCode, error, includePerformance, ...rest } = meta;
      if (Object.keys(rest).length > 0) {
        entry.metadata = rest;
      }
    }

    return entry;
  }

  /**
   * 输出日志
   */
  private output(entry: LogEntry): void {
    const output = this.environment === 'production' 
      ? JSON.stringify(entry)
      : this.formatForDevelopment(entry);

    switch (entry.level) {
      case 'error':
        console.error(output);
        break;
      case 'warn':
        console.warn(output);
        break;
      case 'debug':
      case 'trace':
        console.debug(output);
        break;
      default:
        console.log(output);
    }
  }

  /**
   * 开发环境的友好格式化
   */
  private formatForDevelopment(entry: LogEntry): string {
    const { timestamp, level, message, requestId, sessionId, duration, statusCode, metadata } = entry;
    
    let output = `[${timestamp}] ${level.toUpperCase()}: ${message}`;
    
    if (requestId) output += ` [req:${requestId.slice(0, 8)}]`;
    if (sessionId) output += ` [session:${sessionId.slice(0, 8)}]`;
    if (duration !== undefined) output += ` [${duration}ms]`;
    if (statusCode) output += ` [${statusCode}]`;
    
    if (entry.error) {
      output += ` | Error: ${entry.error.name}: ${entry.error.message}`;
      if (entry.error.stack && this.logLevel === 'debug') {
        output += ` | Stack: ${entry.error.stack.replace(/\n/g, ' → ')}`;
      }
    }
    
    if (metadata && Object.keys(metadata).length > 0) {
      output += ` | Meta: ${JSON.stringify(metadata)}`;
    }
    
    return output;
  }

  info(message: string, meta?: any): void {
    if (this.shouldLog('info')) {
      const entry = this.createLogEntry('info', message, meta);
      this.output(entry);
    }
  }

  warn(message: string, meta?: any): void {
    if (this.shouldLog('warn')) {
      const entry = this.createLogEntry('warn', message, meta);
      this.output(entry);
    }
  }

  error(message: string, error?: any): void {
    if (this.shouldLog('error')) {
      const entry = this.createLogEntry('error', message, error);
      this.output(entry);
    }
  }

  debug(message: string, meta?: any): void {
    if (this.shouldLog('debug')) {
      const entry = this.createLogEntry('debug', message, meta);
      this.output(entry);
    }
  }

  trace(message: string, meta?: any): void {
    if (this.shouldLog('trace')) {
      const entry = this.createLogEntry('trace', message, meta);
      this.output(entry);
    }
  }

  /**
   * 性能日志 - 包含系统性能信息
   */
  performance(message: string, meta?: any): void {
    this.info(message, { ...meta, includePerformance: true });
  }

  /**
   * 审计日志 - 重要操作记录
   */
  audit(message: string, meta?: any): void {
    this.info(`[AUDIT] ${message}`, meta);
  }

  /**
   * 安全日志 - 安全相关事件
   */
  security(message: string, meta?: any): void {
    this.warn(`[SECURITY] ${message}`, meta);
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel): void {
    this.logLevel = level;
    this.info('Log level changed', { newLevel: level });
  }

  /**
   * 获取当前日志级别
   */
  getLevel(): LogLevel {
    return this.logLevel;
  }
}

export const logger = new Logger();
export default logger;