import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { logger } from '../utils/logger';
import config from '../utils/config';
import { PDFGenerationOptions, ProcessedImage } from '../types';

export interface PDFCacheEntry {
  fileName: string;
  filePath: string;
  contentHash: string;
  sessionId: string;
  fileSize: number;
  createdAt: number;
  lastAccessed: number;
}

export class PDFCacheService {
  private static instance: PDFCacheService;
  private cache = new Map<string, PDFCacheEntry>();
  private cacheDir: string;
  private lastCleanupTime: number = 0;
  private cleanupInterval: number = 60 * 60 * 1000; // 1小时
  
  private constructor() {
    this.cacheDir = path.join(config.storage.tempDir, 'pdf-cache');
    this.ensureCacheDirectory();
    this.performInitialCleanup();
  }

  static getInstance(): PDFCacheService {
    if (!PDFCacheService.instance) {
      PDFCacheService.instance = new PDFCacheService();
    }
    return PDFCacheService.instance;
  }

  /**
   * 计算PDF内容的唯一hash
   * 基于图片内容、处理选项和顺序生成
   */
  private calculateContentHash(
    images: ProcessedImage[], 
    options: PDFGenerationOptions
  ): string {
    const hashInput = {
      // 图片内容和顺序
      images: images.map(img => ({
        id: img.id,
        originalName: img.originalName,
        // 使用图片数据的hash而不是完整数据
        dataHash: this.hashImageData(img.original || img.cropped || ''),
        corners: img.corners,
        isLowResolution: img.isLowResolution
      })),
      // PDF生成选项
      options: {
        pageSize: options.pageSize,
        orientation: options.orientation,
        margin: options.margin,
        quality: options.quality
      },
      // 版本标识，确保算法变更时缓存失效
      version: '1.0'
    };

    const hashStr = JSON.stringify(hashInput);
    return crypto.createHash('sha256').update(hashStr).digest('hex');
  }

  /**
   * 计算图片数据的hash
   */
  private hashImageData(imageData: string): string {
    return crypto.createHash('md5').update(imageData).digest('hex').slice(0, 16);
  }

  /**
   * 获取会话的缓存目录
   */
  private getSessionCacheDir(sessionId: string): string {
    return path.join(this.cacheDir, sessionId);
  }

  /**
   * 确保缓存目录存在
   */
  private ensureCacheDirectory(): void {
    try {
      if (!fs.existsSync(this.cacheDir)) {
        fs.mkdirSync(this.cacheDir, { recursive: true });
      }
    } catch (error) {
      logger.error('Failed to create PDF cache directory', { 
        cacheDir: this.cacheDir,
        error 
      });
    }
  }

  /**
   * 确保会话缓存目录存在
   */
  private ensureSessionCacheDir(sessionId: string): void {
    const sessionDir = this.getSessionCacheDir(sessionId);
    try {
      if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true });
      }
    } catch (error) {
      logger.error('Failed to create session cache directory', {
        sessionId,
        sessionDir,
        error
      });
    }
  }

  /**
   * 检查缓存是否存在
   */
  checkCache(
    images: ProcessedImage[],
    options: PDFGenerationOptions,
    sessionId: string
  ): PDFCacheEntry | null {
    const contentHash = this.calculateContentHash(images, options);
    const cacheKey = `${sessionId}:${contentHash}`;
    
    const cached = this.cache.get(cacheKey);
    
    if (!cached) {
      logger.debug('PDF cache miss', { contentHash, sessionId });
      return null;
    }

    // 检查文件是否仍然存在
    if (!fs.existsSync(cached.filePath)) {
      logger.warn('Cached PDF file missing, removing from cache', {
        contentHash,
        sessionId,
        filePath: cached.filePath
      });
      this.cache.delete(cacheKey);
      return null;
    }

    // 更新最后访问时间
    cached.lastAccessed = Date.now();
    this.cache.set(cacheKey, cached);

    logger.info('PDF cache hit', {
      contentHash,
      sessionId,
      fileName: cached.fileName,
      fileSize: cached.fileSize,
      ageMinutes: Math.round((Date.now() - cached.createdAt) / (1000 * 60))
    });

    return cached;
  }

  /**
   * 将PDF文件添加到缓存
   */
  cacheFile(
    images: ProcessedImage[],
    options: PDFGenerationOptions,
    sessionId: string,
    originalFilePath: string,
    fileName: string
  ): PDFCacheEntry | null {
    try {
      const contentHash = this.calculateContentHash(images, options);
      const cacheKey = `${sessionId}:${contentHash}`;
      
      this.ensureSessionCacheDir(sessionId);
      
      // 生成缓存文件路径
      const cachedFileName = `${contentHash}.pdf`;
      const sessionCacheDir = this.getSessionCacheDir(sessionId);
      const cachedFilePath = path.join(sessionCacheDir, cachedFileName);

      // 复制文件到缓存目录
      fs.copyFileSync(originalFilePath, cachedFilePath);
      
      const stat = fs.statSync(cachedFilePath);
      const cacheEntry: PDFCacheEntry = {
        fileName,
        filePath: cachedFilePath,
        contentHash,
        sessionId,
        fileSize: stat.size,
        createdAt: Date.now(),
        lastAccessed: Date.now()
      };

      this.cache.set(cacheKey, cacheEntry);

      logger.info('PDF cached successfully', {
        contentHash,
        sessionId,
        fileName,
        fileSize: stat.size,
        cachedFilePath
      });

      // PDF生成完成后检查是否需要清理
      this.checkAndCleanup();

      return cacheEntry;
    } catch (error) {
      logger.error('Failed to cache PDF file', {
        sessionId,
        fileName,
        originalFilePath,
        error
      });
      return null;
    }
  }

  /**
   * 复制缓存的PDF到临时下载目录
   */
  async serveCachedPDF(cacheEntry: PDFCacheEntry): Promise<{
    filePath: string;
    fileName: string;
    fileSize: number;
  }> {
    try {
      // 生成新的下载文件名
      const downloadFileName = `pdf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.pdf`;
      const downloadPath = path.join(config.storage.tempDir, downloadFileName);

      // 复制缓存文件到下载目录
      fs.copyFileSync(cacheEntry.filePath, downloadPath);

      return {
        filePath: downloadPath,
        fileName: downloadFileName,
        fileSize: cacheEntry.fileSize
      };
    } catch (error) {
      logger.error('Failed to serve cached PDF', {
        cacheEntry,
        error
      });
      throw error;
    }
  }

  /**
   * 清理过期缓存
   * 保留7天内的文件，清理超过7天的缓存
   */
  cleanup(): void {
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
    const now = Date.now();
    
    let cleanedCount = 0;
    let cleanedSize = 0;

    for (const [key, entry] of this.cache.entries()) {
      const age = now - entry.createdAt;
      
      if (age > maxAge) {
        try {
          if (fs.existsSync(entry.filePath)) {
            const stat = fs.statSync(entry.filePath);
            fs.unlinkSync(entry.filePath);
            cleanedSize += stat.size;
          }
          this.cache.delete(key);
          cleanedCount++;
        } catch (error) {
          logger.error('Failed to cleanup cached PDF', {
            key,
            filePath: entry.filePath,
            error
          });
        }
      }
    }

    // 清理空的会话目录
    this.cleanupEmptySessionDirs();

    // 更新最后清理时间
    this.lastCleanupTime = now;

    if (cleanedCount > 0) {
      logger.info('PDF cache cleanup completed', {
        cleanedFiles: cleanedCount,
        cleanedSizeMB: Math.round(cleanedSize / (1024 * 1024) * 100) / 100,
        remainingFiles: this.cache.size,
        nextCleanupDue: new Date(now + this.cleanupInterval).toISOString()
      });
    }
  }

  /**
   * 清理空的会话目录
   */
  private cleanupEmptySessionDirs(): void {
    try {
      if (!fs.existsSync(this.cacheDir)) {
        return;
      }

      const sessionDirs = fs.readdirSync(this.cacheDir, { withFileTypes: true });
      
      for (const dirent of sessionDirs) {
        if (dirent.isDirectory()) {
          const sessionDir = path.join(this.cacheDir, dirent.name);
          const files = fs.readdirSync(sessionDir);
          
          if (files.length === 0) {
            fs.rmdirSync(sessionDir);
            logger.debug('Removed empty session cache directory', {
              sessionId: dirent.name,
              sessionDir
            });
          }
        }
      }
    } catch (error) {
      logger.error('Failed to cleanup empty session directories', { error });
    }
  }

  /**
   * 执行初始清理
   */
  private performInitialCleanup(): void {
    // 启动时延迟5秒执行一次清理
    setTimeout(() => {
      this.cleanup();
    }, 5000);

    logger.info('PDF cache cleanup initialized', {
      cleanupIntervalMinutes: this.cleanupInterval / (1000 * 60),
      maxAgeDays: 7,
      cleanupStrategy: 'on-demand after PDF generation'
    });
  }

  /**
   * 检查是否需要清理，如果需要则执行清理
   */
  private checkAndCleanup(): void {
    const now = Date.now();
    const timeSinceLastCleanup = now - this.lastCleanupTime;
    
    if (timeSinceLastCleanup >= this.cleanupInterval) {
      logger.debug('Triggering cleanup after PDF generation', {
        timeSinceLastCleanupMinutes: Math.round(timeSinceLastCleanup / (1000 * 60)),
        cleanupIntervalMinutes: this.cleanupInterval / (1000 * 60)
      });
      this.cleanup();
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    totalCached: number;
    totalSizeMB: number;
    oldestCacheAge: number;
    newestCacheAge: number;
  } {
    const now = Date.now();
    let totalSize = 0;
    let oldestAge = 0;
    let newestAge = 0;

    for (const entry of this.cache.values()) {
      const age = now - entry.createdAt;
      totalSize += entry.fileSize;
      
      if (oldestAge === 0 || age > oldestAge) {
        oldestAge = age;
      }
      if (newestAge === 0 || age < newestAge) {
        newestAge = age;
      }
    }

    return {
      totalCached: this.cache.size,
      totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
      oldestCacheAge: Math.round(oldestAge / (1000 * 60)), // 分钟
      newestCacheAge: Math.round(newestAge / (1000 * 60))   // 分钟
    };
  }

  /**
   * 清空指定会话的所有缓存
   */
  clearSessionCache(sessionId: string): void {
    const keysToDelete: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.sessionId === sessionId) {
        try {
          if (fs.existsSync(entry.filePath)) {
            fs.unlinkSync(entry.filePath);
          }
          keysToDelete.push(key);
        } catch (error) {
          logger.error('Failed to delete cached file', {
            sessionId,
            filePath: entry.filePath,
            error
          });
        }
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));

    // 删除会话目录
    const sessionDir = this.getSessionCacheDir(sessionId);
    try {
      if (fs.existsSync(sessionDir)) {
        fs.rmSync(sessionDir, { recursive: true, force: true });
      }
    } catch (error) {
      logger.error('Failed to remove session cache directory', {
        sessionId,
        sessionDir,
        error
      });
    }

    logger.info('Session cache cleared', {
      sessionId,
      deletedFiles: keysToDelete.length
    });
  }
}