import { NacosConfigClient } from 'nacos';
import { logger } from '../utils/logger';
import * as yaml from 'js-yaml';

export interface NacosConfig {
  serverAddr: string;
  namespace?: string;
  username?: string;
  password?: string;
  accessKey?: string;
  secretKey?: string;
}

export interface TextinAPIConfig {
  appId: string;
  secret: string;
  apiUrl?: string;
}

export interface AppDownloadConfig {
  android?: {
    url?: string;
  };
  ios?: {
    url?: string;
  };
}

export interface SecurityConfig {
  allowedOrigins: string[];
  corsEnabled?: boolean;
  csrfEnabled?: boolean;
  hmacAuth?: {
    enabled: boolean;
    secretKey: string;
    timestampTolerance?: number; // 时间戳容忍度(秒)
    requiredParams?: string[];   // 必须参与签名的参数
  };
  mobileClientAuth?: {
    secretKey: string;
  };
  rateLimiting?: {
    // 全局通用限制
    general?: {
      enabled: boolean;
      windowMs: number;    // 时间窗口(毫秒)
      maxRequests: number; // 最大请求数
    };
    
    // 文件上传专用限制
    fileUpload?: {
      enabled: boolean;
      windowMs: number;
      maxRequests: number;
      maxFileSize?: number;     // 单文件最大大小(字节)
      maxTotalSize?: number;    // 总上传大小限制(字节)
      maxFilesPerRequest?: number; // 单次请求最大文件数
    };
    
    // 图像处理专用限制  
    imageProcessing?: {
      enabled: boolean;
      windowMs: number;
      maxRequests: number;
      maxImagesPerBatch?: number; // 批量处理最大图片数
      maxConcurrentJobs?: number; // 最大并发处理任务数
    };
    
    // PDF生成专用限制
    pdfGeneration?: {
      enabled: boolean;
      windowMs: number;
      maxRequests: number;
      maxPagesPerPdf?: number;   // 单个PDF最大页数
      maxConcurrentGeneration?: number; // 最大并发生成数
    };
    
    // API调用(TextIn等外部服务)限制
    externalApiCalls?: {
      enabled: boolean;
      windowMs: number;
      maxRequests: number;
      retryAttempts?: number;    // 失败重试次数
      retryDelay?: number;       // 重试延迟(毫秒)
    };
    
    // 按IP的严格限制(防止滥用)
    perIpStrict?: {
      enabled: boolean;
      windowMs: number;
      maxRequests: number;
      blockDuration?: number;    // 超限后阻断时长(毫秒)
    };
  };
}

export class NacosService {
  private client: NacosConfigClient;
  private config: NacosConfig;
  private textinConfig: TextinAPIConfig | null = null;
  private securityConfig: SecurityConfig | null = null;
  private appDownloadConfig: AppDownloadConfig | null = null;
  private isConnected = false;

  constructor(config: NacosConfig) {
    this.config = config;
    this.client = new NacosConfigClient({
      serverAddr: config.serverAddr,
      namespace: config.namespace || 'public',
      ...(config.username && config.password && {
        username: config.username,
        password: config.password,
      }),
      ...(config.accessKey && config.secretKey && {
        accessKey: config.accessKey,
        secretKey: config.secretKey,
      }),
    });
  }

  /**
   * Initialize connection to Nacos and load configurations
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Nacos connection...', {
        serverAddr: this.config.serverAddr,
        namespace: this.config.namespace,
      });

      // Test connection by getting configs
      await this.loadTextinConfig();
      await this.loadSecurityConfig();
      
      // Load app download config (non-critical, don't fail initialization)
      try {
        await this.loadAppDownloadConfig();
      } catch (error) {
        logger.warn('Failed to load app download config during initialization', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
      
      // Set up config listeners for real-time updates
      this.setupConfigListeners();
      
      this.isConnected = true;
      logger.info('Nacos service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Nacos service', error);
      throw new Error(`Nacos initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Load Textin API configuration from Nacos
   */
  async loadTextinConfig(): Promise<TextinAPIConfig> {
    try {
      const dataId = 'textin-api.json';
      const group = 'image-to-pdf';

      logger.debug('Loading Textin API config from Nacos', { dataId, group });

      const configContent = await this.client.getConfig(dataId, group);
      
      if (!configContent) {
        throw new Error(`Config not found: ${dataId} in group ${group}`);
      }

      const config = JSON.parse(configContent) as TextinAPIConfig;
      
      // Validate required fields
      if (!config.appId || !config.secret) {
        throw new Error('Invalid Textin API config: appId and secret are required');
      }

      this.textinConfig = {
        ...config,
        apiUrl: config.apiUrl || 'https://api.textin.com/ai/service/v1/crop_enhance_image',
      };

      logger.info('Textin API config loaded successfully', {
        hasAppId: !!this.textinConfig.appId,
        hasSecret: !!this.textinConfig.secret,
        apiUrl: this.textinConfig.apiUrl,
      });

      return this.textinConfig;
    } catch (error) {
      logger.error('Failed to load Textin API config from Nacos', error);
      throw new Error(`Failed to load Textin config: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Load security configuration from Nacos
   */
  async loadSecurityConfig(): Promise<SecurityConfig> {
    try {
      // 支持YAML和JSON格式，优先使用YAML
      const configs = [
        { dataId: 'security.yaml', format: 'yaml' },
        { dataId: 'security.json', format: 'json' }
      ];
      
      let configContent = '';
      let usedConfig = { dataId: '', format: '' };
      
      // 尝试加载配置，优先YAML
      for (const config of configs) {
        try {
          logger.debug(`Attempting to load config: ${config.dataId}`, { dataId: config.dataId, group: 'image-to-pdf' });
          configContent = await this.client.getConfig(config.dataId, 'image-to-pdf');
          if (configContent) {
            usedConfig = config;
            logger.debug(`Successfully retrieved config content`, { 
              dataId: config.dataId, 
              contentLength: configContent.length,
              contentPreview: configContent.substring(0, 200) + (configContent.length > 200 ? '...' : '')
            });
            break;
          }
        } catch (error) {
          logger.debug(`Failed to load ${config.dataId}, trying next format`, { 
            dataId: config.dataId,
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined
          });
        }
      }

      logger.debug('Loading security config from Nacos', { 
        dataId: usedConfig.dataId, 
        format: usedConfig.format,
        group: 'image-to-pdf' 
      });

      if (!configContent) {
        logger.warn(`Security config not found in Nacos. Tried: security.yaml, security.json. Using defaults.`);
        // 使用默认安全配置
        this.securityConfig = {
          allowedOrigins: [],
          corsEnabled: true,
          csrfEnabled: true,
          rateLimiting: {
            general: { enabled: true, windowMs: 15 * 60 * 1000, maxRequests: 100 },
            fileUpload: { enabled: true, windowMs: 60 * 1000, maxRequests: 20, maxFileSize: 50 * 1024 * 1024, maxTotalSize: 200 * 1024 * 1024, maxFilesPerRequest: 50 },
            imageProcessing: { enabled: true, windowMs: 60 * 1000, maxRequests: 30, maxImagesPerBatch: 100, maxConcurrentJobs: 5 },
            pdfGeneration: { enabled: true, windowMs: 5 * 60 * 1000, maxRequests: 10, maxPagesPerPdf: 200, maxConcurrentGeneration: 3 },
            externalApiCalls: { enabled: true, windowMs: 60 * 1000, maxRequests: 50, retryAttempts: 3, retryDelay: 1000 },
            perIpStrict: { enabled: false, windowMs: 60 * 1000, maxRequests: 200, blockDuration: 5 * 60 * 1000 }
          }
        };
        return this.securityConfig;
      }

      // 根据格式解析配置
      let config: SecurityConfig;
      try {
        if (usedConfig.format === 'yaml') {
          logger.debug('Attempting to parse config as YAML', { 
            dataId: usedConfig.dataId,
            contentLength: configContent.length 
          });
          config = yaml.load(configContent) as SecurityConfig;
          logger.debug('Successfully parsed security config as YAML', { 
            dataId: usedConfig.dataId,
            allowedOriginsCount: config.allowedOrigins?.length || 0,
            corsEnabled: config.corsEnabled,
            csrfEnabled: config.csrfEnabled,
            hasRateLimiting: !!config.rateLimiting,
            hasHmacAuth: !!config.hmacAuth
          });
        } else {
          logger.debug('Attempting to parse config as JSON', { 
            dataId: usedConfig.dataId,
            contentLength: configContent.length 
          });
          config = JSON.parse(configContent) as SecurityConfig;
          logger.debug('Successfully parsed security config as JSON', { 
            dataId: usedConfig.dataId,
            allowedOriginsCount: config.allowedOrigins?.length || 0,
            corsEnabled: config.corsEnabled,
            csrfEnabled: config.csrfEnabled,
            hasRateLimiting: !!config.rateLimiting,
            hasHmacAuth: !!config.hmacAuth
          });
        }
      } catch (parseError) {
        logger.error('Failed to parse security config', {
          dataId: usedConfig.dataId,
          format: usedConfig.format,
          error: parseError instanceof Error ? parseError.message : String(parseError),
          errorStack: parseError instanceof Error ? parseError.stack : undefined,
          contentPreview: configContent.substring(0, 500)
        });
        throw parseError;
      }
      
      // 验证并设置默认值
      this.securityConfig = {
        allowedOrigins: config.allowedOrigins || [],
        corsEnabled: config.corsEnabled !== false, // 默认启用
        csrfEnabled: config.csrfEnabled !== false,  // 默认启用
        hmacAuth: config.hmacAuth,
        mobileClientAuth: config.mobileClientAuth,
        rateLimiting: {
          general: config.rateLimiting?.general || { enabled: true, windowMs: 15 * 60 * 1000, maxRequests: 100 },
          fileUpload: config.rateLimiting?.fileUpload || { enabled: true, windowMs: 60 * 1000, maxRequests: 20, maxFileSize: 50 * 1024 * 1024, maxTotalSize: 200 * 1024 * 1024, maxFilesPerRequest: 50 },
          imageProcessing: config.rateLimiting?.imageProcessing || { enabled: true, windowMs: 60 * 1000, maxRequests: 30, maxImagesPerBatch: 100, maxConcurrentJobs: 5 },
          pdfGeneration: config.rateLimiting?.pdfGeneration || { enabled: true, windowMs: 5 * 60 * 1000, maxRequests: 10, maxPagesPerPdf: 200, maxConcurrentGeneration: 3 },
          externalApiCalls: config.rateLimiting?.externalApiCalls || { enabled: true, windowMs: 60 * 1000, maxRequests: 50, retryAttempts: 3, retryDelay: 1000 },
          perIpStrict: config.rateLimiting?.perIpStrict || { enabled: false, windowMs: 60 * 1000, maxRequests: 200, blockDuration: 5 * 60 * 1000 }
        }
      };

      logger.info('Security config loaded successfully', {
        allowedOriginsCount: this.securityConfig.allowedOrigins.length,
        corsEnabled: this.securityConfig.corsEnabled,
        csrfEnabled: this.securityConfig.csrfEnabled,
        rateLimitingLevels: {
          general: this.securityConfig.rateLimiting?.general?.enabled,
          fileUpload: this.securityConfig.rateLimiting?.fileUpload?.enabled,
          imageProcessing: this.securityConfig.rateLimiting?.imageProcessing?.enabled,
          pdfGeneration: this.securityConfig.rateLimiting?.pdfGeneration?.enabled,
          externalApiCalls: this.securityConfig.rateLimiting?.externalApiCalls?.enabled,
          perIpStrict: this.securityConfig.rateLimiting?.perIpStrict?.enabled
        }
      });

      return this.securityConfig;
    } catch (error) {
      logger.error('Failed to load security config from Nacos', error);
      // 出错时使用默认配置，不抛出错误
      this.securityConfig = {
        allowedOrigins: [],
        corsEnabled: true,
        csrfEnabled: true,
        rateLimiting: {
          general: { enabled: true, windowMs: 15 * 60 * 1000, maxRequests: 100 },
          fileUpload: { enabled: true, windowMs: 60 * 1000, maxRequests: 20, maxFileSize: 50 * 1024 * 1024, maxTotalSize: 200 * 1024 * 1024, maxFilesPerRequest: 50 },
          imageProcessing: { enabled: true, windowMs: 60 * 1000, maxRequests: 30, maxImagesPerBatch: 100, maxConcurrentJobs: 5 },
          pdfGeneration: { enabled: true, windowMs: 5 * 60 * 1000, maxRequests: 10, maxPagesPerPdf: 200, maxConcurrentGeneration: 3 },
          externalApiCalls: { enabled: true, windowMs: 60 * 1000, maxRequests: 50, retryAttempts: 3, retryDelay: 1000 },
          perIpStrict: { enabled: false, windowMs: 60 * 1000, maxRequests: 200, blockDuration: 5 * 60 * 1000 }
        }
      };
      return this.securityConfig;
    }
  }

  /**
   * Set up real-time config listeners
   */
  private setupConfigListeners(): void {
    // Textin API配置监听器
    const textinDataId = 'textin-api.json';
    const group = 'image-to-pdf';

    this.client.subscribe({
      dataId: textinDataId,
      group,
    }, (content: string) => {
      try {
        logger.info('Received Textin API config update from Nacos', { dataId: textinDataId, group });
        
        const newConfig = JSON.parse(content) as TextinAPIConfig;
        
        // Validate new config
        if (!newConfig.appId || !newConfig.secret) {
          logger.error('Invalid updated Textin config: appId and secret are required');
          return;
        }

        this.textinConfig = {
          ...newConfig,
          apiUrl: newConfig.apiUrl || 'https://api.textin.com/ai/service/v1/crop_enhance_image',
        };

        logger.info('Textin API config updated successfully', {
          hasAppId: !!this.textinConfig.appId,
          hasSecret: !!this.textinConfig.secret,
          apiUrl: this.textinConfig.apiUrl,
        });
      } catch (error) {
        logger.error('Failed to process Textin config update', error);
      }
    });

    // 安全配置监听器 - 支持YAML和JSON
    const securityConfigs = ['security.yaml', 'security.json'];
    
    securityConfigs.forEach(dataId => {
      this.client.subscribe({
        dataId,
        group,
      }, (content: string) => {
        try {
          logger.info('Received security config update from Nacos', { dataId, group });
          
          // 检查内容是否为空
          if (!content || content.trim() === '') {
            logger.warn('Received empty security config update, skipping', { dataId });
            return;
          }
          
          // 根据文件扩展名判断格式
          let newConfig: SecurityConfig;
          if (dataId.endsWith('.yaml') || dataId.endsWith('.yml')) {
            newConfig = yaml.load(content) as SecurityConfig;
            logger.debug('Parsed updated security config as YAML', { dataId });
          } else {
            newConfig = JSON.parse(content) as SecurityConfig;
            logger.debug('Parsed updated security config as JSON', { dataId });
          }
        
        // 更新安全配置
        this.securityConfig = {
          allowedOrigins: newConfig.allowedOrigins || [],
          corsEnabled: newConfig.corsEnabled !== false,
          csrfEnabled: newConfig.csrfEnabled !== false,
          hmacAuth: newConfig.hmacAuth,
          mobileClientAuth: newConfig.mobileClientAuth,
          rateLimiting: {
            general: newConfig.rateLimiting?.general || { enabled: true, windowMs: 15 * 60 * 1000, maxRequests: 100 },
            fileUpload: newConfig.rateLimiting?.fileUpload || { enabled: true, windowMs: 60 * 1000, maxRequests: 20, maxFileSize: 50 * 1024 * 1024, maxTotalSize: 200 * 1024 * 1024, maxFilesPerRequest: 50 },
            imageProcessing: newConfig.rateLimiting?.imageProcessing || { enabled: true, windowMs: 60 * 1000, maxRequests: 30, maxImagesPerBatch: 100, maxConcurrentJobs: 5 },
            pdfGeneration: newConfig.rateLimiting?.pdfGeneration || { enabled: true, windowMs: 5 * 60 * 1000, maxRequests: 10, maxPagesPerPdf: 200, maxConcurrentGeneration: 3 },
            externalApiCalls: newConfig.rateLimiting?.externalApiCalls || { enabled: true, windowMs: 60 * 1000, maxRequests: 50, retryAttempts: 3, retryDelay: 1000 },
            perIpStrict: newConfig.rateLimiting?.perIpStrict || { enabled: false, windowMs: 60 * 1000, maxRequests: 200, blockDuration: 5 * 60 * 1000 }
          }
        };

        logger.audit('Security config updated from Nacos', {
          allowedOriginsCount: this.securityConfig.allowedOrigins.length,
          allowedOrigins: this.securityConfig.allowedOrigins,
          corsEnabled: this.securityConfig.corsEnabled,
          csrfEnabled: this.securityConfig.csrfEnabled,
          rateLimitingLevels: {
            general: this.securityConfig.rateLimiting?.general?.enabled,
            fileUpload: this.securityConfig.rateLimiting?.fileUpload?.enabled,
            imageProcessing: this.securityConfig.rateLimiting?.imageProcessing?.enabled,
            pdfGeneration: this.securityConfig.rateLimiting?.pdfGeneration?.enabled,
            externalApiCalls: this.securityConfig.rateLimiting?.externalApiCalls?.enabled,
            perIpStrict: this.securityConfig.rateLimiting?.perIpStrict?.enabled
          }
        });
        } catch (error) {
          logger.error('Failed to process security config update', { error, dataId });
        }
      });
    });

    logger.info('Config listeners set up for real-time updates', { 
      textinDataId, 
      securityDataIds: securityConfigs, 
      group 
    });
  }

  /**
   * Get current Textin API configuration
   */
  getTextinConfig(): TextinAPIConfig {
    if (!this.textinConfig) {
      throw new Error('Textin API config not loaded. Call loadTextinConfig() first.');
    }
    return this.textinConfig;
  }

  /**
   * Get current security configuration
   */
  getSecurityConfig(): SecurityConfig {
    if (!this.securityConfig) {
      throw new Error('Security config not loaded. Call loadSecurityConfig() first.');
    }
    return this.securityConfig;
  }

  /**
   * Load app download configuration from Nacos
   */
  async loadAppDownloadConfig(): Promise<AppDownloadConfig> {
    try {
      const dataId = 'app-downloads.yaml';
      const group = 'image-to-pdf';
      
      logger.info('Loading app download config from Nacos', { dataId, group });
      
      const content = await this.client.getConfig(dataId, group);
      
      logger.info('Raw app-downloads.yaml content:', { content });

      if (!content) {
        logger.info('No app download config found in Nacos, using empty config');
        this.appDownloadConfig = {};
        return this.appDownloadConfig;
      }

      this.appDownloadConfig = yaml.load(content) as AppDownloadConfig;
      
      // Validate URLs if present
      if (this.appDownloadConfig.android?.url) {
        try {
          new URL(this.appDownloadConfig.android.url);
        } catch (error) {
          logger.warn('Invalid Android download URL, ignoring', { 
            url: this.appDownloadConfig.android.url,
            error: error instanceof Error ? error.message : String(error)
          });
          delete this.appDownloadConfig.android.url;
        }
      }
      
      if (this.appDownloadConfig.ios?.url) {
        try {
          new URL(this.appDownloadConfig.ios.url);
        } catch (error) {
          logger.warn('Invalid iOS download URL, ignoring', { 
            url: this.appDownloadConfig.ios.url,
            error: error instanceof Error ? error.message : String(error)
          });
          delete this.appDownloadConfig.ios.url;
        }
      }
      
      logger.info('App download config loaded successfully', { 
        android: !!this.appDownloadConfig.android?.url,
        ios: !!this.appDownloadConfig.ios?.url
      });
      
      // Set up listener for real-time updates (non-blocking)
      try {
        await this.client.subscribe(
          {
            dataId,
            group,
          },
          (content: string) => {
            try {
              const newConfig = yaml.load(content) as AppDownloadConfig;

              // Validate URLs
              if (newConfig.android?.url) {
                try {
                  new URL(newConfig.android.url);
                } catch {
                  delete newConfig.android?.url;
                }
              }

              if (newConfig.ios?.url) {
                try {
                  new URL(newConfig.ios.url);
                } catch {
                  delete newConfig.ios?.url;
                }
              }

              this.appDownloadConfig = newConfig;
              logger.info('App download config updated in real-time', {
                android: !!newConfig.android?.url,
                ios: !!newConfig.ios?.url,
              });
            } catch (error) {
              logger.error('Failed to parse updated app download config', {
                error: error instanceof Error ? error.message : String(error),
              });
            }
          }
        );
        logger.info('App download config listener set up successfully');
      } catch (listenerError) {
        // Don't fail the whole method if listener setup fails
        logger.warn('Failed to set up app download config listener, config will not auto-update', {
          error: listenerError instanceof Error ? listenerError.message : String(listenerError)
        });
      }
      
      return this.appDownloadConfig;
    } catch (error) {
      logger.warn('Failed to load app download config from Nacos, using empty config', { 
        error: error instanceof Error ? error.message : String(error)
      });
      this.appDownloadConfig = {};
      return this.appDownloadConfig;
    }
  }

  /**
   * Get current app download configuration
   */
  getAppDownloadConfig(): AppDownloadConfig {
    if (!this.appDownloadConfig) {
      // Return empty config if not loaded
      return {};
    }
    return this.appDownloadConfig;
  }

  /**
   * Refresh configurations manually
   */
  async refreshConfig(): Promise<{ textinConfig: TextinAPIConfig; securityConfig: SecurityConfig }> {
    logger.info('Manually refreshing configurations');
    const textinConfig = await this.loadTextinConfig();
    const securityConfig = await this.loadSecurityConfig();
    return { textinConfig, securityConfig };
  }

  /**
   * Refresh only Textin API config
   */
  async refreshTextinConfig(): Promise<TextinAPIConfig> {
    logger.info('Manually refreshing Textin API config');
    return await this.loadTextinConfig();
  }

  /**
   * Refresh only security config
   */
  async refreshSecurityConfig(): Promise<SecurityConfig> {
    logger.info('Manually refreshing security config');
    return await this.loadSecurityConfig();
  }

  /**
   * Check if Nacos service is connected and ready
   */
  isReady(): boolean {
    return this.isConnected && this.textinConfig !== null && this.securityConfig !== null;
  }

  /**
   * Get connection status
   */
  getStatus(): {
    connected: boolean;
    textinConfigLoaded: boolean;
    securityConfigLoaded: boolean;
    serverAddr: string;
    namespace: string;
  } {
    return {
      connected: this.isConnected,
      textinConfigLoaded: this.textinConfig !== null,
      securityConfigLoaded: this.securityConfig !== null,
      serverAddr: this.config.serverAddr,
      namespace: this.config.namespace || 'public',
    };
  }

  /**
   * Close connection to Nacos
   */
  async close(): Promise<void> {
    try {
      // Nacos client doesn't have explicit close method in current version
      // This is for future compatibility
      this.isConnected = false;
      this.textinConfig = null;
      this.securityConfig = null;
      logger.info('Nacos service connection closed');
    } catch (error) {
      logger.error('Error closing Nacos connection', error);
    }
  }
}

// Singleton instance
let nacosService: NacosService | null = null;

/**
 * Initialize global Nacos service instance
 */
export const initializeNacosService = async (config: NacosConfig): Promise<NacosService> => {
  if (nacosService) {
    await nacosService.close();
  }

  nacosService = new NacosService(config);
  await nacosService.initialize();
  
  return nacosService;
};

/**
 * Get global Nacos service instance
 */
export const getNacosService = (): NacosService => {
  if (!nacosService) {
    throw new Error('Nacos service not initialized. Call initializeNacosService() first.');
  }
  return nacosService;
};

export default NacosService;