import os from 'os';
import { logger } from '../utils/logger';
import { ImageService } from './imageService';
import { PDFCacheService } from './pdfCacheService';
import { textinService } from './textinService';
import { getNacosService } from './nacosService';

export interface SystemHealthStatus {
  status: 'healthy' | 'warning' | 'critical';
  timestamp: string;
  uptime: number;
  system: {
    memory: {
      used: number;
      available: number;
      usagePercent: number;
      status: 'normal' | 'warning' | 'critical';
    };
    cpu: {
      usage: NodeJS.CpuUsage;
      loadAverage: number[];
      status: 'normal' | 'warning' | 'critical';
    };
    disk: {
      temp: {
        available: boolean;
        status: 'normal' | 'error';
      };
    };
  };
  services: {
    imageService: {
      status: 'healthy' | 'error';
      totalImages: number;
      memoryUsage: string;
    };
    pdfCache: {
      status: 'healthy' | 'error';
      stats: any;
    };
    textinApi: {
      status: 'healthy' | 'unconfigured' | 'error';
      configured: boolean;
    };
    nacos: {
      status: 'healthy' | 'unavailable' | 'error';
      connected: boolean;
      textinConfigLoaded: boolean;
      securityConfigLoaded: boolean;
    };
  };
  performance: {
    avgResponseTime: number;
    requestCount: number;
    errorRate: number;
  };
  security?: {
    dynamicKeys: {
      activeSessionKeys: number;
      activeTimeWindowKeys: number;
      currentTimeWindow: string;
      nextRotationAt: string;
    } | null;
    error?: string;
  };
}

export class HealthService {
  private static instance: HealthService;
  private performanceMetrics = {
    requests: 0,
    errors: 0,
    totalResponseTime: 0,
    lastResetTime: Date.now()
  };

  private constructor() {
    // 每小时重置性能统计
    setInterval(() => {
      this.resetPerformanceMetrics();
    }, 60 * 60 * 1000);
  }

  static getInstance(): HealthService {
    if (!HealthService.instance) {
      HealthService.instance = new HealthService();
    }
    return HealthService.instance;
  }

  /**
   * 记录请求性能指标
   */
  recordRequest(duration: number, isError: boolean = false): void {
    this.performanceMetrics.requests++;
    this.performanceMetrics.totalResponseTime += duration;
    if (isError) {
      this.performanceMetrics.errors++;
    }
  }

  /**
   * 重置性能统计
   */
  private resetPerformanceMetrics(): void {
    logger.debug('Resetting performance metrics', {
      previousStats: { ...this.performanceMetrics }
    });

    this.performanceMetrics = {
      requests: 0,
      errors: 0,
      totalResponseTime: 0,
      lastResetTime: Date.now()
    };
  }

  /**
   * 获取系统内存状态
   */
  private getMemoryStatus(): SystemHealthStatus['system']['memory'] {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const usagePercent = (usedMemory / totalMemory) * 100;

    let status: 'normal' | 'warning' | 'critical' = 'normal';
    if (usagePercent > 90) {
      status = 'critical';
    } else if (usagePercent > 75) {
      status = 'warning';
    }

    return {
      used: usedMemory,
      available: freeMemory,
      usagePercent: Math.round(usagePercent * 100) / 100,
      status
    };
  }

  /**
   * 获取CPU状态
   */
  private getCpuStatus(): SystemHealthStatus['system']['cpu'] {
    const cpuUsage = process.cpuUsage();
    const loadAvg = os.loadavg();
    const avgLoad = loadAvg[0]; // 1分钟平均负载

    let status: 'normal' | 'warning' | 'critical' = 'normal';
    const cpuCount = os.cpus().length;
    
    if (avgLoad > cpuCount * 2) {
      status = 'critical';
    } else if (avgLoad > cpuCount * 1.5) {
      status = 'warning';
    }

    return {
      usage: cpuUsage,
      loadAverage: loadAvg,
      status
    };
  }

  /**
   * 检查磁盘状态
   */
  private getDiskStatus(): SystemHealthStatus['system']['disk'] {
    // 简单检查temp目录是否可用
    const fs = require('fs');
    let tempAvailable = true;
    
    try {
      const tempDir = process.env.TEMP_DIR || '/tmp';
      fs.accessSync(tempDir, fs.constants.W_OK);
    } catch (error) {
      tempAvailable = false;
      logger.error('Temp directory not accessible', { error });
    }

    return {
      temp: {
        available: tempAvailable,
        status: tempAvailable ? 'normal' : 'error'
      }
    };
  }

  /**
   * 检查ImageService状态
   */
  private getImageServiceStatus(): SystemHealthStatus['services']['imageService'] {
    try {
      const imageService = ImageService.getInstance();
      const stats = imageService.getStats();
      
      return {
        status: 'healthy',
        totalImages: stats.totalImages,
        memoryUsage: `${stats.totalImages} images stored`
      };
    } catch (error) {
      logger.error('ImageService health check failed', { error });
      return {
        status: 'error',
        totalImages: 0,
        memoryUsage: 'unavailable'
      };
    }
  }

  /**
   * 检查PDF缓存状态
   */
  private getPdfCacheStatus(): SystemHealthStatus['services']['pdfCache'] {
    try {
      const cacheService = PDFCacheService.getInstance();
      const stats = cacheService.getStats();
      
      return {
        status: 'healthy',
        stats
      };
    } catch (error) {
      logger.error('PDF Cache health check failed', { error });
      return {
        status: 'error',
        stats: null
      };
    }
  }

  /**
   * 检查TextIn API状态
   */
  private getTextinApiStatus(): SystemHealthStatus['services']['textinApi'] {
    try {
      const configured = textinService.validateConfiguration();
      
      return {
        status: configured ? 'healthy' : 'unconfigured',
        configured
      };
    } catch (error) {
      logger.error('TextIn API health check failed', { error });
      return {
        status: 'error',
        configured: false
      };
    }
  }

  /**
   * 检查Nacos状态
   */
  private getNacosStatus(): SystemHealthStatus['services']['nacos'] {
    try {
      const nacosService = getNacosService();
      const status = nacosService.getStatus();
      
      let serviceStatus: 'healthy' | 'unavailable' | 'error' = 'healthy';
      
      if (!status.connected) {
        serviceStatus = 'unavailable';
      } else if (!status.textinConfigLoaded || !status.securityConfigLoaded) {
        serviceStatus = 'error';
      }
      
      return {
        status: serviceStatus,
        connected: status.connected,
        textinConfigLoaded: status.textinConfigLoaded,
        securityConfigLoaded: status.securityConfigLoaded
      };
    } catch (error) {
      logger.error('Nacos health check failed', { error });
      return {
        status: 'unavailable',
        connected: false,
        textinConfigLoaded: false,
        securityConfigLoaded: false
      };
    }
  }

  /**
   * 计算性能指标
   */
  private getPerformanceMetrics(): SystemHealthStatus['performance'] {
    const { requests, errors, totalResponseTime } = this.performanceMetrics;
    
    return {
      avgResponseTime: requests > 0 ? Math.round(totalResponseTime / requests) : 0,
      requestCount: requests,
      errorRate: requests > 0 ? Math.round((errors / requests) * 100 * 100) / 100 : 0
    };
  }

  /**
   * 获取完整的系统健康状态
   */
  getHealthStatus(): SystemHealthStatus {
    const memory = this.getMemoryStatus();
    const cpu = this.getCpuStatus();
    const disk = this.getDiskStatus();
    const imageService = this.getImageServiceStatus();
    const pdfCache = this.getPdfCacheStatus();
    const textinApi = this.getTextinApiStatus();
    const nacos = this.getNacosStatus();
    const performance = this.getPerformanceMetrics();

    // 计算总体状态
    let overallStatus: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (memory.status === 'critical' || cpu.status === 'critical' || 
        disk.temp.status === 'error' || imageService.status === 'error' ||
        nacos.status === 'error' || performance.errorRate > 20) {
      overallStatus = 'critical';
    } else if (memory.status === 'warning' || cpu.status === 'warning' ||
               pdfCache.status === 'error' || textinApi.status === 'error' ||
               nacos.status === 'unavailable' || performance.errorRate > 10) {
      overallStatus = 'warning';
    }

    const healthStatus: SystemHealthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      system: {
        memory,
        cpu,
        disk
      },
      services: {
        imageService,
        pdfCache,
        textinApi,
        nacos
      },
      performance
    };

    // 记录健康状态到日志
    if (overallStatus !== 'healthy') {
      logger.warn('System health status degraded', {
        status: overallStatus,
        issues: {
          memory: memory.status !== 'normal' ? memory.status : null,
          cpu: cpu.status !== 'normal' ? cpu.status : null,
          disk: disk.temp.status !== 'normal' ? disk.temp.status : null,
          services: {
            imageService: imageService.status !== 'healthy' ? imageService.status : null,
            pdfCache: pdfCache.status !== 'healthy' ? pdfCache.status : null,
            textinApi: textinApi.status !== 'healthy' ? textinApi.status : null,
            nacos: nacos.status !== 'healthy' ? nacos.status : null,
          },
          performance: performance.errorRate > 10 ? `${performance.errorRate}% error rate` : null
        }
      });
    } else {
      logger.debug('System health check completed - all systems healthy', {
        memoryUsage: `${memory.usagePercent}%`,
        imageCount: imageService.totalImages,
        cacheStats: pdfCache.stats,
        avgResponseTime: performance.avgResponseTime
      });
    }

    return healthStatus;
  }

  /**
   * 启动定期健康检查
   */
  startHealthMonitoring(intervalMinutes: number = 5): void {
    const intervalMs = intervalMinutes * 60 * 1000;
    
    setInterval(() => {
      const health = this.getHealthStatus();
      
      logger.performance('Periodic health check', {
        status: health.status,
        memoryUsagePercent: health.system.memory.usagePercent,
        cpuLoadAvg: health.system.cpu.loadAverage[0],
        imageCount: health.services.imageService.totalImages,
        cacheCount: health.services.pdfCache.stats?.totalCached || 0,
        avgResponseTime: health.performance.avgResponseTime,
        errorRate: health.performance.errorRate
      });
      
      // 严重状态时发出告警
      if (health.status === 'critical') {
        logger.error('System health critical - immediate attention required', {
          healthStatus: health
        });
      }
    }, intervalMs);

    logger.info('Health monitoring started', {
      intervalMinutes,
      nextCheckAt: new Date(Date.now() + intervalMs).toISOString()
    });
  }
}