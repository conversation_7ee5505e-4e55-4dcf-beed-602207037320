import axios, { AxiosResponse } from 'axios';
import sharp from 'sharp';
import * as fs from 'fs/promises';
import * as path from 'path';
import { logger } from '../utils/logger';
import config from '../utils/config';
import { getNacosService } from './nacosService';
import { TextinAPIResponse, ImageProcessingOptions } from '../types';

// Helper function to save debug logs
const saveTextinDebugInfo = async (
  timestamp: string,
  filename: string,
  originalImage: Buffer,
  requestPayload: object,
  apiResponse: TextinAPIResponse | { error: string }
) => {
  const logDir = path.join(__dirname, '..', '..', 'temp', 'textin-logs', timestamp);
  try {
    await fs.mkdir(logDir, { recursive: true });
    await fs.writeFile(path.join(logDir, `original_${filename}`), originalImage);
    await fs.writeFile(path.join(logDir, 'request_payload.json'), JSON.stringify(requestPayload, null, 2));
    await fs.writeFile(path.join(logDir, 'api_response.json'), JSON.stringify(apiResponse, null, 2));

    if ('data' in apiResponse && apiResponse.data?.result) {
        const processedImageBuffer = Buffer.from(apiResponse.data.result, 'base64');
        const extension = path.extname(filename) || '.jpg';
        await fs.writeFile(path.join(logDir, `processed_${path.basename(filename, extension)}${extension}`), processedImageBuffer);
    }
  } catch (error) {
    logger.error('Failed to save Textin debug info', { error });
  }
};

function getContentTypeFromFilename(filename: string): string {
  const extension = filename.toLowerCase().split('.').pop();
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'bmp':
      return 'image/bmp';
    case 'tiff':
    case 'tif':
      return 'image/tiff';
    default:
      return 'application/octet-stream';
  }
}

export class TextinService {
  private apiUrl!: string;
  private appId!: string;
  private secret!: string;
  private useNacos: boolean;

  constructor() {
    // Initialize with fallback to environment variables
    this.apiUrl = config.textin.apiUrl;
    this.appId = config.textin.appId;
    this.secret = config.textin.secret;
    this.useNacos = false;
    logger.info('TextinService initialized with fallback configuration');
  }

  public initialize() {
    // Try to use Nacos configuration first
    this.useNacos = this.initializeFromNacos();

    if (!this.useNacos) {
      // Fallback to environment variables
      this.apiUrl = config.textin.apiUrl;
      this.appId = config.textin.appId;
      this.secret = config.textin.secret;
      logger.info('Using local environment configuration for Textin API');
    }
  }

  private initializeFromNacos(): boolean {
    try {
      const nacosService = getNacosService();
      if (nacosService.isReady()) {
        const textinConfig = nacosService.getTextinConfig();
        this.apiUrl = textinConfig.apiUrl || 'https://api.textin.com/ai/service/v1/crop_enhance_image';
        this.appId = textinConfig.appId;
        this.secret = textinConfig.secret;
        logger.info('Using Nacos configuration for Textin API');
        return true;
      }
    } catch (error) {
      logger.warn('Nacos service not available, falling back to environment variables', error);
    }
    return false;
  }

  private refreshConfigFromNacos(): void {
    if (this.useNacos) {
      try {
        const nacosService = getNacosService();
        const textinConfig = nacosService.getTextinConfig();
        this.apiUrl = textinConfig.apiUrl || 'https://api.textin.com/ai/service/v1/crop_enhance_image';
        this.appId = textinConfig.appId;
        this.secret = textinConfig.secret;
        logger.debug('Refreshed Textin API config from Nacos');
      } catch (error) {
        logger.error('Failed to refresh config from Nacos', error);
        // Continue with existing configuration
      }
    }
  }

  async processImage(
    imageBuffer: Buffer,
    filename: string,
    options: ImageProcessingOptions = { mode: 'manual' },
    context?: any,
    corners?: [number, number][]
  ): Promise<TextinAPIResponse> {
    const logContext = {
      ...context,
      filename,
      bufferLength: imageBuffer?.length,
      hasCorners: !!corners,
      apiEndpoint: this.apiUrl.split('/').pop()
    };

    logger.info('TextIn API processing started', { 
      ...logContext,
      options,
      corners: corners ? corners.length : 0
    });
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    let apiResponse: TextinAPIResponse | null = null;
    let requestPayload: object = {};
    let processedBuffer = imageBuffer; // 提升到函数作用域

    try {
      this.refreshConfigFromNacos();
      
      let imageDimensions;
      
      try {
        const metadata = await sharp(imageBuffer).metadata();
        imageDimensions = { width: metadata.width || 0, height: metadata.height || 0 };
        
        // 检查是否需要自动缩放（任一边 > 8192px）
        if (imageDimensions.width > 8192 || imageDimensions.height > 8192) {
          logger.info('Image resolution exceeds limits, auto-scaling', { 
            ...logContext,
            originalDimensions: imageDimensions,
            scalingTarget: '8192px max'
          });
          
          const resized = await sharp(imageBuffer)
            .resize(8192, 8192, { fit: 'inside', withoutEnlargement: true })
            .toBuffer();
          
          const resizedMetadata = await sharp(resized).metadata();
          imageDimensions = { width: resizedMetadata.width || 0, height: resizedMetadata.height || 0 };
          processedBuffer = resized;
          
          logger.info('Image resized successfully', { 
            filename, 
            newDimensions: imageDimensions 
          });
        }
        
        // 检查是否分辨率太低，无需处理
        if ((imageDimensions.width < 200 && imageDimensions.height < 200) || 
            imageDimensions.width < 100 || imageDimensions.height < 100) {
          logger.warn('Image resolution too low, skipping TextIn API processing', { 
            ...logContext,
            dimensions: imageDimensions,
            reason: 'below_minimum_resolution',
            threshold: 'width/height < 100px OR both < 200px'
          });
          
          return {
            code: 200,
            message: 'Low resolution image, skipped processing',
            msg: 'Low resolution image, skipped processing',
            x_request_id: '',
            data: {
              result: '', // 空字符串表示跳过处理
              corners: [],
              isLowResolution: true
            },
          } as TextinAPIResponse & { data: { isLowResolution?: boolean } };
        }
        
        if (imageDimensions.width < 20 || imageDimensions.height < 20 ||
            imageDimensions.width > 10000 || imageDimensions.height > 10000) {
          throw new Error(`图片分辨率不符合要求：${imageDimensions.width}x${imageDimensions.height}px。TextIn API 要求图片宽高须介于 20 和 10000 像素之间。`);
        }
      } catch (error) {
        if (error instanceof Error && error.message.includes('图片分辨率不符合要求')) {
          throw error; // 重新抛出分辨率错误
        }
        logger.warn('Failed to get image dimensions', { filename, error });
        imageDimensions = { width: 0, height: 0 };
      }

      const url = new URL(this.apiUrl);
      // Use correct TextIn API parameter names according to documentation
      if (options.crop !== false) url.searchParams.set('crop_image', '1');
      if (options.dewarp !== false) url.searchParams.set('dewarp', 'true');
      if (options.direction_correct !== false) url.searchParams.set('direction_correct', 'true');
      if (options.blur_reduce !== false) url.searchParams.set('blur_reduce', 'true');
      if (options.quality) url.searchParams.set('jpeg_quality', options.quality.toString());
      if (corners && corners.length === 4) {
        // Convert corners to size_and_position format: width,height,x1,y1,x2,y2,x3,y3,x4,y4
        // According to TextIn API docs, first two values should be actual image width and height
        const sizeAndPosition = [
          imageDimensions.width, imageDimensions.height, // actual image width, height
          Math.round(corners[0][0]), Math.round(corners[0][1]), // x1, y1 (left top)
          Math.round(corners[1][0]), Math.round(corners[1][1]), // x2, y2 (right top)
          Math.round(corners[2][0]), Math.round(corners[2][1]), // x3, y3 (right bottom)
          Math.round(corners[3][0]), Math.round(corners[3][1]), // x4, y4 (left bottom)
        ];
        url.searchParams.set('size_and_position', sizeAndPosition.join(','));
        logger.info('Set size_and_position parameter', { 
          filename, 
          imageDimensions, 
          corners, 
          sizeAndPosition: sizeAndPosition.join(',') 
        });
      }

      requestPayload = {
        filename,
        options,
        url: url.toString(),
        fileSize: processedBuffer.length,
        dimensions: imageDimensions,
        appIdFirst4: this.appId.substring(0, 4) + '****',
        hasCorners: !!corners,
        sizeAndPosition: corners ? url.searchParams.get('size_and_position') : null,
      };
      logger.debug('TextIn API request payload prepared', { ...logContext, ...requestPayload });

      const apiCallStart = Date.now();
      logger.info('Sending request to TextIn API', {
        ...logContext,
        url: url.toString(),
        fileSize: processedBuffer.length,
        hasCorners: !!corners,
        sizeAndPosition: corners ? url.searchParams.get('size_and_position') : null,
        timeout: '30s'
      });

      const response: AxiosResponse<TextinAPIResponse> = await axios.post(
        url.toString(),
        processedBuffer,
        {
          headers: {
            'Content-Type': 'application/octet-stream',
            'x-ti-app-id': this.appId,
            'x-ti-secret-code': this.secret,
          },
          timeout: 30000,
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        }
      );
      
      const apiCallDuration = Date.now() - apiCallStart;
      apiResponse = response.data;
      
      logger.performance('TextIn API response received', { 
        ...logContext,
        duration: apiCallDuration,
        code: apiResponse.code,
        message: apiResponse.message,
        success: apiResponse.code === 200,
        hasResult: !!apiResponse.data?.result,
        hasCorners: !!apiResponse.data?.corners,
        responseSize: JSON.stringify(apiResponse).length,
        resultDataSize: apiResponse.data?.result?.length || 0
      });

      if (apiResponse.code === 40303) {
        logger.error('Textin API file type error', { filename, message: apiResponse.message });
      }

      if (apiResponse.result?.image_list?.length && !apiResponse.data) {
        const firstImage = apiResponse.result.image_list[0];
        apiResponse.data = {
          result: firstImage.image,
          corners: [
            [firstImage.position[0], firstImage.position[1]],
            [firstImage.position[2], firstImage.position[3]],
            [firstImage.position[4], firstImage.position[5]],
            [firstImage.position[6], firstImage.position[7]]
          ]
        };
        logger.debug('Converted new API format to legacy format', { filename });
      }

      return apiResponse;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const axiosError = axios.isAxiosError(error);
      const responseStatus = axiosError ? error.response?.status : null;
      const responseData = axiosError ? error.response?.data : null;
      
      logger.error('TextIn API call failed', { 
        ...logContext,
        error: errorMessage,
        axiosError,
        responseStatus,
        responseData: responseData ? JSON.stringify(responseData).substring(0, 500) : null,
        requestUrl: (requestPayload as any).url || 'unknown',
        stack: error instanceof Error ? error.stack : undefined
      });
      
      const errorResponse = { error: errorMessage };
      saveTextinDebugInfo(timestamp, filename, processedBuffer, requestPayload, errorResponse);

      if (axiosError) {
        if (responseStatus === 401) throw new Error('Textin API authentication failed.');
        if (responseStatus === 413) throw new Error('Image file is too large for Textin API.');
        if (responseStatus === 429) throw new Error('Textin API rate limit exceeded.');
      }
      throw new Error(`Failed to process image with Textin API: ${errorMessage}`);
    } finally {
      if (apiResponse) {
        saveTextinDebugInfo(timestamp, filename, processedBuffer, requestPayload, apiResponse);
      }
    }
  }

  async batchProcessImages(
    images: Array<{ buffer: Buffer; filename: string }>,
    options: ImageProcessingOptions = { mode: 'manual' }
  ): Promise<TextinAPIResponse[]> {
    const results: TextinAPIResponse[] = [];
    
    logger.info(`Starting batch processing of ${images.length} images`);

    for (let i = 0; i < images.length; i++) {
      const { buffer, filename } = images[i];
      
      try {
        logger.debug(`Processing image ${i + 1}/${images.length}: ${filename}`);
        
        const result = await this.processImage(buffer, filename, options);
        results.push(result);
        
        if (i < images.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        logger.error(`Failed to process image ${filename}`, error);
        
        results.push({
          code: -1,
          message: error instanceof Error ? error.message : 'Processing failed',
          msg: error instanceof Error ? error.message : 'Processing failed',
          x_request_id: '',
          data: {
            result: '',
            corners: [],
          },
        });
      }
    }

    logger.info(`Batch processing completed. Processed ${results.length} images`);
    return results;
  }

  validateConfiguration(): boolean {
    if (!this.appId || !this.secret) {
      logger.error('Textin API configuration is incomplete', {
        hasAppId: !!this.appId,
        hasSecret: !!this.secret,
      });
      return false;
    }
    return true;
  }
}

export const textinService = new TextinService();