import { jsPDF } from 'jspdf';
import sharp from 'sharp';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { FileUtils } from '../utils/fileUtils';
import config from '../utils/config';
import { ProcessedImage, PDFGenerationOptions } from '../types';

export class PDFService {
  private readonly tempDir: string;

  constructor() {
    this.tempDir = config.storage.tempDir;
    FileUtils.ensureDirectoryExists(this.tempDir);
  }

  async generatePDF(
    images: ProcessedImage[],
    options: PDFGenerationOptions = {
      pageSize: config.pdf.defaultPageSize as any,
      orientation: 'portrait',
      margin: config.pdf.margin,
      quality: config.pdf.quality,
    }
  ): Promise<{ filePath: string; fileName: string; fileSize: number }> {
    logger.info(`Starting PDF generation for ${images.length} images with unified resolution.`);

    if (images.length === 0) {
      throw new Error('No images provided for PDF generation');
    }

    const fileName = `processed_images_${uuidv4()}.pdf`;
    const filePath = path.join(this.tempDir, fileName);

    try {
      // 1. Get dimensions for all images first
      const allImagesInfo = await Promise.all(
        images.map(async (image) => {
          const buffer = FileUtils.base64ToBuffer(image.cropped || image.original);
          const dimensions = await this.getImageDimensions(buffer);
          return { ...dimensions, buffer, originalName: image.originalName };
        })
      );

      // 2. Calculate median width and aspect ratio
      const widths = allImagesInfo.map(d => d.width).sort((a, b) => a - b);
      const aspectRatios = allImagesInfo.map(d => d.width / d.height).sort((a, b) => a - b);
      
      const targetWidth = widths[Math.floor(widths.length / 2)];
      const medianAspectRatio = aspectRatios[Math.floor(aspectRatios.length / 2)];
      const targetHeight = Math.round(targetWidth / medianAspectRatio);

      logger.info('Calculated target PDF page dimensions', { targetWidth, targetHeight, medianAspectRatio });

      // 3. Unify all images to the target dimensions
      const unifiedImages = await Promise.all(
        allImagesInfo.map(async (img) => {
          const mustFit = img.width < targetWidth * 0.5 || img.width > targetWidth * 2;
          let finalBuffer: Buffer;

          if (mustFit) {
            // Case B: Fit image into a canvas with padding
            finalBuffer = await sharp({
              create: {
                width: targetWidth,
                height: targetHeight,
                channels: 4,
                background: { r: 255, g: 255, b: 255, alpha: 1 },
              },
            })
            .composite([{ input: await sharp(img.buffer).resize({ width: targetWidth, height: targetHeight, fit: 'inside' }).toBuffer() }])
            .jpeg()
            .toBuffer();
          } else {
            // Case A: Stretch image to target width
            finalBuffer = await sharp(img.buffer).resize({ width: targetWidth }).jpeg().toBuffer();
          }
          return { buffer: finalBuffer, originalName: img.originalName };
        })
      );

      // 4. Create PDF with unified pages
      const dpi = config.pdf.dpi;
      const mmPerInch = 25.4;
      const marginMm = options.margin;
      const pageWidthMm = (targetWidth / dpi) * mmPerInch + (marginMm * 2);
      const pageHeightMm = (targetHeight / dpi) * mmPerInch + (marginMm * 2);

      const pdf = new jsPDF({
        orientation: pageWidthMm > pageHeightMm ? 'landscape' : 'portrait',
        unit: 'mm',
        format: [pageWidthMm, pageHeightMm],
      });

      for (let i = 0; i < unifiedImages.length; i++) {
        const image = unifiedImages[i];
        if (i > 0) {
          pdf.addPage([pageWidthMm, pageHeightMm]);
        }

        const imageWidthMm = (targetWidth / dpi) * mmPerInch;
        const imageHeightMm = (targetHeight / dpi) * mmPerInch;

        try {
          const imageDataBase64 = image.buffer.toString('base64');
          pdf.addImage(
            `data:image/jpeg;base64,${imageDataBase64}`,
            'JPEG',
            marginMm,
            marginMm,
            imageWidthMm,
            imageHeightMm,
            undefined,
            'NONE'
          );
          logger.debug(`Added unified image ${i + 1}/${images.length} to PDF.`);
        } catch (error) {
           logger.error(`Failed to add image ${image.originalName} to PDF`, error);
        }
      }

      // 5. Save PDF
      const pdfBuffer = Buffer.from(pdf.output('arraybuffer'));
      fs.writeFileSync(filePath, pdfBuffer);
      const fileSize = fs.statSync(filePath).size;

      logger.info('PDF generated successfully with unified dimensions', { fileName, fileSize });
      return { filePath, fileName, fileSize };

    } catch (error) {
      logger.error('Unified PDF generation failed', error);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getImageDimensions(imageBuffer: Buffer): Promise<{ width: number; height: number }> {
    // Simple image dimension detection using buffer analysis
    // This is a basic implementation - in production, you might want to use a more robust solution
    
    try {
      // For JPEG images
      if (imageBuffer[0] === 0xFF && imageBuffer[1] === 0xD8) {
        return this.getJPEGDimensions(imageBuffer);
      }
      
      // For PNG images
      if (imageBuffer.subarray(0, 8).equals(Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]))) {
        return this.getPNGDimensions(imageBuffer);
      }
      
      // Default dimensions if unable to detect
      return { width: 800, height: 600 };
    } catch (error) {
      logger.warn('Failed to detect image dimensions, using defaults', error);
      return { width: 800, height: 600 };
    }
  }

  private getJPEGDimensions(buffer: Buffer): { width: number; height: number } {
    let offset = 2;
    
    while (offset < buffer.length) {
      const marker = buffer.readUInt16BE(offset);
      offset += 2;
      
      if (marker >= 0xFFC0 && marker <= 0xFFC3) {
        offset += 3; // Skip length and precision
        const height = buffer.readUInt16BE(offset);
        const width = buffer.readUInt16BE(offset + 2);
        return { width, height };
      }
      
      const length = buffer.readUInt16BE(offset);
      offset += length;
    }
    
    return { width: 800, height: 600 };
  }

  private getPNGDimensions(buffer: Buffer): { width: number; height: number } {
    const width = buffer.readUInt32BE(16);
    const height = buffer.readUInt32BE(20);
    return { width, height };
  }

  async deletePDF(fileName: string): Promise<void> {
    const filePath = path.join(this.tempDir, fileName);
    
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        logger.info(`Deleted PDF file: ${fileName}`);
      }
    } catch (error) {
      logger.error(`Failed to delete PDF file: ${fileName}`, error);
    }
  }

  getPDFPath(fileName: string): string {
    return path.join(this.tempDir, fileName);
  }

  async validatePDFExists(fileName: string): Promise<boolean> {
    const filePath = path.join(this.tempDir, fileName);
    return fs.existsSync(filePath);
  }
}

export default PDFService;