import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';
import { logger } from '../utils/logger';
import { FileUtils } from '../utils/fileUtils';
import { ProcessedImage, ImageProcessingOptions, TextinAPIResponse } from '../types';
import { textinService } from './textinService';

export class ImageService {
  private static instance: ImageService;
  private processedImages: Map<string, ProcessedImage> = new Map();

  private constructor() {}

  public static getInstance(): ImageService {
    if (!ImageService.instance) {
      ImageService.instance = new ImageService();
      logger.info('ImageService singleton instance created');
    }
    return ImageService.instance;
  }

  async processImages(
    files: Express.Multer.File[],
    options: ImageProcessingOptions = { mode: 'manual' },
    req?: any
  ): Promise<ProcessedImage[]> {
    const requestId = req?.requestId || 'unknown';
    const sessionId = req?.sessionId || 'unknown';
    
    logger.info('Image batch processing started', {
      requestId,
      sessionId,
      totalFiles: files.length,
      processingOptions: options,
      fileDetails: files.map(f => ({
        name: f.originalname,
        size: f.size,
        mimetype: f.mimetype
      }))
    });

    const results: ProcessedImage[] = [];
    const processingResults = {
      successful: 0,
      failed: 0,
      lowResolution: 0,
      textinProcessed: 0,
      manualFallback: 0
    };

    for (let index = 0; index < files.length; index++) {
      const file = files[index];
      const fileContext = {
        requestId,
        sessionId,
        fileIndex: index + 1,
        totalFiles: files.length,
        fileName: file.originalname,
        fileSize: file.size
      };

      logger.debug('Processing individual file', fileContext);

      try {
        const processedImage = await this.processSingleImage(file, options, fileContext);
        results.push(processedImage);
        
        // Store in memory for later PDF generation
        this.processedImages.set(processedImage.id, processedImage);
        
        // Update statistics
        processingResults.successful++;
        if (processedImage.isLowResolution) {
          processingResults.lowResolution++;
        } else if (processedImage.cropped) {
          processingResults.textinProcessed++;
        } else {
          processingResults.manualFallback++;
        }

        logger.debug('File processing successful', {
          ...fileContext,
          imageId: processedImage.id,
          hasOriginal: !!processedImage.original,
          hasCropped: !!processedImage.cropped,
          isLowRes: !!processedImage.isLowResolution,
          confidence: processedImage.confidence
        });
        
      } catch (error) {
        processingResults.failed++;
        
        logger.error('File processing failed', {
          ...fileContext,
          error: error instanceof Error ? {
            name: error.name,
            message: error.message,
            stack: error.stack
          } : error
        });
        
        // Add failed image to results with original data
        const failedImage: ProcessedImage = {
          id: uuidv4(),
          originalName: file.originalname,
          original: FileUtils.convertToBase64(file.buffer),
          timestamp: Date.now(),
        };
        
        results.push(failedImage);
        this.processedImages.set(failedImage.id, failedImage);
        
        logger.warn('Added failed image to results as fallback', {
          ...fileContext,
          fallbackImageId: failedImage.id
        });
      }
    }

    // Final verification
    const memoryStats = this.getStats();
    const storedIds = this.getAllImageIds();
    
    logger.audit('Image batch processing completed', {
      requestId,
      sessionId,
      inputStats: {
        totalFiles: files.length,
        totalSizeMB: Math.round(files.reduce((sum, f) => sum + f.size, 0) / (1024 * 1024) * 100) / 100
      },
      processingResults,
      outputStats: {
        totalProcessed: results.length,
        storedInMemory: memoryStats.totalImages,
        idsMatch: results.length === storedIds.length
      },
      discrepancies: {
        inputOutputMismatch: files.length !== results.length,
        memoryStorageMismatch: results.length !== memoryStats.totalImages
      }
    });

    return results;
  }

  private async processSingleImage(
    file: Express.Multer.File,
    options: ImageProcessingOptions,
    context?: any
  ): Promise<ProcessedImage> {
    const imageId = uuidv4();
    const originalBase64 = FileUtils.convertToBase64(file.buffer);

    const logContext = {
      ...context,
      imageId,
      size: file.size,
      mimetype: file.mimetype,
      processingMode: options.mode
    };

    logger.debug('Single image processing started', logContext);

    // Initialize processed image
    const processedImage: ProcessedImage = {
      id: imageId,
      originalName: file.originalname,
      original: originalBase64,
      timestamp: Date.now(),
    };

    let processingPath = 'unknown';
    let processingError = null;

    // Process based on mode
    if (options.mode === 'auto' || options.mode === 'hybrid') {
      try {
        logger.debug('Attempting TextIn API processing', logContext);
        await this.processWithTextin(file, processedImage, options, context);
        processingPath = 'textin_api';
        logger.debug('TextIn API processing successful', { 
          ...logContext, 
          hasCropped: !!processedImage.cropped,
          confidence: processedImage.confidence,
          isLowRes: processedImage.isLowResolution 
        });
      } catch (error) {
        processingError = error;
        logger.warn('TextIn processing failed, evaluating fallback', {
          ...logContext,
          error: error instanceof Error ? {
            name: error.name,
            message: error.message
          } : error
        });
        
        if (options.mode === 'hybrid') {
          // For hybrid mode, fall back to basic processing
          logger.debug('Falling back to manual processing', logContext);
          await this.processManually(file, processedImage, options, context);
          processingPath = 'manual_fallback';
        } else {
          // For auto mode, throw the error
          processingPath = 'failed';
          throw error;
        }
      }
    } else if (options.mode === 'manual') {
      logger.debug('Using manual processing mode', logContext);
      await this.processManually(file, processedImage, options, context);
      processingPath = 'manual';
    }

    logger.debug('Single image processing completed', {
      ...logContext,
      processingPath,
      hasOriginal: !!processedImage.original,
      hasCropped: !!processedImage.cropped,
      hasCorners: !!processedImage.corners && processedImage.corners.length > 0,
      isLowResolution: !!processedImage.isLowResolution,
      confidence: processedImage.confidence,
      processingError: processingError ? {
        name: processingError instanceof Error ? processingError.name : 'Unknown',
        message: processingError instanceof Error ? processingError.message : 'Unknown error'
      } : null
    });

    return processedImage;
  }

  private async processWithTextin(
    file: Express.Multer.File,
    processedImage: ProcessedImage,
    options: ImageProcessingOptions,
    context?: any
  ): Promise<void> {
    if (!textinService.validateConfiguration()) {
      throw new Error('Textin API is not properly configured');
    }

    // Set default options for AI processing
    const textinOptions: ImageProcessingOptions = {
      ...options,
      crop: options.crop !== false, // Default to true
      dewarp: options.dewarp !== false, // Default to true
      direction_correct: options.direction_correct !== false, // Default to true
      blur_reduce: options.blur_reduce !== false, // Default to true
      quality: options.quality || 90,
    };

    const response: TextinAPIResponse = await textinService.processImage(
      file.buffer,
      file.originalname,
      textinOptions,
      context
    );

    if (response.code === 200) {
      // 检查是否是低分辨率图片（跳过处理的）
      if ((response.data as any)?.isLowResolution) {
        processedImage.isLowResolution = true;
        processedImage.cropped = processedImage.original; // 使用原图
        logger.info(`Image ${file.originalname} marked as low resolution, using original image`);
      } else if (response.data?.result) {
        processedImage.cropped = response.data.result;
        processedImage.corners = response.data.corners;
        processedImage.confidence = 0.95; // Default confidence for successful processing
        processedImage.enhancement = options.enhancement;
      }

      logger.info(`Successfully processed ${file.originalname} with Textin API`, {
        filename: file.originalname,
        resultSize: response.data?.result?.length || 0,
        hasCorners: !!response.data?.corners,
        cornersCount: response.data?.corners?.length || 0
      });
    } else {
      logger.error(`Textin API processing failed for ${file.originalname}`, {
        filename: file.originalname,
        code: response.code,
        message: response.message,
        hasData: !!response.data,
        hasResult: !!response.data?.result
      });
      throw new Error(`Textin API returned error: ${response.message}`);
    }
  }

  private async processManually(
    file: Express.Multer.File,
    processedImage: ProcessedImage,
    options: ImageProcessingOptions,
    context?: any
  ): Promise<void> {
    logger.debug(`Processing ${file.originalname} manually`);

    let processedBuffer = file.buffer;

    // Apply basic image enhancements using Sharp
    try {
      let sharpInstance = sharp(file.buffer);

      if (options.enhancement) {
        switch (options.enhancement) {
          case 'brighten':
            sharpInstance = sharpInstance.modulate({ brightness: 1.2 });
            break;
          case 'sharpen':
            sharpInstance = sharpInstance.sharpen();
            break;
          case 'bw':
            sharpInstance = sharpInstance.grayscale().threshold(128);
            break;
          case 'grayscale':
            sharpInstance = sharpInstance.grayscale();
            break;
          case 'remove_shadow':
            // Basic shadow removal using contrast and brightness
            sharpInstance = sharpInstance.modulate({ brightness: 1.1 });
            break;
        }
      }

      // Apply quality settings
      if (options.quality) {
        sharpInstance = sharpInstance.jpeg({ quality: options.quality });
      }

      processedBuffer = await sharpInstance.toBuffer();
      processedImage.cropped = FileUtils.convertToBase64(processedBuffer);
      processedImage.enhancement = options.enhancement;

      logger.info(`Successfully processed ${file.originalname} manually`);
    } catch (error) {
      logger.error(`Manual processing failed for ${file.originalname}`, error);
      // Use original image if processing fails
      processedImage.cropped = processedImage.original;
    }
  }

  getProcessedImage(imageId: string): ProcessedImage | undefined {
    return this.processedImages.get(imageId);
  }

  getProcessedImages(imageIds: string[]): ProcessedImage[] {
    return imageIds
      .map(id => this.processedImages.get(id))
      .filter((image): image is ProcessedImage => image !== undefined);
  }

  clearProcessedImages(imageIds?: string[]): void {
    if (imageIds) {
      imageIds.forEach(id => this.processedImages.delete(id));
    } else {
      this.processedImages.clear();
    }
    
    logger.info(`Cleared processed images from memory`, {
      cleared: imageIds ? imageIds.length : 'all',
    });
  }

  getStats(): { totalImages: number; memoryUsage: string } {
    const totalImages = this.processedImages.size;
    const memoryUsage = `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`;
    
    return { totalImages, memoryUsage };
  }

  getAllImageIds(): string[] {
    return Array.from(this.processedImages.keys());
  }

  getTotalImagesCount(): number {
    return this.processedImages.size;
  }

  async reprocessImageWithManualCrop(
    imageId: string,
    corners: [number, number][],
    options: ImageProcessingOptions
  ): Promise<ProcessedImage> {
    logger.info('=== ENTERING reprocessImageWithManualCrop ===', { 
      imageId, 
      cornersCount: corners?.length, 
      corners: corners,
      options 
    });

    const existingImage = this.processedImages.get(imageId);
    if (!existingImage) {
      logger.error('Image not found in memory for reprocessing', { imageId, availableImages: Array.from(this.processedImages.keys()) });
      throw new Error(`Image with ID ${imageId} not found`);
    }
    logger.info('Found existing image in memory', { imageId, originalName: existingImage.originalName, hasOriginal: !!existingImage.original });

    logger.info('Converting base64 to buffer...', { imageId });
    const originalBuffer = Buffer.from(existingImage.original, 'base64');
    logger.info(`Converted base64 to buffer. Buffer length: ${originalBuffer.length}`, { imageId });

    try {
      logger.info('=== CALLING textinService.processImage ===', { imageId, corners, options });
      const response = await textinService.processImage(
        originalBuffer,
        existingImage.originalName,
        options,
        corners
      );
      logger.info('=== textinService.processImage COMPLETED ===', { imageId, responseCode: response?.code });

      if (response.code === 200 && response.data?.result) {
        const updatedImage: ProcessedImage = {
          ...existingImage,
          cropped: response.data.result,
          corners: response.data.corners,
          enhancement: options.enhancement,
          timestamp: Date.now(),
        };

        this.processedImages.set(imageId, updatedImage);

        logger.info(`Successfully reprocessed image with manual crop via Textin API`, {
          imageId,
          originalName: existingImage.originalName,
          resultSize: response.data.result.length,
          hasCorners: !!response.data.corners,
          cornersCount: response.data.corners?.length || 0
        });

        return updatedImage;
      } else {
        logger.error(`Textin API reprocessing failed for ${existingImage.originalName}`, {
          imageId,
          originalName: existingImage.originalName,
          code: response.code,
          message: response.message,
          hasData: !!response.data,
          hasResult: !!response.data?.result
        });
        throw new Error(`Textin API returned error during reprocess: ${response.message}`);
      }
    } catch (error) {
      logger.error(`Failed to reprocess image with manual crop`, {
        imageId,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }
}