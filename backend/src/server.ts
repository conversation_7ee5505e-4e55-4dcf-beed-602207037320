import express from 'express';
import cors, { CorsOptions, CorsOptionsDelegate } from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import path from 'path';

import config, { validateConfig } from './utils/config';
import { logger } from './utils/logger';
import { FileUtils } from './utils/fileUtils';
import { errorHandler } from './middleware/errorHandler';
import { initializeNacosService } from './services/nacosService';
import { textinService } from './services/textinService';

import imageRoutes from './routes/imageRoutes';
import pdfRoutes from './routes/pdfRoutes';
import configRoutes from './routes/configRoutes';
import securityRoutes from './routes/securityRoutes';
import healthRoutes from './routes/healthRoutes';
import nativeAuthRoutes from './routes/nativeAuthRoutes';
import { setSecurityHeaders } from './middleware/security';
import { requestTracker, errorTracker } from './middleware/requestTracker';
import { HealthService } from './services/healthService';

class Server {
  private app: express.Application;

  constructor() {
    this.app = express();
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    this.app.set('trust proxy', 1);
    // Request tracking middleware (before all other middleware)
    this.app.use(requestTracker);

    // Security middleware
    this.app.use(helmet());
    this.app.use(setSecurityHeaders);

    // CORS configuration (use dynamic origins incl. Nacos-configured)
    const localNetworkRegex = /^(http|https):\/\/(localhost|127\.0\.0\.1|192\.168\.\d{1,3}\.\d{1,3}|10\.\d{1,3}\.\d{1,3}\.\d{1,3}|172\.(1[6-9]|2[0-9]|3[0-1])\.\d{1,3}\.\d{1,3})(?::\d+)?$/;

    const corsOptionsDelegate: CorsOptionsDelegate = (req, callback) => {
      // Import here to avoid potential import cycles initialization issues
      const { getDynamicAllowedOrigins } = require('./middleware/security');
      const originHeader: string | undefined =
        (req.headers && (req.headers as any).origin as string) ||
        (req.headers && (req.headers as any).Origin as string) ||
        undefined;
      const allowedOrigins: string[] = getDynamicAllowedOrigins(req);
      const normalizedAllowed = allowedOrigins.map((o: string) => o.replace(/\/$/, ''));

      let options: CorsOptions = { credentials: true, origin: false };

      // Allow requests without Origin (curl, server-to-server, mobile)
      if (!originHeader) {
        options.origin = true;
        return callback(null, options);
      }

      const normalizedOrigin = originHeader.replace(/\/$/, '');

      if (normalizedAllowed.includes(normalizedOrigin) || (originHeader && localNetworkRegex.test(originHeader))) {
        options.origin = true;
        return callback(null, options);
      }

      // Log and block otherwise
      logger.error('Not allowed by CORS', { origin: originHeader, allowedOrigins });
      return callback(null, options); // origin=false => CORS blocked
    };

    // Must come before rate limiting to allow OPTIONS preflight
    this.app.use(cors(corsOptionsDelegate));

    // Rate limiting (after CORS to allow OPTIONS preflight requests to pass through)
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: 'Too many requests from this IP, please try again later.',
    });
    this.app.use(limiter);

    // Body parsing middleware - increased limit for PDF generation with many images
    this.app.use(express.json({ limit: '150mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '150mb' }));

    // Static files for temp downloads
    this.app.use('/temp', express.static(path.join(__dirname, '../temp')));

    // Request logging（使用完整路由路径，便于与HMAC签名路径对齐）
    this.app.use((req, res, next) => {
      const fullPath = (req.baseUrl || '') + req.path;
      logger.info(`${req.method} ${fullPath}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      next();
    });
  }

  private initializeRoutes(): void {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        message: 'Server is running',
        timestamp: new Date().toISOString(),
      });
    });

    // API routes
    this.app.use(`${config.apiPrefix}/health`, healthRoutes);
    this.app.use(`${config.apiPrefix}/images`, imageRoutes);
    this.app.use(`${config.apiPrefix}/pdf`, pdfRoutes);
    this.app.use(`${config.apiPrefix}/config`, configRoutes);
    this.app.use(`${config.apiPrefix}/init`, securityRoutes);
    this.app.use(`${config.apiPrefix}/native`, nativeAuthRoutes);

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'Route not found',
        message: `Cannot ${req.method} ${req.originalUrl}`,
      });
    });
  }

  private initializeErrorHandling(): void {
    // Error tracking middleware (before error handler)
    this.app.use(errorTracker);
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Validate configuration
      validateConfig();

      // Initialize Nacos service if configured
      if (config.nacos.serverAddr && config.nacos.serverAddr !== 'localhost:8848' || process.env.NACOS_SERVER_ADDR) {
        try {
          logger.info('Initializing Nacos configuration service...');
          await initializeNacosService(config.nacos);
          logger.info('Nacos service initialized successfully');

          // Initialize Textin service with Nacos config
          textinService.initialize();
        } catch (error) {
          logger.error('Failed to initialize Nacos service', error);
          if (config.nodeEnv === 'production') {
            logger.error('Nacos is required in production environment');
            process.exit(1);
          } else {
            logger.warn('Continuing with local configuration in development mode');
          }
        }
      } else {
        logger.info('Nacos not configured, using local environment variables');
      }

      // Ensure temp directory exists
      FileUtils.ensureDirectoryExists(config.storage.tempDir);

      // Start temp file cleanup scheduler
      FileUtils.startCleanupScheduler();

      // Initialize health monitoring
      const healthService = HealthService.getInstance();
      healthService.startHealthMonitoring(5); // Check every 5 minutes

      // Start server
      this.app.listen(config.port, () => {
        logger.audit('Server started successfully', {
          environment: config.nodeEnv,
          port: config.port,
          tempDir: config.storage.tempDir,
          nacosEnabled: !!config.nacos.serverAddr,
          healthMonitoringEnabled: true,
          requestTrackingEnabled: true,
          structuredLoggingEnabled: true
        });
      });
    } catch (error) {
      logger.error('Failed to start server', error);
      process.exit(1);
    }
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  process.exit(1);
});

// Start server
const server = new Server();
server.start().catch((error) => {
  logger.error('Failed to start application', error);
  process.exit(1);
});
