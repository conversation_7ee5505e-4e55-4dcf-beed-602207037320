export interface ProcessedImage {
  id: string;
  originalName: string;
  original: string;
  cropped?: string;
  corners?: [number, number][];
  confidence?: number;
  enhancement?: string;
  timestamp: number;
  isLowResolution?: boolean;
}

export interface ImageProcessingOptions {
  mode: 'auto' | 'manual' | 'hybrid';
  enhancement?: 'brighten' | 'sharpen' | 'bw' | 'grayscale' | 'remove_shadow';
  quality?: number;
  crop?: boolean;
  dewarp?: boolean;
  direction_correct?: boolean;
  blur_reduce?: boolean;
}

export interface PDFGenerationOptions {
  pageSize: 'A4' | 'Letter';
  orientation: 'portrait' | 'landscape';
  margin: number;
  quality: number;
}

export interface TextinAPIResponse {
  code: number;
  message: string;
  msg: string;
  x_request_id: string;
  version?: string;
  duration?: number;
  result?: {
    image_list: Array<{
      angle: number;
      cropped_height: number;
      cropped_width: number;
      image: string;
      position: [number, number, number, number, number, number, number, number];
    }>;
    origin_height: number;
    origin_width: number;
  };
  // Keep old format for backward compatibility
  data?: {
    result: string;
    corners: [number, number][];
  };
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  code?: string;
  details?: {
    reason?: string;
    suggestion?: string;
    [key: string]: any;
  };
}

export interface UploadResponse {
  images: ProcessedImage[];
}

export interface PDFResponse {
  downloadUrl: string;
  fileSize: number;
  filename: string;
}