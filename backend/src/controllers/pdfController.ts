import { Request, Response } from 'express';
import fs from 'fs';
import path from 'path';
import { logger } from '../utils/logger';
import config from '../utils/config';
import { APIResponse, PDFResponse, PDFGenerationOptions } from '../types';
import { ImageService } from '../services/imageService';
import PDFService from '../services/pdfService';
import { PDFCacheService } from '../services/pdfCacheService';
import { asyncHandler } from '../middleware/errorHandler';

export class PDFController {
  private imageService: ImageService;
  private pdfService: PDFService;
  private cacheService: PDFCacheService;

  constructor() {
    this.imageService = ImageService.getInstance();
    this.pdfService = new PDFService();
    this.cacheService = PDFCacheService.getInstance();
  }

  generatePDFFromData = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { images, options: pdfOptions } = req.body;
    const sessionId = req.headers['x-client-id'] as string || 'default';

    logger.info(`Generating PDF from image data for ${images.length} images`, { sessionId });

    try {
      // Convert image data format to ProcessedImage format
      const processedImages = images.map((img: { id: string; data: string; originalName: string }) => ({
        id: img.id,
        original: img.data, // Use the provided data (could be cropped or original)
        originalName: img.originalName,
        timestamp: Date.now()
      }));

      logger.info('Converted image data', { 
        imageCount: processedImages.length,
        imageIds: processedImages.map((img: { id: string }) => img.id),
        imageNames: processedImages.map((img: { originalName: string }) => img.originalName)
      });

      // Set default PDF options from config
      const options: PDFGenerationOptions = {
        pageSize: pdfOptions?.pageSize || config.pdf.defaultPageSize as any,
        orientation: pdfOptions?.orientation || 'portrait',
        margin: pdfOptions?.margin !== undefined ? pdfOptions.margin : config.pdf.margin,
        quality: pdfOptions?.quality || config.pdf.quality,
      };

      // Check cache first
      const cachedPDF = this.cacheService.checkCache(processedImages, options, sessionId);
      
      let filePath: string, fileName: string, fileSize: number;
      
      if (cachedPDF) {
        // Serve from cache
        const cachedResult = await this.cacheService.serveCachedPDF(cachedPDF);
        filePath = cachedResult.filePath;
        fileName = cachedResult.fileName;
        fileSize = cachedResult.fileSize;
        
        logger.info('PDF served from cache', { sessionId, fileName, fileSize });
      } else {
        // Generate new PDF
        const generateResult = await this.pdfService.generatePDF(processedImages, options);
        filePath = generateResult.filePath;
        fileName = generateResult.fileName;
        fileSize = generateResult.fileSize;
        
        // Cache the generated PDF
        this.cacheService.cacheFile(processedImages, options, sessionId, filePath, fileName);
        
        logger.info('PDF generated and cached', { sessionId, fileName, fileSize });
      }

      const response: APIResponse<PDFResponse> = {
        success: true,
        data: {
          downloadUrl: `/pdf/download/${fileName}`,
          fileSize,
          filename: fileName,
        },
        message: `PDF generated successfully with ${processedImages.length} images`,
      };

      logger.info('PDF generated successfully from image data', {
        fileName,
        fileSize,
        imageCount: processedImages.length,
      });

      res.status(200).json(response);
    } catch (error) {
      logger.error('PDF generation from image data failed', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'PDF generation failed',
        message: 'Failed to generate PDF from image data',
      };

      res.status(500).json(response);
    }
  });

  generatePDF = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { imageIds, options: pdfOptions } = req.body;
    const sessionId = req.headers['x-client-id'] as string || 'default';

    logger.info(`Generating PDF for ${imageIds.length} images`, { sessionId, imageIds, pdfOptions });

    try {
      // Get processed images
      logger.info('Attempting to get processed images from memory', { 
        requestedImageIds: imageIds,
        totalImagesInMemory: this.imageService.getTotalImagesCount()
      });
      
      const images = this.imageService.getProcessedImages(imageIds);
      
      logger.info('Retrieved images from memory', { 
        requestedCount: imageIds.length,
        foundCount: images.length,
        foundImageIds: images.map(img => img.id),
        foundImageNames: images.map(img => img.originalName)
      });

      if (images.length === 0) {
        logger.error('No images found in memory for PDF generation', { 
          requestedImageIds: imageIds,
          availableImageIds: this.imageService.getAllImageIds()
        });
        
        const response: APIResponse = {
          success: false,
          error: 'No images found',
          message: 'No processed images found for the provided IDs',
        };

        res.status(404).json(response);
        return;
      }

      if (images.length !== imageIds.length) {
        logger.warn(`Some images not found. Requested: ${imageIds.length}, Found: ${images.length}`);
      }

      // Set default PDF options from config
      const options: PDFGenerationOptions = {
        pageSize: pdfOptions?.pageSize || config.pdf.defaultPageSize as any,
        orientation: pdfOptions?.orientation || 'portrait',
        margin: pdfOptions?.margin !== undefined ? pdfOptions.margin : config.pdf.margin,
        quality: pdfOptions?.quality || config.pdf.quality,
      };

      // Check cache first
      const cachedPDF = this.cacheService.checkCache(images, options, sessionId);
      
      let filePath: string, fileName: string, fileSize: number;
      
      if (cachedPDF) {
        // Serve from cache
        const cachedResult = await this.cacheService.serveCachedPDF(cachedPDF);
        filePath = cachedResult.filePath;
        fileName = cachedResult.fileName;
        fileSize = cachedResult.fileSize;
        
        logger.info('PDF served from cache', { sessionId, fileName, fileSize });
      } else {
        // Generate new PDF
        const generateResult = await this.pdfService.generatePDF(images, options);
        filePath = generateResult.filePath;
        fileName = generateResult.fileName;
        fileSize = generateResult.fileSize;
        
        // Cache the generated PDF
        this.cacheService.cacheFile(images, options, sessionId, filePath, fileName);
        
        logger.info('PDF generated and cached', { sessionId, fileName, fileSize });
      }

      const response: APIResponse<PDFResponse> = {
        success: true,
        data: {
          downloadUrl: `/pdf/download/${fileName}`,
          fileSize,
          filename: fileName,
        },
        message: `PDF generated successfully with ${images.length} images`,
      };

      logger.info('PDF generated successfully', {
        fileName,
        fileSize,
        imageCount: images.length,
      });

      res.status(200).json(response);
    } catch (error) {
      logger.error('PDF generation failed', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'PDF generation failed',
        message: 'Failed to generate PDF',
      };

      res.status(500).json(response);
    }
  });

  downloadPDF = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { fileName } = req.params;

    logger.info(`PDF download requested: ${fileName}`);

    try {
      // Validate file exists
      const exists = await this.pdfService.validatePDFExists(fileName);
      if (!exists) {
        const response: APIResponse = {
          success: false,
          error: 'File not found',
          message: 'The requested PDF file was not found',
        };

        res.status(404).json(response);
        return;
      }

      const filePath = this.pdfService.getPDFPath(fileName);
      const stat = fs.statSync(filePath);

      // Set headers for file download
      res.set({
        'Content-Type': 'application/pdf',
        'Content-Length': stat.size.toString(),
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Cache-Control': 'no-cache',
      });

      // Create read stream and pipe to response
      const fileStream = fs.createReadStream(filePath);
      
      fileStream.on('error', (error) => {
        logger.error('Error streaming PDF file', error);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            error: 'Failed to stream file',
          });
        }
      });

      fileStream.on('end', () => {
        logger.info(`PDF download completed: ${fileName}`);
        
        // Schedule file deletion after successful download
        setTimeout(() => {
          this.pdfService.deletePDF(fileName);
        }, 60000); // Delete after 1 minute
      });

      fileStream.pipe(res);
      
    } catch (error) {
      logger.error('PDF download failed', error);
      
      if (!res.headersSent) {
        const response: APIResponse = {
          success: false,
          error: error instanceof Error ? error.message : 'Download failed',
          message: 'Failed to download PDF',
        };

        res.status(500).json(response);
      }
    }
  });

  deletePDF = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { fileName } = req.params;

    logger.info(`PDF deletion requested: ${fileName}`);

    try {
      await this.pdfService.deletePDF(fileName);

      const response: APIResponse = {
        success: true,
        message: `PDF file ${fileName} deleted successfully`,
      };

      res.status(200).json(response);
    } catch (error) {
      logger.error('PDF deletion failed', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Deletion failed',
        message: 'Failed to delete PDF',
      };

      res.status(500).json(response);
    }
  });

  getPDFInfo = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { fileName } = req.params;

    try {
      const exists = await this.pdfService.validatePDFExists(fileName);
      
      if (!exists) {
        const response: APIResponse = {
          success: false,
          error: 'File not found',
          message: 'The requested PDF file was not found',
        };

        res.status(404).json(response);
        return;
      }

      const filePath = this.pdfService.getPDFPath(fileName);
      const stat = fs.statSync(filePath);

      const response: APIResponse = {
        success: true,
        data: {
          fileName,
          fileSize: stat.size,
          createdAt: stat.birthtime,
          modifiedAt: stat.mtime,
        },
      };

      res.status(200).json(response);
    } catch (error) {
      logger.error('Failed to get PDF info', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get PDF info',
      };

      res.status(500).json(response);
    }
  });
}

export default PDFController;