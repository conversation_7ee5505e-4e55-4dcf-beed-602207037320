import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { APIResponse, UploadResponse, ImageProcessingOptions } from '../types';
import { ImageService } from '../services/imageService';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';
import { StageTracker } from '../middleware/requestTracker';

export class ImageController {
  private imageService: ImageService;

  constructor() {
    this.imageService = ImageService.getInstance();
  }

  uploadAndProcess = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const tracker = new StageTracker(req);
    
    tracker.startStage('upload_validation', {
      hasFiles: !!req.files,
      filesCount: req.files ? (req.files as Express.Multer.File[]).length : 0,
      bodyKeys: Object.keys(req.body),
      hasOptions: !!req.body.options
    });

    logger.audit('Upload request received', {
      requestId: req.requestId,
      sessionId: req.sessionId,
      ip: req.trackingMeta?.ip,
      userAgent: req.trackingMeta?.userAgent,
      hasFiles: !!req.files,
      filesCount: req.files ? (req.files as Express.Multer.File[]).length : 0,
      contentLength: req.get('content-length')
    });

    const files = req.files as Express.Multer.File[];
    if (!files || files.length === 0) {
      tracker.endStage('upload_validation', null, new Error('No files provided'));
      logger.error('No files received in upload request', {
        requestId: req.requestId,
        sessionId: req.sessionId
      });
      res.status(400).json({ success: false, error: 'No files provided' });
      return;
    }

    // 验证每个文件
    const fileValidationResults = files.map((file, index) => ({
      index,
      name: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      isValid: file.buffer && file.buffer.length > 0,
      sizeOK: file.size <= 10 * 1024 * 1024, // 10MB limit
      typeOK: file.mimetype.startsWith('image/')
    }));

    const invalidFiles = fileValidationResults.filter(f => !f.isValid || !f.sizeOK || !f.typeOK);
    if (invalidFiles.length > 0) {
      tracker.endStage('upload_validation', null, new Error('Invalid files detected'));
      logger.error('Invalid files detected', {
        requestId: req.requestId,
        sessionId: req.sessionId,
        invalidFiles,
        totalFiles: files.length
      });
    }

    const options: ImageProcessingOptions = req.body.options ? JSON.parse(req.body.options) : {};
    tracker.endStage('upload_validation', { validFiles: files.length - invalidFiles.length });

    logger.info('File validation completed, starting processing', {
      requestId: req.requestId,
      sessionId: req.sessionId,
      totalFiles: files.length,
      invalidFiles: invalidFiles.length,
      validFiles: files.length - invalidFiles.length,
      options,
      fileDetails: fileValidationResults.map(f => ({
        name: f.name,
        size: f.size,
        mimetype: f.mimetype,
        valid: f.isValid && f.sizeOK && f.typeOK
      }))
    });

    try {
      tracker.startStage('process_setup');
      
      // Set default options
      const processOptions: ImageProcessingOptions = {
        mode: options.mode || 'auto',
        enhancement: options.enhancement,
        quality: options.quality || 90,
        crop: options.crop !== false,
        dewarp: options.dewarp !== false,
        direction_correct: options.direction_correct !== false,
        blur_reduce: options.blur_reduce !== false,
      };
      
      tracker.endStage('process_setup', { processOptions });

      // Process images with detailed tracking
      tracker.startStage('image_processing', {
        totalFiles: files.length,
        mode: processOptions.mode
      });

      const processedImages = await this.imageService.processImages(files, processOptions, req);
      
      tracker.endStage('image_processing', {
        processedCount: processedImages.length,
        successCount: processedImages.filter(img => !img.isLowResolution && (img.cropped || img.original)).length,
        lowResCount: processedImages.filter(img => img.isLowResolution).length,
        failedCount: files.length - processedImages.length
      });

      // Verify storage
      tracker.startStage('storage_verification');
      const stats = this.imageService.getStats();
      const allImageIds = this.imageService.getAllImageIds();
      
      const storageVerification = {
        requestedFiles: files.length,
        processedImages: processedImages.length,
        storedInMemory: stats.totalImages,
        storageMatch: processedImages.length === allImageIds.length
      };
      
      tracker.endStage('storage_verification', storageVerification);

      logger.audit('Image processing pipeline completed', {
        requestId: req.requestId,
        sessionId: req.sessionId,
        inputFiles: files.length,
        outputImages: processedImages.length,
        processingMode: processOptions.mode,
        storageVerification,
        processingResults: processedImages.map(img => ({
          id: img.id,
          name: img.originalName,
          hasOriginal: !!img.original,
          hasCropped: !!img.cropped,
          isLowRes: !!img.isLowResolution,
          hasCorners: !!img.corners && img.corners.length > 0
        })),
        summary: tracker.getSummary()
      });

      const response: APIResponse<UploadResponse> = {
        success: true,
        data: {
          images: processedImages,
        },
        message: `Successfully processed ${processedImages.length} images`,
      };

      res.status(200).json(response);
    } catch (error) {
      tracker.endStage('image_processing', null, error);
      
      logger.error('Image processing pipeline failed', {
        requestId: req.requestId,
        sessionId: req.sessionId,
        error: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error,
        summary: tracker.getSummary()
      });
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Image processing failed',
        message: 'Failed to process images',
      };

      res.status(500).json(response);
    }
  });

  getImage = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { imageId } = req.params;

    logger.debug(`Retrieving image: ${imageId}`);

    try {
      const image = this.imageService.getProcessedImage(imageId);

      if (!image) {
        const response: APIResponse = {
          success: false,
          error: 'Image not found',
          message: `Image with ID ${imageId} not found`,
        };

        res.status(404).json(response);
        return;
      }

      const response: APIResponse = {
        success: true,
        data: image,
      };

      res.status(200).json(response);
    } catch (error) {
      logger.error('Failed to retrieve image', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to retrieve image',
      };

      res.status(500).json(response);
    }
  });

  getStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = this.imageService.getStats();

      const response: APIResponse = {
        success: true,
        data: stats,
      };

      res.status(200).json(response);
    } catch (error) {
      logger.error('Failed to get stats', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get stats',
      };

      res.status(500).json(response);
    }
  });

  clearImages = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { imageIds } = req.body;

    try {
      this.imageService.clearProcessedImages(imageIds);

      const response: APIResponse = {
        success: true,
        message: imageIds ? `Cleared ${imageIds.length} images` : 'Cleared all images',
      };

      res.status(200).json(response);
    } catch (error) {
      logger.error('Failed to clear images', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to clear images',
      };

      res.status(500).json(response);
    }
  });

  reprocessWithManualCrop = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { imageId } = req.params;
    const { corners, options } = req.body;

    logger.info(`Reprocessing image with manual crop`, {
      imageId,
      corners,
      options,
    });

    try {
      logger.info('Starting reprocess validation', { imageId, cornersType: typeof corners, cornersLength: Array.isArray(corners) ? corners.length : 'not array' });
      
      if (!Array.isArray(corners) || corners.length !== 4 || !corners.every(c => Array.isArray(c) && c.length === 2 && typeof c[0] === 'number' && typeof c[1] === 'number')) {
        logger.error('Invalid corners format', { corners, cornersType: typeof corners });
        res.status(400).json({ success: false, error: 'Invalid corners format.' });
        return;
      }

      logger.info('Corners validation passed, checking for existing image', { imageId });
      
      // Debug: list all available images in memory
      const stats = this.imageService.getStats();
      const allImageIds = this.imageService.getAllImageIds(); // We need to add this method
      logger.info('Current images in memory', { 
        totalImages: stats.totalImages, 
        requestedImageId: imageId,
        availableImageIds: allImageIds 
      });
      
      const existingImage = this.imageService.getProcessedImage(imageId);
      if (!existingImage) {
        logger.error('Image not found in memory', { 
          imageId, 
          totalImagesInMemory: stats.totalImages,
          availableImageIds: allImageIds
        });
        res.status(404).json({ 
          success: false, 
          error: 'Image not found in server memory', 
          message: 'The image may have been lost due to server restart. Please re-upload the image.',
          code: 'IMAGE_NOT_IN_MEMORY',
          totalImagesInMemory: stats.totalImages
        });
        return;
      }

      logger.info('Existing image found, preparing process options', { imageId, originalName: existingImage.originalName });
      const processOptions: ImageProcessingOptions = {
        mode: 'manual',
        enhancement: options?.enhancement,
        quality: options?.quality || 90,
        crop: true,
        dewarp: false,
        direction_correct: false,
        blur_reduce: false,
      };

      logger.info('Calling imageService.reprocessImageWithManualCrop', { imageId, processOptions });
      const reprocessedImage = await this.imageService.reprocessImageWithManualCrop(
        imageId,
        corners,
        processOptions
      );

      logger.info('Reprocessing completed successfully', { imageId });
      res.status(200).json({ success: true, data: reprocessedImage, message: 'Image reprocessed successfully' });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Image reprocessing failed', { imageId, error: errorMessage, stack: error instanceof Error ? error.stack : undefined });
      res.status(500).json({ 
        success: false, 
        error: 'Image reprocessing failed', 
        message: errorMessage.includes('Textin API') ? errorMessage : 'Failed to process image with manual crop. Please try again.' 
      });
    }
  });

  checkImageExists = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { imageId } = req.params;

    try {
      const existingImage = this.imageService.getProcessedImage(imageId);
      const stats = this.imageService.getStats();
      const allImageIds = this.imageService.getAllImageIds();

      if (existingImage) {
        res.status(200).json({ 
          success: true, 
          exists: true,
          data: {
            imageId,
            originalName: existingImage.originalName,
            timestamp: existingImage.timestamp
          }
        });
      } else {
        res.status(404).json({ 
          success: false, 
          exists: false,
          error: 'Image not found in server memory',
          message: 'The image may have been lost due to server restart. Please re-upload the image.',
          totalImagesInMemory: stats.totalImages,
          availableImageIds: allImageIds
        });
      }
    } catch (error) {
      logger.error('Failed to check image existence', { imageId, error });
      res.status(500).json({ 
        success: false, 
        error: 'Failed to check image existence' 
      });
    }
  });
}