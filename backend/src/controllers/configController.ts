import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { APIResponse } from '../types';
import { getNacosService } from '../services/nacosService';
import { asyncHandler } from '../middleware/errorHandler';

export class ConfigController {
  /**
   * Get configuration status
   */
  getStatus = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      let nacosStatus = null;
      let textinConfigStatus: string | object = 'not_configured';

      // Check Nacos status
      try {
        const nacosService = getNacosService();
        nacosStatus = nacosService.getStatus();
        
        if (nacosService.isReady()) {
          const textinConfig = nacosService.getTextinConfig();
          textinConfigStatus = {
            configured: true,
            hasAppId: !!textinConfig.appId,
            hasSecret: !!textinConfig.secret,
            apiUrl: textinConfig.apiUrl,
          };
        } else {
          textinConfigStatus = 'nacos_not_ready';
        }
      } catch (error) {
        nacosStatus = {
          connected: false,
          error: 'Nacos service not initialized',
        };
        
        // Check if we have fallback environment variables
        if (process.env.TEXTIN_APP_ID && process.env.TEXTIN_SECRET) {
          textinConfigStatus = {
            configured: true,
            source: 'environment_variables',
            hasAppId: !!process.env.TEXTIN_APP_ID,
            hasSecret: !!process.env.TEXTIN_SECRET,
          };
        }
      }

      const response: APIResponse = {
        success: true,
        data: {
          nacos: nacosStatus,
          textinAPI: textinConfigStatus,
          environment: process.env.NODE_ENV || 'development',
          timestamp: new Date().toISOString(),
        },
      };

      res.status(200).json(response);
    } catch (error) {
      logger.error('Failed to get config status', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get config status',
      };

      res.status(500).json(response);
    }
  });

  /**
   * Refresh configuration from Nacos
   */
  refreshConfig = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const nacosService = getNacosService();
      const textinConfig = await nacosService.refreshConfig();

      const response: APIResponse = {
        success: true,
        data: {
          message: 'Configuration refreshed successfully',
          config: {
            hasAppId: !!textinConfig.textinConfig.appId,
            hasSecret: !!textinConfig.textinConfig.secret,
            apiUrl: textinConfig.textinConfig.apiUrl,
          },
          timestamp: new Date().toISOString(),
        },
      };

      logger.info('Configuration refreshed via API request');
      res.status(200).json(response);
    } catch (error) {
      logger.error('Failed to refresh config', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to refresh config',
        message: 'Configuration refresh failed',
      };

      res.status(500).json(response);
    }
  });

  /**
   * Test Textin API connectivity
   */
  testTextinAPI = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      // This is a basic connectivity test
      // In practice, you might want to make a minimal API call to test
      let configSource = 'unknown';
      let hasCredentials = false;

      try {
        const nacosService = getNacosService();
        if (nacosService.isReady()) {
          const textinConfig = nacosService.getTextinConfig();
          configSource = 'nacos';
          hasCredentials = !!(textinConfig.appId && textinConfig.secret);
        }
      } catch (error) {
        configSource = 'environment';
        hasCredentials = !!(process.env.TEXTIN_APP_ID && process.env.TEXTIN_SECRET);
      }

      const response: APIResponse = {
        success: hasCredentials,
        data: {
          configSource,
          hasCredentials,
          message: hasCredentials 
            ? 'Textin API credentials are configured'
            : 'Textin API credentials are missing',
          timestamp: new Date().toISOString(),
        },
      };

      res.status(hasCredentials ? 200 : 400).json(response);
    } catch (error) {
      logger.error('Failed to test Textin API', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'API test failed',
      };

      res.status(500).json(response);
    }
  });

  /**
   * Get app download configuration
   */
  getAppDownloads = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      let downloadConfig = {};
      
      try {
        const nacosService = getNacosService();
        
        // Get current config (should already be loaded during initialization)
        downloadConfig = nacosService.getAppDownloadConfig();
        
        // If config is empty, try to load it
        if (!downloadConfig || Object.keys(downloadConfig).length === 0) {
          logger.info('App download config not loaded, attempting to load from Nacos');
          try {
            downloadConfig = await nacosService.loadAppDownloadConfig();
            logger.info('App download config loaded successfully on demand');
          } catch (loadError) {
            logger.warn('Failed to load app download config on demand', {
              error: loadError instanceof Error ? loadError.message : String(loadError)
            });
            // Keep empty config
            downloadConfig = {};
          }
        }
      } catch (error) {
        logger.warn('Failed to get app download config from Nacos service', { 
          error: error instanceof Error ? error.message : String(error)
        });
        // Return empty config rather than failing
        downloadConfig = {};
      }
      
      const response: APIResponse = {
        success: true,
        data: downloadConfig,
      };
      
      logger.debug('Returning app download config', {
        hasAndroid: !!(downloadConfig as any).android?.url,
        hasIos: !!(downloadConfig as any).ios?.url
      });
      
      res.status(200).json(response);
    } catch (error) {
      logger.error('Unexpected error in getAppDownloads', error);
      
      const response: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get app download config',
      };
      
      res.status(500).json(response);
    }
  });
}

export default ConfigController;