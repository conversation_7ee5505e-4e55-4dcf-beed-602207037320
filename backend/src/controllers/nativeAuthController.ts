import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { APIResponse } from '../types';
import { getDynamicKeyManager } from '../utils/dynamicKeyManager';
import { generateSessionId } from '../middleware/security';
import { encrypt, getKeyFromSecret } from '../utils/cryptoUtils';
import { getNacosService } from '../services/nacosService';

export class NativeAuthController {

  /**
   * Creates a secure, encrypted session for a native client.
   */
  createSession = (req: Request, res: Response): void => {
    const { credential } = req.body;

    try {
      // 1. Get mobile client secret from Nacos security config
      const securityConfig = getNacosService().getSecurityConfig();
      const mobileClientSecret = securityConfig.mobileClientAuth?.secretKey;

      if (!mobileClientSecret || credential !== mobileClientSecret) {
        logger.security('Native session failed: Invalid client secret', {
          ip: req.ip,
        });
        // Use a generic error message to avoid confirming that the endpoint exists
        res.status(401).json({ success: false, error: 'Unauthorized' });
        return;
      }
    } catch (error) {
      logger.error('Failed to get mobile client secret from Nacos', error);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
      return;
    }

    try {
      logger.info('Starting native session creation...');
      // 2. Generate new dynamic session credentials
      const keyManager = getDynamicKeyManager();
      const sessionId = generateSessionId();
      const hmacKey = keyManager.generateSessionKey(sessionId);
      const ttl = 3600; // Default: 3600 seconds (1 hour)

      const payload = {
        sid: sessionId,
        ticket: hmacKey.key, // This is the dynamic HMAC key
        ttl: ttl,
      };
      logger.debug('Plaintext payload for encryption', { payload });

      // 3. Encrypt the payload before sending it to the client
      const securityConfig = getNacosService().getSecurityConfig();
      const mobileClientSecret = securityConfig.mobileClientAuth?.secretKey;
      
      if (!mobileClientSecret) {
        throw new Error('Mobile client secret not configured');
      }
      
      const encryptionKey = getKeyFromSecret(mobileClientSecret);
      logger.debug(`[Crypto] Encryption key length: ${encryptionKey.length} bytes`);

      const encryptedData = encrypt(JSON.stringify(payload), encryptionKey);

      const response: APIResponse = {
        success: true,
        data: encryptedData,
      };
      
      logger.info('Native session created successfully', { sessionId: sessionId.slice(0, 8) });
      res.status(200).json(response);

    } catch (error) {
      logger.error('Failed to create native session', error);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  };
}
