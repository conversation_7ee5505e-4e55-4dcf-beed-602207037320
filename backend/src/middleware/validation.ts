import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import { ImageProcessingOptions } from '../types';
import config from '../utils/config';

export const validateFileUpload = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
    throw new AppError('No files uploaded', 400);
  }

  if (req.files.length > config.storage.maxFilesPerRequest) {
    throw new AppError(`Too many files. Maximum ${config.storage.maxFilesPerRequest} files allowed`, 400);
  }

  const files = req.files as Express.Multer.File[];
  
  for (const file of files) {
    if (file.size > config.storage.maxFileSize) {
      throw new AppError(`File ${file.originalname} is too large. Maximum size is ${config.storage.maxFileSize} bytes`, 400);
    }

    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png', 
      'image/bmp',
      'image/webp',
      'image/tiff',
      'image/gif'
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new AppError(`File ${file.originalname} has unsupported type. Allowed types: ${allowedMimeTypes.join(', ')}`, 400);
    }
  }

  next();
};

export const validateProcessingOptions = (req: Request, res: Response, next: NextFunction): void => {
  const options = req.body.options as ImageProcessingOptions;
  
  if (options) {
    const validModes = ['auto', 'manual', 'hybrid'];
    if (options.mode && !validModes.includes(options.mode)) {
      throw new AppError(`Invalid mode. Allowed modes: ${validModes.join(', ')}`, 400);
    }

    const validEnhancements = ['brighten', 'sharpen', 'bw', 'grayscale', 'remove_shadow'];
    if (options.enhancement && !validEnhancements.includes(options.enhancement)) {
      throw new AppError(`Invalid enhancement. Allowed enhancements: ${validEnhancements.join(', ')}`, 400);
    }

    if (options.quality && (options.quality < 1 || options.quality > 100)) {
      throw new AppError('Quality must be between 1 and 100', 400);
    }
  }

  next();
};

export const validatePDFGeneration = (req: Request, res: Response, next: NextFunction): void => {
  const { imageIds, options } = req.body;

  if (!imageIds || !Array.isArray(imageIds) || imageIds.length === 0) {
    throw new AppError('Image IDs are required', 400);
  }

  if (options) {
    const validPageSizes = ['A4', 'Letter'];
    if (options.pageSize && !validPageSizes.includes(options.pageSize)) {
      throw new AppError(`Invalid page size. Allowed sizes: ${validPageSizes.join(', ')}`, 400);
    }

    const validOrientations = ['portrait', 'landscape'];
    if (options.orientation && !validOrientations.includes(options.orientation)) {
      throw new AppError(`Invalid orientation. Allowed orientations: ${validOrientations.join(', ')}`, 400);
    }

    if (options.margin && (options.margin < 0 || options.margin > 100)) {
      throw new AppError('Margin must be between 0 and 100', 400);
    }

    if (options.quality && (options.quality < 1 || options.quality > 100)) {
      throw new AppError('Quality must be between 1 and 100', 400);
    }
  }

  next();
};

export const validatePDFGenerationFromData = (req: Request, res: Response, next: NextFunction): void => {
  const { images, options } = req.body;

  if (!images || !Array.isArray(images) || images.length === 0) {
    throw new AppError('Images data is required', 400);
  }

  // Validate each image object
  for (const image of images) {
    if (!image.id || typeof image.id !== 'string') {
      throw new AppError('Each image must have a valid id', 400);
    }
    
    if (!image.data || typeof image.data !== 'string') {
      throw new AppError('Each image must have valid data', 400);
    }
    
    if (!image.originalName || typeof image.originalName !== 'string') {
      throw new AppError('Each image must have an originalName', 400);
    }
  }

  if (options) {
    const validPageSizes = ['A4', 'Letter'];
    if (options.pageSize && !validPageSizes.includes(options.pageSize)) {
      throw new AppError(`Invalid page size. Allowed sizes: ${validPageSizes.join(', ')}`, 400);
    }

    const validOrientations = ['portrait', 'landscape'];
    if (options.orientation && !validOrientations.includes(options.orientation)) {
      throw new AppError(`Invalid orientation. Allowed orientations: ${validOrientations.join(', ')}`, 400);
    }

    if (options.margin && (options.margin < 0 || options.margin > 100)) {
      throw new AppError('Margin must be between 0 and 100', 400);
    }

    if (options.quality && (options.quality < 1 || options.quality > 100)) {
      throw new AppError('Quality must be between 1 and 100', 400);
    }
  }

  next();
};