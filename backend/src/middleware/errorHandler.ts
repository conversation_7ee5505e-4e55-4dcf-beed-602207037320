import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { APIResponse } from '../types';

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500;
  let message = 'Internal Server Error';

  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation Error';
  } else if (error.name === 'MulterError') {
    statusCode = 400;
    message = `File upload error: ${error.message}`;
  } else if (error.name === 'PayloadTooLargeError' || error.message.includes('request entity too large')) {
    statusCode = 413;
    message = '请求数据过大，无法处理';
  }

  logger.error(`Error ${statusCode}: ${message}`, {
    url: req.url,
    method: req.method,
    stack: error.stack,
  });

  let response: APIResponse = {
    success: false,
    error: message,
    message: statusCode === 500 ? 'Something went wrong' : message,
  };

  // 为413错误提供更详细的信息
  if (statusCode === 413) {
    response = {
      success: false,
      error: 'Request too large',
      message: '请求数据过大，可能是因为图片数量太多或图片尺寸太大',
      code: 'PAYLOAD_TOO_LARGE',
      details: {
        reason: 'PDF生成请求中包含的图片数据超过了服务器限制',
        suggestion: '请尝试减少图片数量，或选择较小尺寸的图片，或分批生成PDF'
      }
    };
  }

  res.status(statusCode).json(response);
};

export const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

export default errorHandler;