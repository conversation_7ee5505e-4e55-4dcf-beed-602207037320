import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';

// 扩展Request接口以包含跟踪信息
declare global {
  namespace Express {
    interface Request {
      requestId: string;
      sessionId: string;
      startTime: number;
      trackingMeta: {
        ip: string;
        userAgent: string;
        method: string;
        path: string;
        origin?: string;
      };
    }
  }
}

/**
 * 请求跟踪中间件
 * 为每个请求分配唯一ID，记录基本信息，全程跟踪处理过程
 */
export const requestTracker = (req: Request, res: Response, next: NextFunction) => {
  // 生成或获取请求ID
  req.requestId = req.headers['x-correlation-id'] as string || uuidv4();
  
  // 获取或生成会话ID
  req.sessionId = req.headers['x-client-id'] as string || uuidv4();
  
  // 记录开始时间
  req.startTime = Date.now();
  
  // 统一日志路径为完整路由（与签名计算一致）
  const fullPath = (req.baseUrl || '') + req.path;

  // 收集基本跟踪信息
  req.trackingMeta = {
    ip: req.ip || req.connection.remoteAddress || 'unknown',
    userAgent: req.get('user-agent') || 'unknown',
    method: req.method,
    path: fullPath,
    origin: req.get('origin') || req.get('referer')
  };

  // 设置响应头
  res.set({
    'X-Request-ID': req.requestId,
    'X-Session-ID': req.sessionId
  });

  // 记录请求开始
  logger.info('Request started', {
    requestId: req.requestId,
    sessionId: req.sessionId,
    method: req.method,
    path: fullPath,
    ip: req.trackingMeta.ip,
    userAgent: req.trackingMeta.userAgent,
    origin: req.trackingMeta.origin,
    timestamp: new Date().toISOString(),
    hasFiles: !!(req as any).files,
    contentLength: req.get('content-length'),
    contentType: req.get('content-type')
  });

  // 拦截响应结束事件
  const originalSend = res.send;
  res.send = function(data: any) {
    const duration = Date.now() - req.startTime;
    
    // 记录请求完成
    logger.info('Request completed', {
      requestId: req.requestId,
      sessionId: req.sessionId,
      method: req.method,
      path: fullPath,
      statusCode: res.statusCode,
      duration,
      timestamp: new Date().toISOString(),
      responseSize: data ? JSON.stringify(data).length : 0,
      success: res.statusCode < 400
    });

    return originalSend.call(this, data);
  };

  next();
};

/**
 * 错误跟踪中间件
 * 记录请求处理过程中的错误
 */
export const errorTracker = (error: any, req: Request, res: Response, next: NextFunction) => {
  const duration = Date.now() - (req.startTime || Date.now());
  const fullPath = (req.baseUrl || '') + req.path;
  
  logger.error('Request error', {
    requestId: req.requestId || 'unknown',
    sessionId: req.sessionId || 'unknown',
    method: req.method,
    path: fullPath,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code
    },
    duration,
    timestamp: new Date().toISOString(),
    ip: req.trackingMeta?.ip,
    userAgent: req.trackingMeta?.userAgent
  });

  next(error);
};

/**
 * 阶段跟踪工具类
 * 用于跟踪请求处理的各个阶段
 */
export class StageTracker {
  private requestId: string;
  private sessionId: string;
  private stages: Map<string, { startTime: number; endTime?: number; metadata?: any }> = new Map();

  constructor(req: Request) {
    this.requestId = req.requestId || 'unknown';
    this.sessionId = req.sessionId || 'unknown';
  }

  /**
   * 开始一个处理阶段
   */
  startStage(stageName: string, metadata?: any): void {
    this.stages.set(stageName, {
      startTime: Date.now(),
      metadata
    });

    logger.debug('Stage started', {
      requestId: this.requestId,
      sessionId: this.sessionId,
      stage: stageName,
      metadata,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 结束一个处理阶段
   */
  endStage(stageName: string, result?: any, error?: any): void {
    const stage = this.stages.get(stageName);
    if (!stage) {
      logger.warn('Attempted to end non-existent stage', {
        requestId: this.requestId,
        sessionId: this.sessionId,
        stage: stageName
      });
      return;
    }

    const endTime = Date.now();
    const duration = endTime - stage.startTime;
    
    stage.endTime = endTime;
    this.stages.set(stageName, stage);

    logger.info('Stage completed', {
      requestId: this.requestId,
      sessionId: this.sessionId,
      stage: stageName,
      duration,
      success: !error,
      result: result ? (typeof result === 'object' ? Object.keys(result) : typeof result) : undefined,
      error: error ? {
        name: error.name,
        message: error.message
      } : undefined,
      metadata: stage.metadata,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录阶段中的事件
   */
  logEvent(stageName: string, eventName: string, data?: any): void {
    logger.debug('Stage event', {
      requestId: this.requestId,
      sessionId: this.sessionId,
      stage: stageName,
      event: eventName,
      data,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取处理摘要
   */
  getSummary(): any {
    const summary = {
      requestId: this.requestId,
      sessionId: this.sessionId,
      totalStages: this.stages.size,
      stages: {} as any,
      totalDuration: 0
    };

    let minStartTime = Infinity;
    let maxEndTime = 0;

    for (const [stageName, stage] of this.stages.entries()) {
      const duration = stage.endTime ? stage.endTime - stage.startTime : Date.now() - stage.startTime;
      
      summary.stages[stageName] = {
        duration,
        completed: !!stage.endTime,
        metadata: stage.metadata
      };

      minStartTime = Math.min(minStartTime, stage.startTime);
      if (stage.endTime) {
        maxEndTime = Math.max(maxEndTime, stage.endTime);
      }
    }

    summary.totalDuration = maxEndTime > 0 ? maxEndTime - minStartTime : Date.now() - minStartTime;

    return summary;
  }
}
