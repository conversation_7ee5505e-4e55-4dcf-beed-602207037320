import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { logger } from '../utils/logger';
import { getNacosService } from '../services/nacosService';
import DynamicKeyManager, { getDynamicKeyManager } from '../utils/dynamicKeyManager';

/**
 * CSRF保护中间件
 */
export class CSRFProtection {
  private static tokens = new Map<string, { token: string; expires: number }>();
  
  // 生成CSRF token
  static generateToken(sessionId: string): string {
    const token = crypto.randomBytes(32).toString('hex');
    const expires = Date.now() + (60 * 60 * 1000); // 1小时过期
    
    this.tokens.set(sessionId, { token, expires });
    
    // 定期清理过期token
    this.cleanup();
    
    return token;
  }
  
  // 验证CSRF token（支持增强token）
  static validateToken(sessionId: string, token: string): boolean {
    // 提取原始CSRF token（支持v1/v2增强token）
    let originalToken = token;
    if (!/^[a-f0-9]{64}$/i.test(originalToken)) {
      const extracted = DynamicKeyManager.extractOriginalCSRFFromEnhanced(token);
      if (extracted) {
        originalToken = extracted;
      } else if (token.includes('.')) {
        originalToken = token.split('.')[0];
      }
    }
    
    const storedData = this.tokens.get(sessionId);
    
    if (!storedData) {
      return false;
    }
    
    if (Date.now() > storedData.expires) {
      this.tokens.delete(sessionId);
      return false;
    }
    
    return storedData.token === originalToken;
  }
  
  // 清理过期token
  private static cleanup() {
    const now = Date.now();
    for (const [sessionId, data] of this.tokens.entries()) {
      if (now > data.expires) {
        this.tokens.delete(sessionId);
      }
    }
  }
  
  // 中间件：验证CSRF token
  static middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      // 检查是否为原生客户端请求，如果是，则跳过CSRF检查
      if (req.headers['x-client-type'] === 'mobile') {
        logger.debug('CSRF check skipped for mobile client', {
          requestId: (req as any).requestId,
          path: req.path,
        });
        return next();
      }

      // 只对POST/PUT/DELETE请求进行CSRF检查
      if (!['POST', 'PUT', 'DELETE'].includes(req.method)) {
        logger.debug('CSRF check skipped for GET request', {
          requestId: (req as any).requestId,
          method: req.method,
          path: req.path
        });
        return next();
      }
      
      // 在开发环境中可以暂时禁用CSRF检查
      if (process.env.NODE_ENV === 'development' && process.env.DISABLE_CSRF === 'true') {
        logger.debug('CSRF check disabled in development', {
          requestId: (req as any).requestId,
          method: req.method,
          path: req.path
        });
        return next();
      }
      
      const sessionId = req.headers['x-client-id'] as string;
      const csrfToken = req.headers['x-request-id'] as string;
      
      logger.debug('CSRF protection check started', {
        requestId: (req as any).requestId,
        sessionId: sessionId?.slice(0, 8),
        hasSessionId: !!sessionId,
        hasCSRFToken: !!csrfToken,
        method: req.method,
        path: req.path
      });
      
      if (!sessionId || !csrfToken) {
        logger.security('CSRF protection failed - missing headers', {
          requestId: (req as any).requestId,
          ip: req.ip,
          userAgent: req.get('user-agent'),
          method: req.method,
          path: req.path,
          hasSessionId: !!sessionId,
          hasCSRFToken: !!csrfToken,
          origin: req.get('origin')
        });
        
        return res.status(403).json({
          success: false,
          error: 'CSRF Protection Required',
          message: '请求缺少必要的安全验证信息'
        });
      }
      
      if (!this.validateToken(sessionId, csrfToken)) {
        logger.security('CSRF protection failed - invalid token', {
          requestId: (req as any).requestId,
          ip: req.ip,
          sessionId: sessionId?.slice(0, 8),
          userAgent: req.get('user-agent'),
          method: req.method,
          path: req.path,
          origin: req.get('origin')
        });
        
        return res.status(403).json({
          success: false,
          error: 'Invalid CSRF Token',
          message: '安全验证失败，请刷新页面后重试'
        });
      }
      
      logger.debug('CSRF protection passed', {
        requestId: (req as any).requestId,
        sessionId: sessionId?.slice(0, 8),
        method: req.method,
        path: req.path
      });
      
      next();
    };
  }
}

/**
 * 动态生成允许的来源列表
 */
export const getDynamicAllowedOrigins = (req: Request): string[] => {
  const baseOrigins = [
    'http://localhost:3000',
    'http://localhost:5173',
    'https://localhost:3000',
    'https://localhost:5173',
  ];
  
  // 在开发环境中，自动允许当前请求的host（但使用前端常用端口）
  if (process.env.NODE_ENV === 'development') {
    const requestHost = req.get('host');
    if (requestHost) {
      // 提取IP地址（去掉端口）
      const hostWithoutPort = requestHost.split(':')[0];
      
      // 如果是IP地址，添加前端常用端口的变体
      if (/^\d+\.\d+\.\d+\.\d+$/.test(hostWithoutPort)) {
        const frontendPorts = ['5173', '3000', '5174', '3001']; // 常见前端端口
        const protocols = ['http', 'https'];
        
        protocols.forEach(protocol => {
          frontendPorts.forEach(port => {
            baseOrigins.push(`${protocol}://${hostWithoutPort}:${port}`);
          });
        });
      }
    }
    
    // 从Origin头中动态提取并允许（仅开发环境）
    const origin = req.get('Origin');
    if (origin) {
      try {
        const url = new URL(origin);
        // 只允许本地网络的IP地址
        if (isLocalNetworkIP(url.hostname)) {
          baseOrigins.push(origin);
          // 记录自动添加的Origin
          if (process.env.LOG_LEVEL === 'debug') {
            console.log(`[DEBUG] Auto-added origin: ${origin} (local network IP: ${url.hostname})`);
          }
        }
      } catch (error) {
        // 忽略无效的Origin
      }
    }
  }
  
  // 在生产环境中，优先从Nacos读取，否则从环境变量读取允许的域名
  if (process.env.NODE_ENV === 'production') {
    try {
      const nacosService = getNacosService();
      if (nacosService.isReady()) {
        const securityConfig = nacosService.getSecurityConfig();
        if (securityConfig.allowedOrigins && securityConfig.allowedOrigins.length > 0) {
          baseOrigins.push(...securityConfig.allowedOrigins);
          logger.debug('Added allowed origins from Nacos', {
            count: securityConfig.allowedOrigins.length,
            origins: securityConfig.allowedOrigins
          });
        }
      } else {
        logger.warn('Nacos not ready, falling back to environment variables for allowed origins');
        const productionOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];
        baseOrigins.push(...productionOrigins);
      }
    } catch (error) {
      logger.error('Failed to get security config from Nacos, using environment variables', error);
      const productionOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];
      baseOrigins.push(...productionOrigins);
    }
  }
  
  return [...new Set(baseOrigins)]; // 去重
};

/**
 * 检查是否为本地网络IP
 */
const isLocalNetworkIP = (hostname: string): boolean => {
  // localhost
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return true;
  }
  
  // 检查是否为私有网络IP地址
  const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
  const match = hostname.match(ipRegex);
  
  if (!match) {
    return false;
  }
  
  const [, a, b, c, d] = match.map(Number);
  
  // 验证每个段都在0-255范围内
  if (a > 255 || b > 255 || c > 255 || d > 255) {
    return false;
  }
  
  // 检查私有网络地址段
  return (
    (a === 10) ||                                    // 10.0.0.0/8
    (a === 172 && b >= 16 && b <= 31) ||            // **********/12
    (a === 192 && b === 168) ||                     // ***********/16
    (a === 169 && b === 254) ||                     // ***********/16 (link-local)
    (a === 127)                                      // *********/8 (loopback)
  );
};

/**
 * 轻量级验证 - 用于CSRF token端点
 * 只验证基本的浏览器请求，对来源更加宽松
 */
export const validateBrowserRequest = (req: Request, res: Response, next: NextFunction) => {
  const userAgent = req.get('User-Agent') || '';
  
  // 检查User-Agent是否看起来像浏览器
  const browserUserAgents = [
    'Mozilla', 'Chrome', 'Safari', 'Firefox', 'Edge', 'Opera'
  ];
  
  const isBrowserRequest = browserUserAgents.some(browser => 
    userAgent.includes(browser)
  );
  
  logger.debug('Browser request validation', {
    requestId: (req as any).requestId,
    isBrowserRequest,
    userAgent: userAgent?.slice(0, 50),
    method: req.method,
    path: req.path
  });

  if (!isBrowserRequest) {
    logger.security('Browser request validation failed - non-browser request', {
      requestId: (req as any).requestId,
      ip: req.ip,
      userAgent,
      method: req.method,
      path: req.path
    });
    
    return res.status(403).json({
      success: false,
      error: 'Invalid Request Source',
      message: '请使用浏览器访问此服务'
    });
  }
  
  logger.debug('Browser request validation passed', {
    requestId: (req as any).requestId,
    method: req.method,
    path: req.path
  });
  
  next();
};

/**
 * 请求来源验证中间件
 */
export const validateRequestOrigin = (req: Request, res: Response, next: NextFunction) => {
  // Skip validation for mobile clients
  if (req.headers['x-client-type'] === 'mobile') {
    logger.debug('Request origin validation skipped for mobile client', {
      requestId: (req as any).requestId,
      path: req.path,
    });
    return next();
  }

  // In development, we can be more permissive for ease of use with local IPs
  if (process.env.NODE_ENV === 'development') {
    logger.debug('Skipping request origin validation in development mode', {
      requestId: (req as any).requestId,
      origin: req.get('Origin') || req.get('Referer')
    });
    return next();
  }

  const allowedOrigins = getDynamicAllowedOrigins(req);
  
  const origin = req.get('Origin') || req.get('Referer');
  const userAgent = req.get('User-Agent') || '';
  
  // 检查User-Agent是否看起来像浏览器
  const browserUserAgents = [
    'Mozilla', 'Chrome', 'Safari', 'Firefox', 'Edge', 'Opera'
  ];
  
  const isBrowserRequest = browserUserAgents.some(browser => 
    userAgent.includes(browser)
  );
  
  logger.debug('Request origin validation started', {
    requestId: (req as any).requestId,
    origin,
    userAgent: userAgent?.slice(0, 50),
    isBrowserRequest,
    method: req.method,
    path: req.path,
    allowedOrigins: process.env.NODE_ENV === 'development' ? allowedOrigins : allowedOrigins.length,
    isDevelopment: process.env.NODE_ENV === 'development'
  });

  if (!isBrowserRequest) {
    logger.security('Request origin validation failed - non-browser request', {
      requestId: (req as any).requestId,
      ip: req.ip,
      userAgent,
      method: req.method,
      path: req.path,
      origin
    });
    
    return res.status(403).json({
      success: false,
      error: 'Invalid Request Source',
      message: '请使用浏览器访问此服务'
    });
  }
  
  // 检查来源域名（仅在有Origin/Referer时检查）
  if (origin) {
    const originDomain = new URL(origin).origin;
    
    // 标准化处理：将所有origin都去掉末尾斜杠进行比较
    const normalizedOrigin = originDomain.replace(/\/$/, '');
    const normalizedAllowedOrigins = allowedOrigins.map(o => o.replace(/\/$/, ''));
    
    if (!normalizedAllowedOrigins.includes(normalizedOrigin)) {
      logger.security('Request origin validation failed - unauthorized domain', {
        requestId: (req as any).requestId,
        ip: req.ip,
        origin: originDomain,
        allowedOrigins,
        userAgent: userAgent?.slice(0, 50),
        method: req.method,
        path: req.path
      });
      
      return res.status(403).json({
        success: false,
        error: 'Unauthorized Origin',
        message: '请求来源未授权'
      });
    }
  }
  
  logger.debug('Request origin validation passed', {
    requestId: (req as any).requestId,
    origin: origin ? new URL(origin).origin : 'none',
    isBrowserRequest,
    method: req.method,
    path: req.path,
    matchedFromDynamic: process.env.NODE_ENV === 'development' && origin && isLocalNetworkIP(new URL(origin).hostname)
  });
  
  next();
};

/**
 * 基础安全头设置
 */
export const setSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // 设置安全相关的HTTP头
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; frame-ancestors https://*.apusai.com https://*.apuscn.com;",
  });
  
  next();
};

/**
 * 生成会话ID的工具函数
 */
export const generateSessionId = (): string => {
  return crypto.randomBytes(16).toString('hex');
};

/**
 * API端点：获取CSRF token
 * 增强安全检查：验证请求来源
 */
export const getCSRFToken = (req: Request, res: Response) => {
  // 安全检查1: 验证Referer头（防止跨站获取token）
  const referer = req.get('Referer');
  const origin = req.get('Origin');
  
  // 在生产环境中，必须有合法的Referer或Origin
  if (process.env.NODE_ENV === 'production') {
    if (!referer && !origin) {
      logger.security('CSRF token request without referer/origin blocked', {
        ip: req.ip,
        userAgent: req.get('user-agent'),
        requestId: (req as any).requestId
      });
      
      return res.status(403).json({
        success: false,
        error: 'Invalid Request',
        message: '请从正确的页面访问'
      });
    }
    
    // 验证来源域名
    const requestOrigin = origin || (referer ? new URL(referer).origin : '');
    const allowedOrigins = getDynamicAllowedOrigins(req);
    
    if (!allowedOrigins.includes(requestOrigin)) {
      logger.security('CSRF token request from unauthorized origin', {
        ip: req.ip,
        origin: requestOrigin,
        allowedOrigins: allowedOrigins,
        userAgent: req.get('user-agent'),
        requestId: (req as any).requestId
      });
      
      return res.status(403).json({
        success: false,
        error: 'Unauthorized Origin',
        message: '请求来源未授权'
      });
    }
  }
  
  const sessionId = req.headers['x-client-id'] as string || generateSessionId();
  const csrfToken = CSRFProtection.generateToken(sessionId);
  
  // 获取动态密钥管理器并嵌入HMAC密钥到CSRF token
  const keyManager = getDynamicKeyManager();
  const enhancedToken = keyManager.embedKeyInToken(sessionId, csrfToken);
  
  logger.debug('Enhanced CSRF token generated with embedded HMAC key', {
    sessionId: sessionId.slice(0, 8),
    origin: origin || 'none',
    referer: referer || 'none',
    requestId: (req as any).requestId,
    originalTokenLength: csrfToken.length,
    enhancedTokenLength: enhancedToken.length
  });
  
  // 禁止缓存（防止客户端/CDN缓存过期的握手票据）
  res.set({
    'Cache-Control': 'no-store, no-cache, must-revalidate',
    'Pragma': 'no-cache',
    'Vary': 'Origin'
  });

  res.json({
    success: true,
    sessionId,
    handshake: enhancedToken, // 返回混淆后的会话握手票据
    expires: Date.now() + (60 * 60 * 1000)
  });
};
