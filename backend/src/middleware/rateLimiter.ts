import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { getNacosService } from '../services/nacosService';

interface RateLimitConfig {
  windowMs: number; // 时间窗口(毫秒)
  maxRequests: number; // 最大请求数
  message: string; // 超限提示消息
  skipSuccessfulRequests?: boolean; // 是否跳过成功请求
  blockDuration?: number; // IP封锁时长(毫秒)，超过限制后封锁IP多长时间
}

interface ClientInfo {
  count: number;
  resetTime: number;
  blockedUntil?: number; // IP封锁到什么时候（毫秒时间戳）
}

class RateLimiter {
  private store: Map<string, ClientInfo> = new Map();
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
    
    // 定期清理过期数据
    setInterval(() => {
      this.cleanup();
    }, this.config.windowMs);
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, client] of this.store.entries()) {
      if (now > client.resetTime) {
        this.store.delete(key);
      }
    }
  }

  private getKey(req: Request): string {
    // 使用IP + User-Agent的组合作为键，提高准确性
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('user-agent') || 'unknown';
    return `${ip}:${Buffer.from(userAgent).toString('base64').slice(0, 10)}`;
  }

  middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const key = this.getKey(req);
      const now = Date.now();
      
      let client = this.store.get(key);
      
      // 检查是否处于封锁状态
      if (client?.blockedUntil && now < client.blockedUntil) {
        const remainingBlockTime = Math.ceil((client.blockedUntil - now) / 1000);
        
        logger.security('Request blocked - IP still under block period', {
          requestId: (req as any).requestId,
          ip: req.ip,
          userAgent: req.get('user-agent'),
          endpoint: req.path,
          method: req.method,
          blockedUntil: new Date(client.blockedUntil).toISOString(),
          remainingSeconds: remainingBlockTime
        });

        res.status(429).json({
          success: false,
          error: 'IP Blocked',
          message: `IP被临时封锁，请在${remainingBlockTime}秒后重试`,
          blockedUntil: new Date(client.blockedUntil).toISOString(),
          retryAfter: remainingBlockTime
        });
        return;
      }
      
      if (!client || now > client.resetTime) {
        // 新客户端或窗口重置（但保留封锁状态）
        client = {
          count: 1,
          resetTime: now + this.config.windowMs,
          blockedUntil: client?.blockedUntil // 保留封锁状态
        };
        this.store.set(key, client);
      } else {
        client.count++;
      }

      // 设置响应头
      res.set({
        'X-RateLimit-Limit': this.config.maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(0, this.config.maxRequests - client.count).toString(),
        'X-RateLimit-Reset': new Date(client.resetTime).toISOString(),
        'X-RateLimit-Blocked': client.blockedUntil ? 'true' : 'false'
      });

      if (client.count > this.config.maxRequests) {
        // 如果配置了封锁时长，则封锁IP
        if (this.config.blockDuration && this.config.blockDuration > 0) {
          client.blockedUntil = now + this.config.blockDuration;
          
          logger.security('Rate limit exceeded - IP blocked for abuse', {
            requestId: (req as any).requestId,
            sessionId: (req as any).sessionId,
            ip: req.ip,
            userAgent: req.get('user-agent'),
            endpoint: req.path,
            method: req.method,
            count: client.count,
            limit: this.config.maxRequests,
            windowMs: this.config.windowMs,
            blockDuration: this.config.blockDuration,
            blockedUntil: new Date(client.blockedUntil).toISOString()
          });

          res.status(429).json({
            success: false,
            error: 'Rate Limit Exceeded - IP Blocked',
            message: `请求过于频繁，IP已被临时封锁 ${Math.ceil(this.config.blockDuration / 1000)} 秒`,
            blockedUntil: new Date(client.blockedUntil).toISOString(),
            retryAfter: Math.ceil(this.config.blockDuration / 1000)
          });
          return;
        }
        logger.security('Rate limit exceeded - potential abuse detected', {
          requestId: (req as any).requestId,
          sessionId: (req as any).sessionId,
          ip: req.ip,
          userAgent: req.get('user-agent'),
          endpoint: req.path,
          method: req.method,
          count: client.count,
          limit: this.config.maxRequests,
          windowMs: this.config.windowMs,
          resetTime: new Date(client.resetTime).toISOString()
        });

        res.status(429).json({
          success: false,
          error: 'Too Many Requests',
          message: this.config.message,
          retryAfter: Math.ceil((client.resetTime - now) / 1000)
        });
        return;
      }

      // 记录通过率限检查的请求（仅在调试模式）
      if (process.env.NODE_ENV === 'development') {
        logger.debug('Rate limit check passed', {
          requestId: (req as any).requestId,
          ip: req.ip,
          endpoint: req.path,
          currentCount: client.count,
          limit: this.config.maxRequests,
          remaining: this.config.maxRequests - client.count
        });
      }

      next();
    };
  }
}

// 预定义的限制器
export const createRateLimiter = (config: RateLimitConfig) => new RateLimiter(config);

/**
 * 获取Nacos配置的Rate Limiter，回退到默认配置
 */
const getRateLimiterConfig = (
  limiterType: 'general' | 'fileUpload' | 'imageProcessing' | 'pdfGeneration' | 'externalApiCalls' | 'perIpStrict',
  defaultConfig: RateLimitConfig
): RateLimitConfig => {
  try {
    const nacosService = getNacosService();
    logger.debug(`Checking Nacos service for ${limiterType} rate limiter config`, {
      limiterType,
      nacosReady: nacosService.isReady(),
      nacosStatus: nacosService.getStatus()
    });
    
    if (nacosService.isReady()) {
      const securityConfig = nacosService.getSecurityConfig();
      logger.debug(`Retrieved security config for ${limiterType}`, {
        limiterType,
        hasRateLimiting: !!securityConfig.rateLimiting,
        hasTargetConfig: !!securityConfig.rateLimiting?.[limiterType],
        targetConfigEnabled: securityConfig.rateLimiting?.[limiterType]?.enabled,
        allRateLimitingKeys: securityConfig.rateLimiting ? Object.keys(securityConfig.rateLimiting) : []
      });
      
      const rateLimitConfig = securityConfig.rateLimiting?.[limiterType];
      
      if (rateLimitConfig?.enabled) {
        logger.debug(`Using Nacos rate limit config for ${limiterType}`, {
          windowMs: rateLimitConfig.windowMs,
          maxRequests: rateLimitConfig.maxRequests,
          hasBlockDuration: 'blockDuration' in rateLimitConfig
        });
        
        const dynamicConfig = {
          ...defaultConfig,
          windowMs: rateLimitConfig.windowMs,
          maxRequests: rateLimitConfig.maxRequests
        };
        
        // 如果配置中有blockDuration，则添加到配置中
        if ('blockDuration' in rateLimitConfig && rateLimitConfig.blockDuration) {
          dynamicConfig.blockDuration = rateLimitConfig.blockDuration;
        }
        
        return dynamicConfig;
      } else {
        logger.debug(`Rate limit config for ${limiterType} is disabled or missing`, {
          limiterType,
          configExists: !!rateLimitConfig,
          enabled: rateLimitConfig?.enabled,
          usingDefaults: true
        });
      }
    } else {
      logger.debug(`Nacos service not ready for ${limiterType}, using defaults`, {
        limiterType,
        nacosStatus: nacosService.getStatus()
      });
    }
  } catch (error) {
    logger.warn(`Failed to get Nacos rate limit config for ${limiterType}, using defaults`, {
      limiterType,
      error: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined
    });
  }
  
  return defaultConfig;
};

/**
 * 获取动态配置的限制器工厂函数
 */
export const createDynamicLimiter = (
  limiterType: 'general' | 'fileUpload' | 'imageProcessing' | 'pdfGeneration' | 'externalApiCalls' | 'perIpStrict',
  defaultConfig: RateLimitConfig
) => {
  // 缓存限制器实例，每小时重新创建以适应配置变化
  let cachedLimiter: RateLimiter | null = null;
  let lastCreated = 0;
  const cacheTimeout = 60 * 60 * 1000; // 1小时
  
  return {
    middleware: () => {
      return (req: Request, res: Response, next: NextFunction) => {
        const now = Date.now();
        
        // 如果缓存过期或不存在，重新创建限制器
        if (!cachedLimiter || (now - lastCreated) > cacheTimeout) {
          const config = getRateLimiterConfig(limiterType, defaultConfig);
          cachedLimiter = createRateLimiter(config);
          lastCreated = now;
          
          logger.debug(`Created new rate limiter for ${limiterType}`, {
            windowMs: config.windowMs,
            maxRequests: config.maxRequests,
            cacheExpired: (now - lastCreated) > cacheTimeout
          });
        }
        
        return cachedLimiter.middleware()(req, res, next);
      };
    }
  };
};

// 通用API限制器
export const generalApiLimiter = createDynamicLimiter('general', {
  windowMs: 15 * 60 * 1000,
  maxRequests: 100,
  message: '请求过于频繁，请稍后再试'
});

// 图片处理限制器
export const imageProcessingLimiter = createDynamicLimiter('imageProcessing', {
  windowMs: 60 * 1000,
  maxRequests: 30,
  message: '图片处理请求过于频繁，请稍后再试'
});

// PDF生成限制器
export const pdfGenerationLimiter = createDynamicLimiter('pdfGeneration', {
  windowMs: 5 * 60 * 1000,
  maxRequests: 10,
  message: 'PDF生成请求过于频繁，请稍后再试'
});

// 文件上传限制器
export const fileUploadLimiter = createDynamicLimiter('fileUpload', {
  windowMs: 60 * 1000,
  maxRequests: 20,
  message: '文件上传请求过于频繁，请稍后再试'
});

// 外部API调用限制器  
export const externalApiLimiter = createDynamicLimiter('externalApiCalls', {
  windowMs: 60 * 1000,
  maxRequests: 50,
  message: 'API调用过于频繁，请稍后再试'
});

// 严格IP限制器 - 带IP封锁功能
export const strictIpLimiter = createDynamicLimiter('perIpStrict', {
  windowMs: 60 * 1000,
  maxRequests: 300,
  message: 'IP请求频率过高，已被临时封锁',
  blockDuration: 5 * 60 * 1000 // 5分钟封锁
});

// 会话级限制器 - 基于更严格的限制
export const createSessionLimiter = (sessionId: string) => {
  const sessionLimiters = new Map<string, RateLimiter>();
  
  if (!sessionLimiters.has(sessionId)) {
    sessionLimiters.set(sessionId, createRateLimiter({
      windowMs: 60 * 60 * 1000, // 1小时
      maxRequests: 50, // 每小时最多50张图片
      message: '您在1小时内处理的图片数量已达上限，请稍后再试'
    }));
  }
  
  return sessionLimiters.get(sessionId)!;
};