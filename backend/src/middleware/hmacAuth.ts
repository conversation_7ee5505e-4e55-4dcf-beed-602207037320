import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { logger } from '../utils/logger';
import { getDynamicKeyManager, DynamicKey } from '../utils/dynamicKeyManager';

/**
 * HMAC参数校验中间件
 * 防止API被程序化攻击，只允许拥有密钥的前端调用
 */

export interface HMACAuthConfig {
  secretKey: string;
  timestampTolerance?: number; // 时间戳容忍度(秒)，防重放攻击
  requiredParams?: string[];   // 必须参与签名的参数
}

export class HMACAuth {
  private config: HMACAuthConfig;

  constructor(config: HMACAuthConfig) {
    this.config = {
      timestampTolerance: 300, // 默认5分钟
      requiredParams: [],
      ...config
    };
  }

  /**
   * 生成HMAC签名
   * @param params 请求参数
   * @param timestamp 时间戳
   * @param method HTTP方法
   * @param path 请求路径
   */
  generateSignature(params: Record<string, any>, timestamp: number, method: string, path: string): string {
    // 1. 构建签名字符串
    const signString = this.buildSignString(params, timestamp, method, path);
    
    // 2. 使用HMAC-SHA256生成签名
    const signature = crypto
      .createHmac('sha256', this.config.secretKey)
      .update(signString)
      .digest('hex');
    
    logger.debug('HMAC signature generated', {
      signString: signString.substring(0, 100) + '...',
      signature: signature.substring(0, 16) + '...'
    });
    
    return signature;
  }

  /**
   * 构建签名字符串
   */
  private buildSignString(params: Record<string, any>, timestamp: number, method: string, path: string): string {
    // 1. 获取所有参数（包括必需参数）
    const allParams = { ...params };
    
    // 2. 添加系统参数
    allParams._timestamp = timestamp;
    allParams._method = method.toUpperCase();
    allParams._path = path;
    
    // 3. 按键名排序
    const sortedKeys = Object.keys(allParams).sort();
    
    // 4. 构建键值对字符串
    const paramString = sortedKeys
      .map(key => `${key}=${this.stringify(allParams[key])}`)
      .join('&');
    
    return paramString;
  }

  /**
   * 参数值标准化为字符串
   */
  private stringify(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  /**
   * 验证HMAC签名（支持动态密钥）
   */
  validateSignature(req: Request): boolean {
    try {
      // 1. 获取通用请求头
      const clientSignature = req.headers['x-auth-token'] as string;
      const clientTimestamp = parseInt(req.headers['x-timestamp'] as string, 10);
      const clientNonce = req.headers['x-trace-id'] as string;
      const sessionId = req.headers['x-client-id'] as string;
      const clientType = req.headers['x-client-type'] as string;

      if (!clientSignature || !clientTimestamp || !clientNonce || !sessionId) {
        logger.debug('HMAC validation failed - missing required headers', {
            hasSignature: !!clientSignature,
            hasTimestamp: !!clientTimestamp,
            hasNonce: !!clientNonce,
            hasSessionId: !!sessionId,
        });
        return false;
      }

      // 2. 校验时间戳，防重放攻击
      const now = Math.floor(Date.now() / 1000);
      const timeDiff = Math.abs(now - clientTimestamp);
      if (timeDiff > this.config.timestampTolerance!) {
        logger.security('HMAC validation failed - timestamp out of range', {
            clientTimestamp,
            serverTimestamp: now,
            timeDiff,
            tolerance: this.config.timestampTolerance,
        });
        return false;
      }

      // 3. 构建用于签名的参数
      let params: any = { _nonce: clientNonce };
      const contentType = req.get('content-type') || '';
      if (contentType.includes('multipart/form-data')) {
        if (req.body.options && typeof req.body.options === 'string') {
          try {
            params.options = JSON.parse(req.body.options);
          } catch (e) {
            logger.warn('Failed to parse options from body for HMAC', { error: e });
            params.options = {};
          }
        } else if (req.query.options && typeof req.query.options === 'string') {
            try {
                params.options = JSON.parse(req.query.options as string);
            } catch (e) {
                logger.warn('Failed to parse options from query for HMAC', { error: e });
                params.options = {};
            }
        } else {
          params.options = {};
        }

        // Handle autoProcess parameter from multipart form
        if (req.body.autoProcess !== undefined) {
          params.autoProcess = req.body.autoProcess;
        }

        // The client-side signing logic for file uploads signs an empty `files` array.
        params.files = [];
      } else {
        // For regular JSON requests, combine query and body.
        params = { ...params, ...req.query, ...req.body };
      }

      // 4. 根据客户端类型，采用不同策略获取HMAC密钥并验证
      const keyManager = getDynamicKeyManager();
      let hmacKey: DynamicKey | null = null;

      if (clientType === 'mobile') {
        // 原生客户端：直接通过 sessionId 获取密钥
        logger.debug('Mobile client detected, using direct session key for HMAC validation');
        hmacKey = keyManager.getSessionKey(sessionId);
      } else {
        // Web 客户端：从 x-request-id (handshake token) 中提取密钥
        logger.debug('Web client detected, extracting key from CSRF token');
        const csrfToken = req.headers['x-request-id'] as string;
        if (!csrfToken) {
          logger.warn('Web client HMAC validation failed: x-request-id header is missing');
          return false;
        }
        hmacKey = keyManager.extractKeyFromToken(sessionId, csrfToken);
      }

      if (!hmacKey) {
        logger.warn('HMAC validation failed: Could not retrieve a valid key for the session', { sessionId: sessionId.slice(0, 8), clientType });
        return false;
      }

      // 5. 生成服务端签名并进行安全比较
      const fullPath = (req.baseUrl || '') + req.path;
      const serverSignature = this.generateSignatureWithKey(hmacKey.key, params, clientTimestamp, req.method, fullPath);
      const isValid = crypto.timingSafeEqual(Buffer.from(clientSignature, 'hex'), Buffer.from(serverSignature, 'hex'));

      if (!isValid) {
        logger.security('HMAC validation failed: Signatures do not match', { clientType: clientType || 'web' });
      }

      return isValid;

    } catch (error) {
      logger.error('HMAC validation error', { error, requestId: (req as any).requestId });
      return false;
    }
  }

  /**
   * 使用动态密钥验证签名
   */
  private validateWithDynamicKey(
    params: Record<string, any>, 
    clientSignature: string, 
    timestamp: number, 
    method: string, 
    path: string, 
    sessionId: string,
    csrfToken?: string
  ): boolean {
    try {
      const keyManager = getDynamicKeyManager();
      
      logger.debug('Attempting dynamic key validation', {
        hasSessionId: !!sessionId,
        hasCsrfToken: !!csrfToken,
        csrfTokenFormat: csrfToken ? (csrfToken.includes('.') ? 'enhanced' : 'simple') : 'none'
      });
      
      // 在计算签名前，输出服务端使用的规范化签名字符串，方便与前端对比
      try {
        const canonical = this.buildSignString(params, timestamp, method, path);
        logger.debug('HMAC canonical sign string (server)', {
          method,
          path,
          timestamp,
          preview: canonical.substring(0, 160) + '...',
        });
      } catch (e) {
        logger.warn('Failed to build canonical sign string for debug', {
          error: e instanceof Error ? e.message : String(e)
        });
      }

      // 尝试从CSRF token中提取密钥
      if (csrfToken && csrfToken.includes('.')) {
        const extractedKey = keyManager.extractKeyFromToken(sessionId, csrfToken);
        if (extractedKey) {
          logger.debug('Extracted dynamic key successfully', {
            sessionId: sessionId.slice(0, 8),
            keyVersion: extractedKey.version,
            keyLength: extractedKey.key.length
          });
          
          const dynamicSignature = this.generateSignatureWithKey(
            extractedKey.key, params, timestamp, method, path
          );
          
          logger.debug('Generated dynamic signature', {
            clientSignature: clientSignature.substring(0, 16) + '...',
            dynamicSignature: dynamicSignature.substring(0, 16) + '...'
          });
          
          const isValid = crypto.timingSafeEqual(
            Buffer.from(clientSignature, 'hex'),
            Buffer.from(dynamicSignature, 'hex')
          );
          
          if (isValid) {
            logger.debug('HMAC signature verified with dynamic key from token', {
              sessionId: sessionId.slice(0, 8),
              keyVersion: extractedKey.version,
              method,
              path
            });
            return true;
          }
        }
      }
      
      // 备选：使用会话密钥验证
      const sessionKey = keyManager.getSessionKey(sessionId);
      const sessionSignature = this.generateSignatureWithKey(
        sessionKey.key, params, timestamp, method, path
      );
      
      const isValidSession = crypto.timingSafeEqual(
        Buffer.from(clientSignature, 'hex'),
        Buffer.from(sessionSignature, 'hex')
      );
      
      if (isValidSession) {
        logger.debug('HMAC signature verified with session key', {
          sessionId: sessionId.slice(0, 8),
          keyVersion: sessionKey.version,
          method,
          path
        });
        return true;
      }
      
      return false;
    } catch (error) {
      logger.warn('Dynamic HMAC verification failed', {
        sessionId: sessionId.slice(0, 8),
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 使用指定密钥生成签名
   */
  private generateSignatureWithKey(
    secretKey: string, 
    params: Record<string, any>, 
    timestamp: number, 
    method: string, 
    path: string
  ): string {
    const signString = this.buildSignString(params, timestamp, method, path);
    return crypto
      .createHmac('sha256', secretKey)
      .update(signString)
      .digest('hex');
  }

  /**
   * 中间件：验证HMAC签名
   */
  middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      // 在开发环境中可以暂时禁用HMAC检查
      if (process.env.NODE_ENV === 'development' && process.env.DISABLE_HMAC === 'true') {
        logger.debug('HMAC check disabled in development', {
          requestId: (req as any).requestId,
          method: req.method,
          path: req.path
        });
        return next();
      }

      const isValid = this.validateSignature(req);
      
      if (!isValid) {
        logger.security('HMAC authentication failed', {
          requestId: (req as any).requestId,
          ip: req.ip,
          userAgent: req.get('user-agent'),
          method: req.method,
          path: (req.baseUrl || '') + req.path,
          origin: req.get('origin')
        });

        return res.status(401).json({
          success: false,
          error: 'Authentication Failed',
          message: '请求签名验证失败'
        });
      }

      next();
    };
  }
}

/**
 * 前端签名生成工具函数（供前端使用）
 * 注意：实际使用时需要将secretKey安全地传递给前端
 */
export const generateClientSignature = (
  secretKey: string,
  params: Record<string, any>,
  method: string,
  path: string,
  nonce: string
): { signature: string; timestamp: number } => {
  const timestamp = Math.floor(Date.now() / 1000);
  const hmacAuth = new HMACAuth({ secretKey });
  
  const paramsWithNonce = {
    ...params,
    _nonce: nonce
  };
  
  const signature = hmacAuth.generateSignature(paramsWithNonce, timestamp, method, path);
  
  return { signature, timestamp };
};

/**
 * 生成随机nonce
 */
export const generateNonce = (): string => {
  return crypto.randomBytes(16).toString('hex');
};

import { getNacosService } from '../services/nacosService';

/**
 * 获取动态HMAC配置
 */
const getHMACConfig = (): HMACAuthConfig => {
  // 默认配置
  const defaultConfig: HMACAuthConfig = {
    secretKey: process.env.HMAC_SECRET_KEY || 'default-dev-key-change-in-production',
    timestampTolerance: 300, // 5分钟
    requiredParams: []
  };

  try {
    const nacosService = getNacosService();
    if (nacosService.isReady()) {
      const securityConfig = nacosService.getSecurityConfig();
      const hmacConfig = securityConfig.hmacAuth;
      
      if (hmacConfig?.enabled) {
        logger.debug('Using Nacos HMAC configuration', {
          timestampTolerance: hmacConfig.timestampTolerance || defaultConfig.timestampTolerance,
          hasSecretKey: !!hmacConfig.secretKey,
          requiredParamsCount: hmacConfig.requiredParams?.length || 0
        });
        
        return {
          secretKey: hmacConfig.secretKey,
          timestampTolerance: hmacConfig.timestampTolerance || defaultConfig.timestampTolerance,
          requiredParams: hmacConfig.requiredParams || defaultConfig.requiredParams
        };
      }
    }
  } catch (error) {
    logger.warn('Failed to get Nacos HMAC config, using defaults', error);
  }
  
  return defaultConfig;
};

/**
 * 动态HMAC认证中间件
 */
export const createDynamicHMACAuth = () => {
  // 缓存HMAC实例，每小时重新创建以适应配置变化
  let cachedAuth: HMACAuth | null = null;
  let lastCreated = 0;
  const cacheTimeout = 60 * 60 * 1000; // 1小时
  
  return {
    middleware: () => {
      return (req: Request, res: Response, next: NextFunction) => {
        const now = Date.now();
        
        // 如果缓存过期或不存在，重新创建HMAC认证实例
        if (!cachedAuth || (now - lastCreated) > cacheTimeout) {
          const config = getHMACConfig();
          cachedAuth = new HMACAuth(config);
          lastCreated = now;
          
          logger.debug('Created new HMAC auth instance', {
            timestampTolerance: config.timestampTolerance,
            cacheExpired: (now - lastCreated) > cacheTimeout
          });
        }
        
        return cachedAuth.middleware()(req, res, next);
      };
    }
  };
};

// 创建动态HMAC认证实例
export const hmacAuth = createDynamicHMACAuth();

export default HMACAuth;
