import multer from 'multer';
import { Request, RequestHandler } from 'express';
import config from '../utils/config';
import { logger } from '../utils/logger';

// Memory storage for file uploads
const storage = multer.memoryStorage();

// File filter function
const fileFilter = (
  req: Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
): void => {
  logger.debug(`File filter check: ${file.originalname}`, {
    mimetype: file.mimetype,
    fieldname: file.fieldname,
  });

  // Allowed MIME types
  const allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/bmp',
    'image/webp',
    'image/tiff',
    'image/gif',
  ];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    logger.warn(`Rejected file with unsupported MIME type: ${file.mimetype}`, {
      filename: file.originalname,
    });
    
    cb(new Error(`Unsupported file type: ${file.mimetype}. Allowed types: ${allowedMimeTypes.join(', ')}`));
  }
};

// Create multer instance
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.storage.maxFileSize, // 50MB per file
    files: config.storage.maxFilesPerRequest, // Max files per request
  },
});

// Middleware for multiple file uploads
export const uploadMultiple: RequestHandler = upload.array('files', config.storage.maxFilesPerRequest);

// Error handler for multer errors
export const handleUploadError = (error: any, req: Request, res: any, next: any): void => {
  if (error instanceof multer.MulterError) {
    logger.error('Multer upload error', {
      code: error.code,
      message: error.message,
      field: error.field,
    });

    let message = 'File upload error';
    let statusCode = 400;

    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        message = `File too large. Maximum size is ${config.storage.maxFileSize} bytes`;
        break;
      case 'LIMIT_FILE_COUNT':
        message = `Too many files. Maximum ${config.storage.maxFilesPerRequest} files allowed`;
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = 'Unexpected file field';
        break;
      case 'LIMIT_PART_COUNT':
        message = 'Too many parts in multipart form';
        break;
      case 'LIMIT_FIELD_KEY':
        message = 'Field name too long';
        break;
      case 'LIMIT_FIELD_VALUE':
        message = 'Field value too long';
        break;
      case 'LIMIT_FIELD_COUNT':
        message = 'Too many fields';
        break;
    }

    return res.status(statusCode).json({
      success: false,
      error: message,
      code: error.code,
    });
  }

  // For other types of errors
  if (error) {
    logger.error('Upload error', error);
    return res.status(400).json({
      success: false,
      error: error.message || 'Upload failed',
    });
  }

  next();
};

export default upload;