import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { getNacosService } from '../services/nacosService';

// 图片文件魔数映射
const IMAGE_MAGIC_NUMBERS = {
  'image/jpeg': [
    [0xFF, 0xD8, 0xFF], // JPEG
  ],
  'image/png': [
    [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A], // PNG
  ],
  'image/gif': [
    [0x47, 0x49, 0x46, 0x38, 0x37, 0x61], // GIF87a
    [0x47, 0x49, 0x46, 0x38, 0x39, 0x61], // GIF89a
  ],
  'image/webp': [
    [0x52, 0x49, 0x46, 0x46], // RIFF (需要进一步检查WEBP标识)
  ],
  'image/bmp': [
    [0x42, 0x4D], // BM
  ],
  'image/tiff': [
    [0x49, 0x49, 0x2A, 0x00], // II*\0 (little endian)
    [0x4D, 0x4D, 0x00, 0x2A], // MM\0* (big endian)
  ],
};

/**
 * 检查文件头是否匹配指定的魔数
 */
function checkMagicNumber(buffer: Buffer, magicNumbers: number[]): boolean {
  if (buffer.length < magicNumbers.length) {
    return false;
  }
  
  for (let i = 0; i < magicNumbers.length; i++) {
    if (buffer[i] !== magicNumbers[i]) {
      return false;
    }
  }
  
  return true;
}

/**
 * 验证文件是否为有效的图片格式
 */
function validateImageFormat(buffer: Buffer, mimeType: string): boolean {
  const magicNumbersForType = IMAGE_MAGIC_NUMBERS[mimeType as keyof typeof IMAGE_MAGIC_NUMBERS];
  
  if (!magicNumbersForType) {
    return false;
  }
  
  // 特殊处理WEBP格式
  if (mimeType === 'image/webp') {
    // 检查RIFF头
    if (!checkMagicNumber(buffer, magicNumbersForType[0])) {
      return false;
    }
    // 检查WEBP标识（偏移8字节）
    if (buffer.length < 12) {
      return false;
    }
    const webpSignature = [0x57, 0x45, 0x42, 0x50]; // WEBP
    for (let i = 0; i < webpSignature.length; i++) {
      if (buffer[8 + i] !== webpSignature[i]) {
        return false;
      }
    }
    return true;
  }
  
  // 检查是否匹配任何一个魔数
  return magicNumbersForType.some(magicNumbers => 
    checkMagicNumber(buffer, magicNumbers)
  );
}

/**
 * 获取配置的上传限制
 */
function getUploadLimits() {
  try {
    const nacosService = getNacosService();
    const securityConfig = nacosService.getSecurityConfig();
    const rateLimiting = securityConfig?.rateLimiting?.fileUpload;
    
    const limits = {
      maxFileSize: rateLimiting?.maxFileSize || 50 * 1024 * 1024, // 50MB
      maxTotalSize: rateLimiting?.maxTotalSize || 200 * 1024 * 1024, // 200MB
      maxFilesPerRequest: rateLimiting?.maxFilesPerRequest || 50
    };
    
    logger.debug('Upload limits from config', { limits, hasNacosConfig: !!rateLimiting });
    return limits;
  } catch (error) {
    logger.warn('Failed to get Nacos config, using defaults', { error: error instanceof Error ? error.message : String(error) });
    
    // Nacos未初始化时的默认配置
    return {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      maxTotalSize: 200 * 1024 * 1024, // 200MB
      maxFilesPerRequest: 50
    };
  }
}

/**
 * 格式化文件大小为可读字符串
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * 文件上传安全验证中间件
 */
export const validateImageUpload = (req: Request, res: Response, next: NextFunction) => {
  try {
    const files = req.files as Express.Multer.File[] | undefined;
    const limits = getUploadLimits();
    
    logger.info('Image upload security validation started', {
      requestId: (req as any).requestId,
      sessionId: (req as any).sessionId,
      fileCount: files?.length || 0,
      totalSize: files?.reduce((sum, f) => sum + f.size, 0) || 0,
      limits
    });
    
    if (!files || files.length === 0) {
      logger.warn('No files provided for validation', {
        requestId: (req as any).requestId,
        sessionId: (req as any).sessionId
      });
      
      return res.status(400).json({
        success: false,
        error: 'No files uploaded',
        message: '没有上传文件',
        code: 'NO_FILES'
      });
    }

    // 检查文件数量限制
    if (files.length > limits.maxFilesPerRequest) {
      logger.warn('Too many files uploaded', {
        requestId: (req as any).requestId,
        sessionId: (req as any).sessionId,
        fileCount: files.length,
        maxFiles: limits.maxFilesPerRequest
      });
      
      return res.status(400).json({
        success: false,
        error: 'Too many files',
        message: `一次最多只能上传 ${limits.maxFilesPerRequest} 个文件，您选择了 ${files.length} 个文件`,
        code: 'TOO_MANY_FILES',
        details: {
          currentCount: files.length,
          maxCount: limits.maxFilesPerRequest,
          suggestion: `请减少文件数量或分批上传`
        }
      });
    }

    // 验证每个上传的文件
    for (const file of files) {
      // 1. 检查文件大小（单文件限制）
      if (file.size > limits.maxFileSize) {
        logger.warn('File too large', {
          requestId: (req as any).requestId,
          sessionId: (req as any).sessionId,
          fileName: file.originalname,
          fileSize: file.size,
          maxSize: limits.maxFileSize
        });
        
        return res.status(400).json({
          success: false,
          error: 'File too large',
          message: `文件 "${file.originalname}" 太大，单个文件不能超过 ${formatFileSize(limits.maxFileSize)}`,
          code: 'FILE_TOO_LARGE',
          fileName: file.originalname,
          details: {
            currentSize: file.size,
            currentSizeFormatted: formatFileSize(file.size),
            maxSize: limits.maxFileSize,
            maxSizeFormatted: formatFileSize(limits.maxFileSize),
            suggestion: `请压缩图片或选择较小的文件`
          }
        });
      }

      // 2. 检查MIME类型
      const allowedMimeTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
        'image/webp', 'image/bmp', 'image/tiff'
      ];
      
      if (!allowedMimeTypes.includes(file.mimetype)) {
        logger.security('File type validation failed - unsupported format', {
          requestId: (req as any).requestId,
          sessionId: (req as any).sessionId,
          fileName: file.originalname,
          mimeType: file.mimetype,
          allowedTypes: allowedMimeTypes
        });

        return res.status(400).json({
          success: false,
          error: 'Invalid file type',
          message: `文件 "${file.originalname}" 不是支持的图片格式`,
          code: 'INVALID_FILE_TYPE',
          fileName: file.originalname,
          details: {
            currentType: file.mimetype,
            allowedTypes: allowedMimeTypes,
            suggestion: '请选择 JPEG、PNG、GIF、WebP、BMP 或 TIFF 格式的图片文件'
          }
        });
      }

      // 3. 检查文件名安全性（防止路径遍历攻击）
      const fileName = file.originalname;
      if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
        logger.security('File name validation failed - path traversal attempt', {
          requestId: (req as any).requestId,
          sessionId: (req as any).sessionId,
          fileName: fileName,
          reason: 'contains_path_traversal_chars',
          suspiciousChars: fileName.match(/[\.\/\\]/g)
        });

        return res.status(400).json({
          success: false,
          error: 'Invalid file name',
          message: `文件名 "${fileName}" 包含非法字符`,
          code: 'INVALID_FILE_NAME',
          fileName,
          details: {
            reason: '文件名不能包含路径字符',
            suggestion: '请重命名文件，移除 ".."、"/" 或 "\\" 字符'
          }
        });
      }

      // 4. 魔数验证（检查文件头）
      if (!validateImageFormat(file.buffer, file.mimetype)) {
        logger.security('File magic number validation failed - potential file spoofing', {
          requestId: (req as any).requestId,
          sessionId: (req as any).sessionId,
          fileName: file.originalname,
          declaredMimeType: file.mimetype,
          fileSize: file.size,
          actualMagicNumbers: file.buffer.slice(0, 12).toString('hex'),
          reason: 'file_content_mismatch_extension'
        });

        return res.status(400).json({
          success: false,
          error: 'Invalid image format',
          message: `文件 "${file.originalname}" 不是有效的图片文件`,
          code: 'INVALID_IMAGE_FORMAT',
          fileName: file.originalname,
          details: {
            reason: '文件内容与声明的格式不符',
            declaredType: file.mimetype,
            suggestion: '请确保文件确实是有效的图片文件，或尝试重新保存图片'
          }
        });
      }
    }

    // 5. 检查批量上传总大小限制
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    
    if (totalSize > limits.maxTotalSize) {
      logger.warn('Batch size too large', {
        requestId: (req as any).requestId,
        sessionId: (req as any).sessionId,
        totalSize,
        maxTotalSize: limits.maxTotalSize,
        fileCount: files.length
      });
      
      const excessSize = totalSize - limits.maxTotalSize;
      
      return res.status(400).json({
        success: false,
        error: 'Batch too large',
        message: `批量上传文件总大小超过限制，当前 ${formatFileSize(totalSize)}，限制 ${formatFileSize(limits.maxTotalSize)}`,
        code: 'BATCH_TOO_LARGE',
        details: {
          currentSize: totalSize,
          currentSizeFormatted: formatFileSize(totalSize),
          maxSize: limits.maxTotalSize,
          maxSizeFormatted: formatFileSize(limits.maxTotalSize),
          excessSize: excessSize,
          excessSizeFormatted: formatFileSize(excessSize),
          fileCount: files.length,
          suggestion: `请删除约 ${formatFileSize(excessSize)} 的文件，或分批上传`
        }
      });
    }

    // 验证通过，继续处理
    logger.audit('Image upload security validation passed', {
      requestId: (req as any).requestId,
      sessionId: (req as any).sessionId,
      fileCount: files.length,
      totalSize,
      totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
      fileNames: files.map(f => f.originalname),
      fileTypes: files.map(f => f.mimetype),
      validationChecks: {
        fileSize: 'passed',
        mimeType: 'passed',
        fileName: 'passed',
        magicNumber: 'passed',
        batchSize: 'passed'
      }
    });

    next();
  } catch (error) {
    logger.error('Image validation error', { error });
    res.status(500).json({
      success: false,
      error: 'Validation error',
      message: '文件验证过程中发生错误'
    });
  }
};

/**
 * 检查单个图片文件是否有效（用于其他地方的验证）
 */
export const isValidImageFile = (buffer: Buffer, mimeType: string): boolean => {
  return validateImageFormat(buffer, mimeType);
};

/**
 * 获取支持的图片格式列表
 */
export const getSupportedImageTypes = (): string[] => {
  return Object.keys(IMAGE_MAGIC_NUMBERS);
};