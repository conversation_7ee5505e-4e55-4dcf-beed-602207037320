import { Router, Request, Response } from 'express';
import { HealthService } from '../services/healthService';
import { logger } from '../utils/logger';
import { generalApiLimiter } from '../middleware/rateLimiter';
import { getDynamicKeyManager } from '../utils/dynamicKeyManager';

const router: Router = Router();
const healthService = HealthService.getInstance();

// 应用限流
router.use(generalApiLimiter.middleware());

/**
 * @route GET /api/health
 * @description Get basic health status
 * @access Public
 */
router.get('/', (req: Request, res: Response) => {
  try {
    const basicHealth = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0'
    };

    logger.debug('Basic health check requested', {
      requestId: req.requestId,
      ip: req.ip,
      userAgent: req.get('user-agent')
    });

    res.json(basicHealth);
  } catch (error) {
    logger.error('Basic health check failed', {
      requestId: req.requestId,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message
      } : error
    });

    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    });
  }
});

/**
 * @route GET /api/health/detailed
 * @description Get detailed system health status
 * @access Public
 */
router.get('/detailed', (req: Request, res: Response) => {
  try {
    const healthStatus = healthService.getHealthStatus();
    
    // 添加动态密钥管理器状态
    try {
      const keyManager = getDynamicKeyManager();
      const keyStats = keyManager.getStats();
      healthStatus.security = {
        dynamicKeys: keyStats
      };
    } catch (error) {
      healthStatus.security = {
        dynamicKeys: null,
        error: 'Dynamic key manager not available'
      };
    }

    logger.audit('Detailed health check requested', {
      requestId: req.requestId,
      sessionId: req.sessionId,
      ip: req.ip,
      systemStatus: healthStatus.status,
      memoryUsage: healthStatus.system.memory.usagePercent,
      errorRate: healthStatus.performance.errorRate
    });

    // 根据健康状态设置HTTP状态码
    const httpStatus = healthStatus.status === 'healthy' ? 200 :
                      healthStatus.status === 'warning' ? 200 : 503;

    res.status(httpStatus).json(healthStatus);
  } catch (error) {
    logger.error('Detailed health check failed', {
      requestId: req.requestId,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error
    });

    res.status(503).json({
      status: 'critical',
      timestamp: new Date().toISOString(),
      error: 'Detailed health check failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route GET /api/health/metrics
 * @description Get performance metrics only
 * @access Public
 */
router.get('/metrics', (req: Request, res: Response) => {
  try {
    const healthStatus = healthService.getHealthStatus();
    
    const metrics = {
      timestamp: healthStatus.timestamp,
      performance: healthStatus.performance,
      system: {
        memoryUsagePercent: healthStatus.system.memory.usagePercent,
        cpuLoadAverage: healthStatus.system.cpu.loadAverage[0],
        uptime: healthStatus.uptime
      },
      services: {
        imageCount: healthStatus.services.imageService.totalImages,
        cacheCount: healthStatus.services.pdfCache.stats?.totalCached || 0,
        textinConfigured: healthStatus.services.textinApi.configured
      }
    };

    res.json(metrics);
  } catch (error) {
    logger.error('Metrics check failed', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(503).json({
      error: 'Metrics unavailable',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @route GET /api/health/status
 * @description Get simple status (for load balancers)
 * @access Public
 */
router.get('/status', (req: Request, res: Response) => {
  try {
    const healthStatus = healthService.getHealthStatus();
    
    // 简化的状态检查，适用于负载均衡器
    const isHealthy = healthStatus.status !== 'critical' &&
                     healthStatus.system.memory.usagePercent < 95 &&
                     healthStatus.performance.errorRate < 50;

    if (isHealthy) {
      res.status(200).send('OK');
    } else {
      res.status(503).send('UNAVAILABLE');
    }
  } catch (error) {
    logger.error('Status check failed', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(503).send('ERROR');
  }
});

export default router;