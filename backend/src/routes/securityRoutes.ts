import { Router } from 'express';
import { getCSRFToken, validateBrowserRequest } from '../middleware/security';
import { generalApiLimiter } from '../middleware/rateLimiter';

const router: Router = Router();

// 应用通用限制和轻量级验证
router.use(generalApiLimiter.middleware());
router.use(validateBrowserRequest);

/**
 * @route GET /api/init/session
 * @description Initialize session
 * @access Public
 */
router.get('/session', getCSRFToken);

export default router;