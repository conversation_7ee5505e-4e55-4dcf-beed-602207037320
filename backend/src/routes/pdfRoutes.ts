import { Router } from 'express';
import PDFController from '../controllers/pdfController';
import { validatePDFGeneration, validatePDFGenerationFromData } from '../middleware/validation';
import { generalApiLimiter, pdfGenerationLimiter } from '../middleware/rateLimiter';
import { validateRequestOrigin, CSRFProtection } from '../middleware/security';
import { hmacAuth } from '../middleware/hmacAuth';

const router: Router = Router();
const pdfController = new PDFController();

// Apply security and rate limiting to all PDF routes
router.use(validateRequestOrigin);
router.use(generalApiLimiter.middleware());

/**
 * @route POST /api/pdf/generate
 * @description Generate PDF from processed images
 * @access Public
 * @body {imageIds: string[], options: PDFGenerationOptions}
 * @security HMAC signature required
 */
router.post('/generate', 
  hmacAuth.middleware(),
  CSRFProtection.middleware(),
  pdfGenerationLimiter.middleware(),
  validatePDFGeneration, 
  pdfController.generatePDF
);

/**
 * @route POST /api/pdf/generate-from-data
 * @description Generate PDF from image data directly
 * @access Public
 * @body {images: {id: string, data: string, originalName: string}[], options: PDFGenerationOptions}
 * @security HMAC signature required
 */
router.post('/generate-from-data', 
  hmacAuth.middleware(),
  CSRFProtection.middleware(),
  pdfGenerationLimiter.middleware(),
  validatePDFGenerationFromData, 
  pdfController.generatePDFFromData
);

/**
 * @route GET /api/pdf/download/:fileName
 * @description Download generated PDF file
 * @access Public
 * @params {string} fileName - The PDF file name
 */
router.get('/download/:fileName', pdfController.downloadPDF);

/**
 * @route GET /api/pdf/info/:fileName
 * @description Get PDF file information
 * @access Public
 * @params {string} fileName - The PDF file name
 */
router.get('/info/:fileName', pdfController.getPDFInfo);

/**
 * @route DELETE /api/pdf/:fileName
 * @description Delete PDF file
 * @access Public
 * @params {string} fileName - The PDF file name
 * @security HMAC signature required
 */
router.delete('/:fileName', 
  hmacAuth.middleware(),
  CSRFProtection.middleware(),
  pdfController.deletePDF
);

export default router;