import { Router } from 'express';
import { ImageController } from '../controllers/imageController';
import { uploadMultiple, handleUploadError } from '../middleware/upload';
import { validateFileUpload, validateProcessingOptions } from '../middleware/validation';
import { validateImageUpload } from '../middleware/imageValidation';
import { generalApiLimiter, imageProcessingLimiter, fileUploadLimiter } from '../middleware/rateLimiter';
import { validateRequestOrigin, CSRFProtection } from '../middleware/security';
import { hmacAuth } from '../middleware/hmacAuth';

const router: Router = Router();
const imageController = new ImageController();

// Apply security and rate limiting to all image routes
router.use(validateRequestOrigin);
router.use(generalApiLimiter.middleware());

/**
 * @route POST /api/images/upload
 * @description Upload and process multiple images with AI
 * @access Public
 * @body {files: File[], options: ImageProcessingOptions}
 * @security HMAC signature required
 */
router.post(
  '/upload',
  hmacAuth.middleware(),
  CSRFProtection.middleware(),
  fileUploadLimiter.middleware(),
  imageProcessingLimiter.middleware(),
  uploadMultiple,
  handleUploadError,
  validateFileUpload,
  validateImageUpload,
  validateProcessingOptions,
  imageController.uploadAndProcess
);

/**
 * @route GET /api/images/:imageId
 * @description Get processed image by ID
 * @access Public
 * @params {string} imageId - The image ID
 */
router.get('/:imageId', imageController.getImage);

/**
 * @route GET /api/images/stats
 * @description Get processing statistics
 * @access Public
 */
router.get('/stats', imageController.getStats);

/**
 * @route DELETE /api/images/clear
 * @description Clear processed images from memory
 * @access Public
 * @body {imageIds?: string[]} - Optional array of image IDs to clear
 * @security HMAC signature required
 */
router.delete('/clear', 
  hmacAuth.middleware(),
  CSRFProtection.middleware(),
  imageController.clearImages
);

/**
 * @route POST /api/images/:imageId/reprocess
 * @description Reprocess image with manual crop coordinates
 * @access Public
 * @params {string} imageId - The image ID
 * @body {corners: [number, number][], options: ImageProcessingOptions}
 * @security HMAC signature required
 */
router.post('/:imageId/reprocess', 
  hmacAuth.middleware(),
  CSRFProtection.middleware(),
  imageProcessingLimiter.middleware(),
  imageController.reprocessWithManualCrop
);
router.get('/:imageId/exists', imageController.checkImageExists);

export default router;