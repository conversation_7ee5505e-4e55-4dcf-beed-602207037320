import { Router } from 'express';
import { NativeAuthController } from '../controllers/nativeAuthController';

const router: Router = Router();
const controller = new NativeAuthController();

/**
 * @route   POST /api/native/session
 * @desc    Initializes a secure session for a native client.
 * @access  Public (but requires a valid client secret in the body)
 */
router.post('/session', controller.createSession);

export default router;
