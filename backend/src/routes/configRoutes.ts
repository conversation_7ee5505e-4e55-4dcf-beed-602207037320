import { Router } from 'express';
import ConfigController from '../controllers/configController';

const router: Router = Router();
const configController = new ConfigController();

/**
 * @route GET /api/config/status
 * @description Get configuration status including Nacos and Textin API
 * @access Public
 */
router.get('/status', configController.getStatus);

/**
 * @route POST /api/config/refresh
 * @description Refresh configuration from Nacos
 * @access Public
 */
router.post('/refresh', configController.refreshConfig);

/**
 * @route GET /api/config/test-textin
 * @description Test Textin API connectivity and configuration
 * @access Public
 */
router.get('/test-textin', configController.testTextinAPI);

/**
 * @route GET /api/config/app-downloads
 * @description Get app download URLs configuration
 * @access Public
 */
router.get('/app-downloads', configController.getAppDownloads);

export default router;