# Image to PDF Backend Service

Backend service for the AI-powered image cropping and PDF generation application. This service integrates with Textin API for intelligent document processing and provides RESTful APIs for the frontend application.

## Features

- **AI Image Processing**: Integration with Textin API for intelligent document cropping
- **Multiple Processing Modes**: Auto, manual, and hybrid processing modes
- **Image Enhancement**: Support for various enhancement options (brighten, sharpen, B&W, etc.)
- **PDF Generation**: High-quality PDF generation with customizable options
- **File Management**: Automatic temporary file cleanup and memory management
- **Error Handling**: Comprehensive error handling and logging
- **Security**: Input validation, rate limiting, and CORS protection

## Quick Start

### Prerequisites

- Node.js >= 16.0.0
- npm or yarn
- Textin API credentials

### Installation

1. **Install dependencies**:
```bash
npm install
```

2. **Environment setup**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Build the project**:
```bash
npm run build
```

4. **Start development server**:
```bash
npm run dev
```

5. **Start production server**:
```bash
npm start
```

## Environment Configuration

### 配置管理

本项目支持两种配置方式：

#### 生产环境 - Nacos配置中心（推荐）

```bash
# Nacos连接配置
NACOS_SERVER_ADDR=your-nacos-server:8848
NACOS_NAMESPACE=production
NACOS_USERNAME=your_username
NACOS_PASSWORD=your_password

# 服务配置
PORT=3001
NODE_ENV=production
```

#### 开发环境 - 本地环境变量（备选）

```bash
# Textin API配置（仅开发环境）
TEXTIN_APP_ID=your_app_id_here
TEXTIN_SECRET=your_secret_code_here

# 服务配置
PORT=3001
NODE_ENV=development
```

### Optional Variables

```bash
# API Configuration
API_PREFIX=/api
TEXTIN_API_URL=https://api.textin.com/ai/service/v1/crop_enhance_image

# File Storage
TEMP_DIR=./temp
MAX_FILE_SIZE=52428800
CLEANUP_INTERVAL=3600000
MAX_FILES_PER_REQUEST=50

# Security
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## API Endpoints

### Image Processing

#### Upload and Process Images
```http
POST /api/images/upload
Content-Type: multipart/form-data

Form Data:
- files: File[] (image files)
- options: string (JSON stringified ImageProcessingOptions)
```

#### Get Processed Image
```http
GET /api/images/:imageId
```

#### Get Processing Statistics
```http
GET /api/images/stats
```

#### Clear Processed Images
```http
DELETE /api/images/clear
Content-Type: application/json

{
  "imageIds": ["id1", "id2"] // Optional
}
```

### PDF Generation

#### Generate PDF
```http
POST /api/pdf/generate
Content-Type: application/json

{
  "imageIds": ["id1", "id2", "id3"],
  "options": {
    "pageSize": "A4",
    "orientation": "portrait",
    "margin": 10,
    "quality": 90
  }
}
```

#### Download PDF
```http
GET /api/pdf/download/:fileName
```

#### Get PDF Info
```http
GET /api/pdf/info/:fileName
```

#### Delete PDF
```http
DELETE /api/pdf/:fileName
```

## Data Types

### ImageProcessingOptions
```typescript
interface ImageProcessingOptions {
  mode: 'auto' | 'manual' | 'hybrid';
  enhancement?: 'brighten' | 'sharpen' | 'bw' | 'grayscale' | 'remove_shadow';
  quality?: number; // 1-100
  crop?: boolean;
  dewarp?: boolean;
  direction_correct?: boolean;
  blur_reduce?: boolean;
}
```

### PDFGenerationOptions
```typescript
interface PDFGenerationOptions {
  pageSize: 'A4' | 'Letter';
  orientation: 'portrait' | 'landscape';
  margin: number;
  quality: number;
}
```

## Architecture

### Directory Structure
```
src/
├── controllers/          # Request handlers
├── middleware/          # Custom middleware
├── routes/             # API route definitions
├── services/           # Business logic services
├── types/             # TypeScript type definitions
├── utils/             # Utility functions
└── server.ts          # Application entry point
```

### Service Layer

- **TextinService**: Handles Textin API integration
- **ImageService**: Manages image processing and storage
- **PDFService**: Handles PDF generation and file management

### Middleware

- **Error Handling**: Centralized error handling and logging
- **File Upload**: Multer configuration for file uploads
- **Validation**: Input validation for API endpoints
- **Security**: Rate limiting, CORS, and helmet protection

## Development

### Scripts

```bash
# Development with hot reload
npm run dev

# Build TypeScript
npm run build

# Start production server
npm start

# Clean build directory
npm run clean

# Run linting
npm run lint

# Run tests
npm test
```

### Logging

The application uses a custom logger that outputs structured logs with timestamps. Log levels include:

- `info`: General information
- `warn`: Warning messages
- `error`: Error messages with stack traces
- `debug`: Debug information (development only)

### File Management

- **Temporary Storage**: Uploaded files and generated PDFs are stored temporarily
- **Automatic Cleanup**: Files are automatically cleaned up based on configured intervals
- **Memory Management**: Processed images are stored in memory with cleanup capabilities

## Production Deployment

### Docker

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY dist/ ./dist/
COPY temp/ ./temp/

EXPOSE 3001

CMD ["npm", "start"]
```

### Environment Variables

Ensure all required environment variables are set in production:

- `TEXTIN_APP_ID`
- `TEXTIN_SECRET` 
- `NODE_ENV=production`
- `PORT`
- Other optional configurations

### Health Check

The service provides a health check endpoint:

```http
GET /health
```

Response:
```json
{
  "success": true,
  "message": "Server is running",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "message": "User-friendly message"
}
```

Common HTTP status codes:
- `200`: Success
- `400`: Bad Request (validation errors)
- `404`: Not Found
- `413`: Payload Too Large
- `429`: Too Many Requests
- `500`: Internal Server Error

## Security

- **Rate Limiting**: Prevents API abuse
- **File Validation**: Validates file types and sizes
- **CORS**: Configurable cross-origin resource sharing
- **Helmet**: Security headers
- **Input Validation**: Validates all incoming data

## Monitoring

- **Structured Logging**: All operations are logged with context
- **Memory Usage**: Track processed image memory usage
- **File Cleanup**: Monitor temporary file cleanup
- **API Performance**: Request/response logging