# Nacos Configuration Guide

本项目使用Nacos作为配置中心来安全地管理API密钥和其他敏感配置信息。

## Nacos配置结构

### 配置项详情

| 配置项 | Data ID | Group | 描述 |
|--------|---------|-------|------|
| Textin API配置 | `textin-api.json` | `image-to-pdf` | 文本识别API的认证信息 |

## 配置步骤

### 1. 启动Nacos服务器

```bash
# 下载并启动Nacos (单机模式)
# 访问 https://nacos.io/zh-cn/docs/quick-start.html

# 启动命令
sh startup.sh -m standalone

# 默认访问地址
http://localhost:8848/nacos
# 默认用户名/密码: nacos/nacos
```

### 2. 在Nacos控制台添加配置

#### 配置Textin API

1. 登录Nacos控制台: `http://localhost:8848/nacos`
2. 进入"配置管理" -> "配置列表"
3. 点击"+"添加配置
4. 填写配置信息：
   - **Data ID**: `textin-api.json`
   - **Group**: `image-to-pdf`
   - **配置格式**: `JSON`
   - **配置内容**: 参考 `textin-api.json` 示例文件

```json
{
  "appId": "your_actual_textin_app_id",
  "secret": "your_actual_textin_secret_code",
  "apiUrl": "https://api.textin.com/ai/service/v1/crop_enhance_image"
}
```

5. 点击"发布"保存配置

### 3. 配置应用连接信息

在后端项目的 `.env` 文件中配置Nacos连接信息：

```bash
# Nacos服务器地址
NACOS_SERVER_ADDR=localhost:8848

# 命名空间（可选，默认public）
NACOS_NAMESPACE=public

# 如果启用了认证，配置用户名密码
NACOS_USERNAME=nacos
NACOS_PASSWORD=nacos

# 或者使用AccessKey/SecretKey方式（阿里云等）
# NACOS_ACCESS_KEY=your_access_key
# NACOS_SECRET_KEY=your_secret_key
```

## 配置验证

### 1. 检查配置状态

启动应用后，访问配置状态API：

```bash
curl http://localhost:3001/api/config/status
```

响应示例：
```json
{
  "success": true,
  "data": {
    "nacos": {
      "connected": true,
      "configLoaded": true,
      "serverAddr": "localhost:8848",
      "namespace": "public"
    },
    "textinAPI": {
      "configured": true,
      "hasAppId": true,
      "hasSecret": true,
      "apiUrl": "https://api.textin.com/ai/service/v1/crop_enhance_image"
    },
    "environment": "development",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. 测试API连通性

```bash
curl http://localhost:3001/api/config/test-textin
```

### 3. 手动刷新配置

```bash
curl -X POST http://localhost:3001/api/config/refresh
```

## 生产环境部署

### Docker环境变量

```yaml
# docker-compose.yml
services:
  backend:
    environment:
      - NACOS_SERVER_ADDR=nacos-server:8848
      - NACOS_NAMESPACE=production
      - NACOS_USERNAME=${NACOS_USERNAME}
      - NACOS_PASSWORD=${NACOS_PASSWORD}
      # 不再需要TEXTIN_APP_ID和TEXTIN_SECRET
```

### 安全最佳实践

1. **使用专用命名空间**: 为不同环境创建独立的命名空间
2. **启用访问控制**: 在生产环境中启用Nacos的用户认证
3. **网络隔离**: 确保只有应用服务器能访问Nacos服务器
4. **配置加密**: 对敏感配置使用Nacos的配置加密功能
5. **审计日志**: 启用配置变更的审计日志

## 故障排除

### 常见问题

1. **连接失败**
   - 检查Nacos服务器是否正常运行
   - 验证网络连接和端口是否可达
   - 确认用户名密码是否正确

2. **配置未加载**
   - 检查Data ID和Group是否正确
   - 验证JSON格式是否有效
   - 查看应用日志中的错误信息

3. **配置更新不生效**
   - 检查配置监听器是否正常工作
   - 手动调用refresh API强制刷新
   - 重启应用服务

### 日志查看

应用启动时会输出Nacos相关的日志信息：

```
[INFO] Initializing Nacos configuration service...
[INFO] Using Nacos configuration for Textin API
[INFO] Nacos service initialized successfully
[INFO] Config listener set up for real-time updates
```

## 开发模式 vs 生产模式

### 开发模式
- 可以使用环境变量作为fallback
- Nacos连接失败时不会阻止应用启动
- 支持本地配置文件

### 生产模式
- 必须使用Nacos配置
- Nacos连接失败时应用启动失败
- 不允许使用本地环境变量中的敏感信息

这样确保了在生产环境中所有敏感配置都通过Nacos安全管理，避免了配置泄露的风险。