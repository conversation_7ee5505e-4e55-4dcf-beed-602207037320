{"name": "image-to-pdf-backend", "version": "1.0.0", "description": "Backend service for AI-powered image cropping and PDF generation", "main": "dist/server.js", "scripts": {"dev": "nodemon --exec tsx src/server.ts", "build": "tsc", "start": "node dist/server.js", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "jest"}, "keywords": ["image-processing", "pdf-generation", "textin-api", "node.js"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.0", "helmet": "^7.1.0", "js-yaml": "^4.1.0", "jspdf": "^2.5.1", "multer": "^1.4.5-lts.1", "nacos": "^2.5.1", "sharp": "^0.34.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/cors": "^2.8.14", "@types/express": "^4.17.17", "@types/jest": "^29.5.5", "@types/js-yaml": "^4.0.9", "@types/multer": "^1.4.8", "@types/node": "^20.8.0", "@types/uuid": "^9.0.5", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.2.2"}}