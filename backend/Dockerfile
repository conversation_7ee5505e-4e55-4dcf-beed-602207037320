# builder stage
FROM qa-roc.apuscn.com/library/node:18-alpine AS builder
WORKDIR /app
RUN npm install -g pnpm
COPY package*.json ./
# 安装所有依赖用于构建
RUN pnpm install
COPY . .
RUN pnpm build
RUN ls -la /app/dist

# final stage
FROM qa-roc.apuscn.com/library/node:18-alpine
WORKDIR /app
RUN npm install -g pnpm
# 只从 builder 阶段拷贝必要的产物
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=builder /app/dist ./dist
# 只安装生产环境需要的依赖
RUN pnpm install --prod

EXPOSE 3001
CMD ["node", "dist/server.js"]