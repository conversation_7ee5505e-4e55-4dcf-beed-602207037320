/**
 * HMAC认证功能测试脚本
 * 验证签名生成和验证是否正常工作
 */

const crypto = require('crypto');
const http = require('http');

// HMAC客户端实现
class HMACClient {
  constructor(secretKey) {
    this.secretKey = secretKey;
  }

  generateNonce() {
    return crypto.randomBytes(16).toString('hex');
  }

  stringify(value) {
    if (value === null || value === undefined) {
      return '';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  buildSignString(params, timestamp, method, path) {
    const allParams = { ...params };
    allParams._timestamp = timestamp;
    allParams._method = method.toUpperCase();
    allParams._path = path;
    
    const sortedKeys = Object.keys(allParams).sort();
    const paramString = sortedKeys
      .map(key => `${key}=${this.stringify(allParams[key])}`)
      .join('&');
    
    return paramString;
  }

  generateSignature(params, method, path, nonce) {
    const timestamp = Math.floor(Date.now() / 1000);
    const paramsWithNonce = {
      ...params,
      _nonce: nonce
    };
    
    const signString = this.buildSignString(paramsWithNonce, timestamp, method, path);
    const signature = crypto
      .createHmac('sha256', this.secretKey)
      .update(signString)
      .digest('hex');
    
    return { signature, timestamp };
  }

  addAuthHeaders(params, method, path) {
    const nonce = this.generateNonce();
    const { signature, timestamp } = this.generateSignature(params, method, path, nonce);
    
    return {
      'x-auth-token': signature,
      'x-timestamp': timestamp.toString(),
      'x-trace-id': nonce
    };
  }
}

// HTTP请求工具函数
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (error) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 测试函数
async function testHMACAuth() {
  const secretKey = process.env.HMAC_SECRET_KEY || 'default-dev-key-change-in-production';
  const hmacClient = new HMACClient(secretKey);
  
  console.log('🔐 Testing HMAC Authentication...\n');
  
  // 测试1: 获取CSRF token (不需要HMAC)
  console.log('1️⃣ Testing CSRF token endpoint (no HMAC required)...');
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3001,
      path: '/api/security/csrf-token',
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Test)',
        'Origin': 'http://localhost:3000'
      }
    });
    
    const data = response.data;
    if (response.status === 200) {
      console.log('✅ CSRF token obtained successfully');
      console.log(`   Session ID: ${data.sessionId?.substring(0, 8)}...`);
      
      // 测试2: 测试需要HMAC认证的接口
      console.log('\n2️⃣ Testing protected endpoint with HMAC...');
      
      const testParams = { test: 'data', value: 123 };
      const authHeaders = hmacClient.addAuthHeaders(testParams, 'POST', '/upload');
      
      console.log('📝 Generated HMAC headers:');
      console.log(`   Signature: ${authHeaders['x-auth-token'].substring(0, 16)}...`);
      console.log(`   Timestamp: ${authHeaders['x-timestamp']}`);
      console.log(`   Nonce: ${authHeaders['x-trace-id'].substring(0, 16)}...`);
      
      // 模拟上传请求（会失败因为没有实际文件，但能测试HMAC认证）
      const uploadResponse = await makeRequest({
        hostname: 'localhost',
        port: 3001,
        path: '/api/images/upload',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Test)',
          'Origin': 'http://localhost:3000',
          'x-client-id': data.sessionId,
          'x-request-id': data.csrfToken,
          ...authHeaders
        }
      }, testParams);
      
      const uploadResult = uploadResponse.data;
      console.log('\n📊 Upload endpoint response:');
      console.log(`   Status: ${uploadResponse.status}`);
      console.log(`   Success: ${uploadResult.success}`);
      console.log(`   Message: ${uploadResult.message || 'N/A'}`);
      
      if (uploadResponse.status === 401 && uploadResult.error === 'Authentication Failed') {
        console.log('❌ HMAC authentication failed - signature verification issue');
      } else if (uploadResponse.status !== 401) {
        console.log('✅ HMAC authentication passed (request proceeded to next middleware)');
      }
      
      // 测试3: 测试无HMAC签名的请求（应该被拒绝）
      console.log('\n3️⃣ Testing protected endpoint without HMAC (should fail)...');
      
      const noHmacResponse = await makeRequest({
        hostname: 'localhost',
        port: 3001,
        path: '/api/images/upload',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Test)',
          'Origin': 'http://localhost:3000',
          'x-session-id': data.sessionId,
          'x-csrf-token': data.csrfToken
        }
      }, testParams);
      
      const noHmacResult = noHmacResponse.data;
      console.log('\n📊 No HMAC endpoint response:');
      console.log(`   Status: ${noHmacResponse.status}`);
      console.log(`   Success: ${noHmacResult.success}`);
      console.log(`   Error: ${noHmacResult.error}`);
      
      if (noHmacResponse.status === 401 && noHmacResult.error === 'Authentication Failed') {
        console.log('✅ HMAC protection working - request without signature rejected');
      } else {
        console.log('❌ HMAC protection failed - request should have been rejected');
      }
      
    } else {
      console.log('❌ Failed to get CSRF token:', data);
    }
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the server is running on port 3001');
      console.log('   Run: npm run dev');
    }
  }
  
  console.log('\n🏁 HMAC test completed');
}

// 运行测试
testHMACAuth().catch(console.error);