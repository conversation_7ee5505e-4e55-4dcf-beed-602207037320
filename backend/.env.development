# Nacos Configuration (Primary - for production)
NACOS_SERVER_ADDR=10.11.20.130:30911
NACOS_NAMESPACE=image-to-pdf
NACOS_USERNAME=
NACOS_PASSWORD=
NACOS_ACCESS_KEY=
NACOS_SECRET_KEY=

# Textin API Configuration (Fallback - for development only)
# In production, these should be configured in Nacos
TEXTIN_APP_ID=
TEXTIN_SECRET=
TEXTIN_API_URL=https://api.textin.com/ai/service/v1/crop_enhance_image

# Server Configuration
PORT=3001
NODE_ENV=development
API_PREFIX=/api

# File Storage Configuration
TEMP_DIR=./temp
MAX_FILE_SIZE=52428800
CLEANUP_INTERVAL=3600000
MAX_FILES_PER_REQUEST=50

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# PDF Generation Configuration
PDF_MARGIN=0
PDF_DPI=96
PDF_QUALITY=90
PDF_DEFAULT_PAGE_SIZE=A4