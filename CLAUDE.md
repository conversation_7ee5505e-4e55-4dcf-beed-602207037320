## 项目主要功能
请阅读 @README.md

开发调试的时候，会有另一个窗口运行 `sh dev.sh` 启动Web服务然后用浏览器访问，因此你只需要提示用户到浏览器中使用即可，不需要你来启动服务器。

## 重要问题排查记录

### Chrome浏览器拖拽文件夹崩溃问题

**问题描述**：
- 用户拖拽包含大量图片的文件夹到网页时，Chrome浏览器崩溃
- 错误代码：`RESULT_CODE_KILLED_BAD_MESSAGE`
- 问题在添加图片列表页面后开始出现

**根本原因**：
Chrome浏览器的`getAsFileSystemHandle()` API在处理包含大量文件的文件夹时存在bug，会导致浏览器进程崩溃。

**解决方案**：
创建了只使用`webkitGetAsEntry()` API的安全版本：
- 完全避免调用`getAsFileSystemHandle()`

### 图片列表页拖拽显示问题

**问题描述**：
- 在列表页拖拽图片时，拖拽预览图像不显示或显示异常
- 拖拽过程中只显示默认的系统拖拽图标，无法看到被拖拽的图片内容

**根本原因** 
1. **未设置自定义拖拽图像**：没有调用 `e.dataTransfer.setDragImage()` 
2. **无法获取图片元素**：没有从事件目标中正确获取 `<img>` 元素
3. **缺少拖拽视觉反馈**：用户看不到正在拖拽什么内容

**解决方案**：

1. **获取被拖拽的图片元素**：
```typescript
// 获取被拖拽的图片元素
const target = e.target as HTMLElement
let imgElement: HTMLImageElement | null = null

// 方式1: 如果拖拽的就是图片
if (target.tagName === 'IMG') {
  imgElement = target as HTMLImageElement
} else {
  // 方式2: 在父容器中查找图片
  imgElement = target.closest('.card')?.querySelector('img') as HTMLImageElement
}
```

2. **创建临时DOM拖拽图像**：
```typescript
// 创建容器
const dragContainer = document.createElement('div')
dragContainer.style.cssText = `
  position: absolute;
  top: -2000px;
  width: ${dragWidth}px;
  height: ${dragHeight}px;
  opacity: 0.7;
  border: 2px solid #007bff;
  border-radius: 8px;
  overflow: hidden;
  background: white;
`

// 创建图片副本
const imgClone = document.createElement('img')
imgClone.src = imgElement.src
imgClone.style.cssText = `
  width: 100%;
  height: 100%;
  object-fit: cover;
`
```

3. **设置自定义拖拽图像**：
```typescript
// 设置拖拽图像
e.dataTransfer.setDragImage(dragContainer, dragWidth / 2, dragHeight / 2)

// 延迟清理临时元素
setTimeout(() => {
  if (document.body.contains(dragContainer)) {
    document.body.removeChild(dragContainer)
  }
}, 1000)
```

4. **多重降级策略**：实现了 DOM元素方式 → Canvas方式 → 默认方式的降级处理
