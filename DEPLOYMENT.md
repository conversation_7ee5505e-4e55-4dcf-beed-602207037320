# 部署说明

## 域名配置

本项目支持部署到多个域名：
- `https://all-pdf-editor.apusai.com`（生产环境）
- `https://image-to-pdf.apusai.com`（生产环境）
- `https://test-image-to-pdf.apuscn.com`（测试环境）

## 前端构建

### 为 all-pdf-editor.apusai.com 构建
```bash
cp .env.production .env
pnpm run build
```

### 为 image-to-pdf.apusai.com 构建
```bash
cp .env.production.image-to-pdf .env.production
pnpm run build
```

## 后端配置

后端支持同时服务多个域名。确保在部署时设置环境变量：

### Docker Compose 部署
使用 `.env` 文件设置：
```
ALLOWED_ORIGINS=https://all-pdf-editor.apusai.com,https://image-to-pdf.apusai.com
```

### K8s 部署
在 deployment.yaml 中设置环境变量：
```yaml
- name: ALLOWED_ORIGINS
  value: "https://all-pdf-editor.apusai.com,https://image-to-pdf.apusai.com"
```

## 构建 Docker 镜像

```bash
# 构建前端镜像
docker build -f Dockerfile.frontend -t your-registry/image-to-pdf-frontend:server_0.5.5 .

# 构建后端镜像
docker build -f backend/Dockerfile -t your-registry/image-to-pdf-backend:server_0.5.5 ./backend
```

## K8s 部署

1. 更新镜像版本到 `server_0.5.5`
2. 应用配置：
```bash
kubectl apply -f k8s-deployment.yaml
```

## 验证部署

检查 CORS 是否正确配置：
```bash
curl -H "Origin: https://all-pdf-editor.apusai.com" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://your-backend-url/api/images/upload -v
```

应该返回正确的 CORS 头部。

## Ingress 会话粘性（Sticky Sessions）

当后端以多副本部署到 K8s 时，为确保 Web 路径上的 CSRF 校验在同一实例完成，建议在 Ingress/网关开启会话粘性。

### 方式一：NGINX Ingress（基于 Cookie 的粘性）

在 Ingress 上添加如下注解（v1.9+）：

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: image-to-pdf
  annotations:
    nginx.ingress.kubernetes.io/affinity: "cookie"
    nginx.ingress.kubernetes.io/session-cookie-name: "SESSIONID"
    nginx.ingress.kubernetes.io/session-cookie-path: "/"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "3600"
spec:
  rules:
  - host: image-to-pdf.apusai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: image-to-pdf-backend
            port:
              number: 3001
```

说明：
- 使用 Cookie 方式时，Ingress 会向客户端下发 `SESSIONID` Cookie，并将后续请求路由到同一后端实例。
- `max-age` 可按需调整（单位：秒）。

### 方式二：NGINX Ingress（基于请求头哈希的粘性）

如果希望按客户端会话头进行哈希，可使用：

```yaml
metadata:
  annotations:
    nginx.ingress.kubernetes.io/upstream-hash-by: "$http_x_session_id"
```

配合客户端或网关传递 `X-Session-Id` 请求头（本项目前端已发送 `x-client-id`；可在网关把 `x-client-id` 复制/映射为 `X-Session-Id`）。

### 其他注意事项

- Web 路径：HMAC 校验无状态，但 CSRF 默认存于实例内存 Map，需会话粘性或改造为共享存储（如 Redis）/无状态签名。
- 原生客户端：确保所有 Pod 注入相同的 `HMAC_MASTER_SECRET` 且时钟同步，以便跨实例派生相同会话密钥。
