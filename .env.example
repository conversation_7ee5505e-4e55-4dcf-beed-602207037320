# Frontend Environment Variables

# API Configuration


# Application Configuration
VITE_MAX_FILES=50
VITE_SUPPORTED_FORMATS=jpg,png,bmp,webp,pdf,tiff,gif
VITE_MAX_FILE_SIZE=52428800

# Backend Environment Variables (for docker-compose)

# Nacos Configuration
NACOS_SERVER_ADDR=your-nacos-server:port
NACOS_NAMESPACE=image-to-pdf
NACOS_USERNAME=
NACOS_PASSWORD=
NACOS_ACCESS_KEY=
NACOS_SECRET_KEY=

# CORS Configuration for Production
ALLOWED_ORIGINS=https://all-pdf-editor.apusai.com,https://image-to-pdf.apusai.com,https://test-image-to-pdf.apuscn.com,http://localhost:3000