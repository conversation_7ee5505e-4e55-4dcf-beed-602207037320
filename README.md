# 图片转PDF工具

一个集成了AI智能裁剪能力的B/S架构应用，用于批量处理系列截图并导出为PDF文件。通过集成Textin API，实现文档图像的自动识别、智能裁剪和图像增强功能。

## 🚀 快速启动

本项目提供了两种运行方式：**Docker Compose（推荐）**和**本地脚本**。

---

### 方式一：使用 Docker Compose 启动 (推荐)

这是最简单、最推荐的启动方式，它通过 `docker-compose` 创建了一个包含前端、后端和反向代理的隔离网络环境，完美模拟生产部署。

**架构优势:**
- **零跨域配置**: 前端容器通过 Nginx 反向代理与后端 API 通信，无需处理复杂的 CORS 问题。
- **安全隔离**: 后端服务仅在 Docker 内部网络暴露，无法从外部直接访问，增强了安全性。
- **数据持久化**: 生成的 PDF 文件保存在 Docker 数据卷 (`volume`) 中，即使容器被销毁，数据依然安全。

**前提条件:**
- [Docker](https://www.docker.com/products/docker-desktop/) 已安装并正在运行。
- 你拥有一个外部 Nacos 服务的访问权限。

**步骤:**

1.  **配置Nacos:**
    在项目根目录创建一个 `.env` 文件，并填入你的 Nacos 服务器信息。`docker-compose` 会自动加载此文件。
    ```bash
    # 从模板复制并创建 .env 文件
    cp backend/.env.example .env
    # 编辑 .env 文件，填入你的配置
    nano .env 
    ```
    **.env 文件内容示例:**
    ```env
    NACOS_SERVER_ADDR=zz-nacos3.internalapus.com:31143
    NACOS_NAMESPACE=image-to-pdf
    # ... 其他 Nacos 相关配置
    ```

2.  **构建并启动服务:**
    此命令会构建前端和后端的 Docker 镜像，并以守护进程模式启动所有服务。
    ```bash
    docker-compose up --build -d
    ```

3.  **访问应用:**
    服务启动成功后，在浏览器中打开 **http://localhost:3000** 即可访问。所有 API 请求都会被 Nginx 自动代理到后端服务。

4.  **查看日志/停止服务:**
    ```bash
    # 查看实时日志
    docker-compose logs -f

    # 停止并移除容器
    docker-compose down
    ```

---

### 方式二：本地开发模式

此方式会分别启动前端 Vite 服务器和后端 Node.js 服务器，适合需要频繁修改代码并利用热更新进行调试的场景。

**注意:** 此模式主要用于开发，与生产环境存在差异（如跨域问题），**功能验证和最终测试请使用 Docker 模式**。

**前提条件:**
- [Node.js](https://nodejs.org/) (v18+) 和 [pnpm](https://pnpm.io/) 已安装。
- 你拥有一个外部 Nacos 服务的访问权限。

**步骤:**

1.  **安装依赖:**
    ```bash
    # 安装根目录和 backend 工作区的依赖
    pnpm install --recursive
    ```

2.  **配置后端:**
    复制并配置后端的环境变量文件。
    ```bash
    cp backend/.env.example backend/.env
    nano backend/.env # 填入你的 Nacos 和 Textin API 信息
    ```

3.  **启动开发服务器:**
    ```bash
    sh dev.sh
    ```

4.  **访问应用:**
    - **前端 (Vite):** http://localhost:5173
    - **后端:** http://localhost:3001
    - **跨域问题:** 在此模式下，前端直接请求后端会产生跨域。你需要使用浏览器插件（如 `Allow CORS`）或在开发时临时禁用浏览器安全策略来解决。

---

## 核心功能

- **🤖 AI智能处理**: 自动识别文档边界、裁剪并增强图像。
- **📁 文件管理**: 支持批量上传、拖拽排序和多种图片格式。
- **🔧 灵活裁剪模式**: 提供AI自动、手动调整和混合处理模式。
- **📄 PDF生成**: 高质量输出，支持多种页面尺寸和方向。
- **🛡️ 多层安全保护**: CORS、CSRF、HMAC签名验证、多级访问限制。

## 系统架构

本项目采用前后端分离的B/S架构，并通过 Docker Compose 进行容器化编排。

- **前端**: React, TypeScript, Vite, Bootstrap
- **后端**: Node.js, Express, TypeScript
- **Web服务器/反向代理**: Nginx (在前端容器中，负责托管静态文件和代理API请求)
- **AI服务**: 集成 [Textin API](https://www.textin.com/)
- **配置中心**: 使用 Nacos 进行服务发现和配置管理

## API 接口

本项目主要为 B/S (浏览器/服务器) 架构的 Web 应用提供服务。

此外，系统还额外提供了一套为 C/S (客户端/服务器) 架构（如原生移动应用）设计的安全 API 接口。这套接口采用了独立的、基于加密会话和动态 HMAC 签名的认证机制，以确保原生客户端通信的安全性。

详细的 C/S 接口安全设计方案、认证流程及加密规范，请参阅文档：**[C/S 架构接口安全设计方案](./CS接口.md)**。


## 环境配置与部署

### 配置管理
本项目优先使用 **Nacos** 作为配置中心来管理敏感信息和安全配置。如果 Nacos 不可用，系统会回退到使用本地环境变量，但这**不推荐**用于生产环境。

#### Textin API 配置
- **Nacos Data ID**: `textin-api.json`
- **Group**: `image-to-pdf`
- **回退**: 环境变量 (`TEXTIN_APP_ID`, `TEXTIN_SECRET`)

#### 安全配置
- **Nacos Data ID**: `security.yaml` (推荐) 或 `security.json`
- **Group**: `image-to-pdf`
- **功能**: CORS跨域、CSRF保护、HMAC签名验证、多级访问限制

### 部署
- **前端**: 通过 `Dockerfile.frontend` 构建。该镜像内置了 Nginx，用于提供静态文件并将 `/api/` 的请求反向代理到后端服务。
- **后端**: 通过 `backend/Dockerfile` 构建。
- **编排**: `docker-compose.yml` 负责统一构建、网络配置和部署所有服务。生成的PDF文件会持久化到 Docker Volume 中。

---

## 🛡️ 安全特性

### 多层安全防护架构

本项目实现了企业级的多层安全防护机制，有效防止各种网络攻击和滥用。

#### 1. CORS 跨域保护
- **功能**: 限制允许访问API的前端域名
- **配置**: 通过 Nacos `security.yaml` 中的 `allowedOrigins` 配置
- **开发环境**: 自动允许本地网络IP地址
- **生产环境**: 严格限制为指定域名

#### 2. CSRF 跨站请求伪造保护
- **功能**: 防止恶意网站利用用户身份发起未授权请求
- **机制**: 基于会话的令牌验证
- **生产环境增强**: 验证请求来源(Referer/Origin)

#### 3. HMAC 签名认证
- **功能**: 防止程序化攻击和API滥用
- **算法**: HMAC-SHA256 签名验证
- **防重放**: 时间戳容忍度(默认5分钟) + 随机数(nonce)验证
- **保护接口**: 图片上传、PDF生成、数据删除等核心API

#### 4. 多级访问限制
- **通用限制**: 15分钟内最多200次请求
- **文件上传**: 1分钟内最多20次上传请求
- **图片处理**: 1分钟内最多50张图片处理
- **PDF生成**: 5分钟内最多10次PDF生成
- **IP封锁**: 超限后可临时封锁IP地址

### HMAC 认证集成指南

#### 前端集成步骤

1. **安装依赖** (如果需要在浏览器中使用crypto)
   ```bash
   npm install crypto-js
   ```

2. **导入HMAC工具类**
   ```javascript
   // 将 backend/src/utils/hmacClient.ts 中的代码移植到前端
   import { HMACClient } from './utils/hmacAuth';
   ```

3. **创建HMAC客户端**
   ```javascript
   const hmacClient = new HMACClient({
     secretKey: 'your-hmac-secret-key' // 从安全渠道获取
   });
   ```

4. **发起认证请求**
   ```javascript
   // 获取CSRF token
   const csrfResponse = await fetch('/api/security/csrf-token');
   const { sessionId, csrfToken } = await csrfResponse.json();

   // 准备请求参数
   const requestParams = {
     files: selectedFiles,
     options: { autoProcess: true }
   };

   // 生成HMAC认证头
   const authHeaders = hmacClient.addAuthHeaders(
     requestParams, 
     'POST', 
     '/upload'  // 注意使用相对路径，不包含 /api/images
   );

   // 发起请求
   const response = await fetch('/api/images/upload', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json',
       'x-session-id': sessionId,
       'x-csrf-token': csrfToken,
       ...authHeaders  // 包含 x-hmac-signature, x-hmac-timestamp, x-hmac-nonce
     },
     body: formData
   });
   ```

#### 签名算法说明

HMAC签名包含以下参数，按字母序排列后生成：
- `_method`: HTTP方法(大写)
- `_timestamp`: Unix时间戳(秒)
- `_path`: API路径(相对路径)
- `_nonce`: 随机数(防重放)
- 请求参数: 所有body中的参数

**示例签名字符串:**
```
_method=POST&_nonce=abc123&_path=/upload&_timestamp=1754929786&files=["image1.jpg"]&options={"autoProcess":true}
```

#### 开发环境配置

开发时可以临时禁用HMAC检查：
```bash
DISABLE_HMAC=true npm run dev
```

#### 生产环境部署

1. **配置安全密钥**: 在 Nacos `security.yaml` 中设置强密钥
   ```yaml
   hmacAuth:
     enabled: true
     secretKey: "your-super-strong-production-key"
     timestampTolerance: 300  # 5分钟容忍度
   ```

2. **前端安全传输**: 确保HMAC密钥通过安全渠道传递给前端
   - 方案1: 登录后通过加密API获取
   - 方案2: 构建时注入(适合私有部署)
   - 方案3: 环境变量配置(仅适用于受控环境)

### 安全最佳实践

1. **密钥管理**
   - 生产环境必须使用强随机密钥
   - 定期轮换HMAC密钥
   - 密钥长度不少于32字符

2. **网络安全**
   - 生产环境强制使用HTTPS
   - 配置正确的CORS允许域名
   - 启用IP级别的严格限制

3. **监控和日志**
   - 监控异常的API访问频率
   - 记录所有安全验证失败事件
   - 设置告警规则检测潜在攻击

4. **开发安全**
   - 开发环境可适当放宽限制
   - 测试时使用专门的测试密钥
   - 代码中不硬编码生产密钥