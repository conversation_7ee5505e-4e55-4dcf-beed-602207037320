import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { getSecurityTokens, createHMACClient, extractHMACKeyFromToken } from '../utils/hmacAuth';
import { supportsNativeBridge } from '../utils/deviceDetection';

// Get API base URL from environment or auto-detect based on current host
const getAPIBaseURL = () => {
  // In both development and production, we use a relative path.
  // In dev, V<PERSON>'s proxy handles it. In prod, a reverse proxy (e.g., Nginx) handles it.
  return '/api';
};

const API_BASE_URL = getAPIBaseURL();

// Normalize URL path for HMAC to match server's req.baseUrl + req.path
// Server computes signature over the full API path (e.g., '/api/images/upload').
// Since our axios baseURL already points to '/api', ensure the signed path includes '/api'.
// Examples:
//  - input '/images/upload' -> '/api/images/upload'
//  - input '/api/images/upload' -> '/api/images/upload'
//  - input '/pdf/generate' -> '/api/pdf/generate'
const buildHMACPath = (url: string): string => {
  const pathOnly = url.split('?')[0];
  return pathOnly.startsWith('/api') ? pathOnly : `/api${pathOnly}`;
};

/**
 * 将Blob转换为Base64
 */
function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // 移除data:application/pdf;base64,前缀，只保留base64数据
      const base64Data = result.split(',')[1];
      resolve(base64Data);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * 使用原生桥接下载PDF
 */
async function saveWithNativeBridge(
  blob: Blob,
  fileName: string
): Promise<void> {
  try {
    const base64Data = await blobToBase64(blob);

    // iOS WebView (WKWebView)
    if (window.webkit?.messageHandlers?.saveFile) {
      const params = JSON.stringify({
        data: base64Data,
        fileName: fileName,
        mimeType: 'application/pdf'
      });
      console.log('使用iOS原生桥接下载PDF');
      window.webkit?.messageHandlers?.saveFile?.postMessage(params);
      return;
    }
    
    // Android WebView
    if (window.Android?.saveFile) {
      console.log('使用Android原生桥接下载PDF');
      window.Android.saveFile(base64Data, fileName);
      return;
    }
    
    throw new Error('原生桥接接口不可用');
  } catch (error) {
    console.error('原生桥接下载失败:', error);
    throw error;
  }
}

class APIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 300000, // 5 minutes timeout for file uploads
      // Don't set default Content-Type - let axios handle it based on request data
    });

    // Request interceptor for logging and content-type handling
    this.client.interceptors.request.use(
      (config) => {
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        
        // Set Content-Type for non-FormData requests
        if (!config.headers['Content-Type'] && !(config.data instanceof FormData)) {
          config.headers['Content-Type'] = 'application/json';
        }
        
        return config;
      },
      (error) => {
        console.error('❌ API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`✅ API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error: AxiosError) => {
        console.error('❌ API Response Error:', error.response?.status, error.message);
        console.error('API Base URL:', API_BASE_URL);
        
        // Extract detailed error information from response
        let detailizedError: any = new Error('服务暂时维护中，预计需要几小时时间，请稍后再来访问');
        const errorData = error.response?.data as any;
        
        if (errorData?.message) {
          // If backend provides detailed error info, use it
          detailizedError = new Error(errorData.message);
          detailizedError.code = errorData.code;
          detailizedError.details = errorData.details;
          detailizedError.fileName = errorData.fileName;
          detailizedError.status = error.response?.status;
        }
        
        // Handle specific HTTP status codes
        if (error.response?.status === 413) {
          detailizedError = new Error(errorData?.message || '请求数据过大，请减少图片数量或选择较小的图片');
          detailizedError.code = errorData?.code || 'PAYLOAD_TOO_LARGE';
        } else if (error.response?.status === 429) {
          detailizedError = new Error('请求过于频繁，请稍后再试');
          detailizedError.code = 'RATE_LIMITED';
        } else if (error.response?.status === 500) {
          detailizedError = new Error('服务器内部错误，请稍后再试');
          detailizedError.code = 'SERVER_ERROR';
        } else if (error.code === 'ERR_NETWORK') {
          detailizedError = new Error('服务暂时维护中，预计需要几小时时间，请稍后再来访问');
          detailizedError.code = 'NETWORK_ERROR';
        } else if (error.code === 'ECONNABORTED') {
          detailizedError = new Error('请求超时，请重试');
          detailizedError.code = 'TIMEOUT';
        }
        
        return Promise.reject(detailizedError);
      }
    );
  }

  // Generic GET request
  async get<T>(url: string): Promise<T> {
    const response = await this.client.get<T>(url);
    return response.data;
  }

  // Generic POST request with optional HMAC authentication
  async post<T>(url: string, data?: any, config?: any): Promise<T> {
    // Add HMAC authentication for sensitive endpoints
    if (this.shouldUseHMAC(url)) {
      config = await this.addHMACAuthentication(url, 'POST', data, config);
    }
    
    const response = await this.client.post<T>(url, data, config);
    return response.data;
  }

  // Check if URL requires HMAC authentication
  private shouldUseHMAC(url: string): boolean {
    const sensitiveEndpoints = ['/images/upload', '/pdf/generate', '/images/clear', '/pdf/download'];
    return sensitiveEndpoints.some(endpoint => url.includes(endpoint));
  }

  // Add HMAC authentication to request config
  private async addHMACAuthentication(url: string, method: string, data?: any, config?: any): Promise<any> {
    try {
      const tokens = await getSecurityTokens();

      // 尝试从CSRF token中提取动态密钥
      const secretKey = extractHMACKeyFromToken(tokens.handshake);
      if (!secretKey) {
        throw new Error('Authentication key extraction failed');
      }
      const hmacClient = createHMACClient(secretKey);

      // Prepare params for HMAC
      const hmacParams = typeof data === 'object' ? data : {};
      
      // Normalize path to match server-side req.path
      const urlPath = buildHMACPath(url);
      
      const hmacHeaders = await hmacClient.addAuthHeaders(hmacParams, method, urlPath);
      
      const additionalHeaders = {
        'x-client-id': tokens.sessionId,
        'x-request-id': tokens.handshake,
        ...hmacHeaders
      };
      
      return {
        ...config,
        headers: {
          ...(config?.headers || {}),
          ...additionalHeaders
        }
      };
    } catch (error) {
      console.warn('⚠️ Failed to add HMAC authentication:', error);
      if (!import.meta.env.DEV) {
        throw new Error('Authentication required');
      }
      return config;
    }
  }

  // Generic DELETE request
  async delete<T>(url: string): Promise<T> {
    const response = await this.client.delete<T>(url);
    return response.data;
  }



  // File upload with progress and HMAC authentication
  async uploadFiles<T>(
    url: string, 
    files: File[], 
    options?: any,
    onProgress?: (progress: { loaded: number; total: number; percentage: number }) => void
  ): Promise<T> {
    const formData = new FormData();
    
    // Add files to form data
    files.forEach(file => {
      formData.append('files', file);
    });
    
    // Add options to form data instead of URL
    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    // Get security tokens and generate HMAC headers
    let additionalHeaders: Record<string, string> = {};
    
    try {
      console.log('🔍 Starting HMAC authentication for upload...');
      console.log('🌍 Environment:', { isDev: import.meta.env.DEV, baseURL: API_BASE_URL });
      
      const tokens = await getSecurityTokens();
      console.log('✅ Got security tokens:', { hasSessionId: !!tokens.sessionId, hasHandshake: !!tokens.handshake });
      
      const secretKey = extractHMACKeyFromToken(tokens.handshake);
      if (!secretKey) {
        throw new Error('Authentication key extraction failed');
      }
      console.log('🔑 Using dynamic HMAC key extracted from token, length:', secretKey.length);
      const hmacClient = createHMACClient(secretKey);

      // These params MUST match what the server reconstructs for validation.
      // For multipart/form-data, the backend HMAC middleware runs before multer parses the body,
      // so it cannot read `options` from FormData and defaults to an empty object.
      // Therefore we always sign with options: {} here to match the server's canonicalization.
      const hmacParams = {
        files: [], // File content is not part of signature
        options: {}
      };
      console.log('📋 HMAC params:', hmacParams);
      
      // Normalize path to match server-side req.path
      const urlPath = buildHMACPath(url);
      console.log('🛣️ URL path for HMAC:', urlPath);
      
      const hmacHeaders = await hmacClient.addAuthHeaders(hmacParams, 'POST', urlPath);
      console.log('🔐 Generated HMAC headers:', Object.keys(hmacHeaders));
      
      additionalHeaders = {
        'x-client-id': tokens.sessionId,
        'x-request-id': tokens.handshake,
        ...hmacHeaders
      };
      
      console.log('✨ Final authentication headers:', Object.keys(additionalHeaders));
      console.log('🔐 Added HMAC authentication headers for upload');
    } catch (error) {
      console.error('❌ Failed to add HMAC authentication:', error);
      console.error('📍 Error details:', error instanceof Error ? error.message : error);
      if (!import.meta.env.DEV) {
        throw new Error('Authentication required');
      }
    }

    const config = {
      headers: {
        ...additionalHeaders
      },
      onUploadProgress: (progressEvent: any) => {
        if (onProgress && progressEvent.total) {
          const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress({
            loaded: progressEvent.loaded,
            total: progressEvent.total,
            percentage,
          });
        }
      },
    };

    console.log('🚀 Final request config headers:', Object.keys(config.headers));
    console.log('📦 FormData keys:', Array.from(formData.keys()));

    // Use the original URL without query string
    const response = await this.client.post<T>(url, formData, config);
    return response.data;
  }

  // Download file with save dialog and HMAC authentication
  async downloadFile(url: string, filename?: string): Promise<Blob> {
    console.log('🔽 Starting download process for:', filename);
    console.log('📍 Current environment:', {
      protocol: window.location.protocol,
      isSecureContext: window.isSecureContext,
      hasFileSystemAccess: 'showSaveFilePicker' in window
    });

    // Add HMAC authentication for download requests
    let config: any = {
      responseType: 'blob',
    };

    if (this.shouldUseHMAC(url)) {
      config = await this.addHMACAuthentication(url, 'GET', {}, config);
    }

    const response = await this.client.get(url, config);

    const blob = new Blob([response.data], { type: 'application/pdf' });
    
    if (filename) {
      // First try native bridge (iOS/Android WebView)
      if (supportsNativeBridge()) {
        try {
          console.log('🔗 Attempting native bridge download...');
          await saveWithNativeBridge(blob, filename);
          console.log('✅ File saved via native bridge');
          return blob;
        } catch (error: any) {
          console.log('⚠️ Native bridge failed, trying fallback:', error.message);
        }
      }

      // Second try File System Access API (Chrome 86+, Edge 86+)
      if (window.isSecureContext && 'showSaveFilePicker' in window) {
        try {
          console.log('🚀 Attempting File System Access API...');
          const fileHandle = await (window as any).showSaveFilePicker({
            suggestedName: filename,
            types: [{
              description: 'PDF文件',
              accept: { 'application/pdf': ['.pdf'] }
            }]
          });
          
          const writableStream = await fileHandle.createWritable();
          await writableStream.write(blob);
          await writableStream.close();
          
          console.log('✅ File saved via File System Access API');
          return blob;
        } catch (error: any) {
          if (error.name === 'AbortError') {
            console.log('❌ User cancelled save dialog');
            return blob;
          }
          console.log('⚠️ File System Access API failed, trying fallback:', error.message);
        }
      }
      
      // Fallback: Try different download approaches
      this.tryMultipleDownloadMethods(blob, filename);
    }

    return blob;
  }

  // Try multiple download methods
  private tryMultipleDownloadMethods(blob: Blob, filename: string): void {
    console.log('🔄 Trying multiple download methods...');
    
    const objectUrl = window.URL.createObjectURL(blob);
    
    // Method 1: Traditional download (most reliable)
    this.createDownloadLink(objectUrl, filename, true);
    
    // Cleanup
    setTimeout(() => {
      window.URL.revokeObjectURL(objectUrl);
    }, 1000);
  }

  // Create download link
  private createDownloadLink(url: string, filename: string, useDownloadAttr: boolean = true): void {
    const link = document.createElement('a');
    link.href = url;
    link.style.display = 'none';
    
    if (useDownloadAttr) {
      link.download = filename;
      console.log('💾 Creating download link with filename:', filename);
    } else {
      link.target = '_blank';
      console.log('📂 Opening PDF in new tab for manual save');
    }
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    if (!useDownloadAttr) {
      console.log('💡 Tip: Use Ctrl+S or right-click to save the PDF');
    }
  }


  // Get base URL for constructing download URLs
  getBaseURL(): string {
    return API_BASE_URL.replace('/api', '');
  }
}

// Create singleton instance
export const apiClient = new APIClient();
export default apiClient;
