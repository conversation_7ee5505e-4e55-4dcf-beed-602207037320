import apiClient from './client';
import { 
  APIResponse, 
  UploadResponse, 
  ProcessedImage, 
  ImageProcessingOptions,
  ProcessingStats,
  UploadProgress
} from '../types/api';

export class ImageAPI {
  /**
   * Upload and process multiple images
   */
  static async uploadAndProcess(
    files: File[],
    options: ImageProcessingOptions,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResponse> {
    const response = await apiClient.uploadFiles<APIResponse<UploadResponse>>(
      '/images/upload',
      files,
      options,
      onProgress
    );

    if (!response.success || !response.data) {
      throw new Error(response.error || 'Upload failed');
    }

    return response.data;
  }

  /**
   * Get processed image by ID
   */
  static async getImage(imageId: string): Promise<ProcessedImage> {
    const response = await apiClient.get<APIResponse<ProcessedImage>>(`/images/${imageId}`);

    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to get image');
    }

    return response.data;
  }

  /**
   * Get processing statistics
   */
  static async getStats(): Promise<ProcessingStats> {
    const response = await apiClient.get<APIResponse<ProcessingStats>>('/images/stats');

    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to get stats');
    }

    return response.data;
  }

  /**
   * Clear processed images from server memory
   */
  static async clearImages(_imageIds?: string[]): Promise<void> {
    const response = await apiClient.delete<APIResponse>('/images/clear');

    if (!response.success) {
      throw new Error(response.error || 'Failed to clear images');
    }
  }

  /**
   * Reprocess image with manual crop coordinates
   */
  static async reprocessWithManualCrop(
    imageId: string,
    corners: [number, number][]
  ): Promise<ProcessedImage> {
    const response = await apiClient.post<APIResponse<ProcessedImage>>(
      `/images/${imageId}/reprocess`,
      {
        corners,
        options: {
          mode: 'manual',
          quality: 90
        }
      }
    );

    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to reprocess image');
    }

    return response.data;
  }

  /**
   * Validate processing options
   */
  static validateProcessingOptions(options: ImageProcessingOptions): string[] {
    const errors: string[] = [];

    if (!['auto', 'manual', 'hybrid'].includes(options.mode)) {
      errors.push('Invalid processing mode');
    }

    if (options.enhancement && 
        !['brighten', 'sharpen', 'bw', 'grayscale', 'remove_shadow'].includes(options.enhancement)) {
      errors.push('Invalid enhancement option');
    }

    if (options.quality && (options.quality < 1 || options.quality > 100)) {
      errors.push('Quality must be between 1 and 100');
    }

    return errors;
  }

  /**
   * Get default processing options
   */
  static getDefaultOptions(): ImageProcessingOptions {
    return {
      mode: 'auto',
      quality: 90,
      crop: true,
      dewarp: true,
      direction_correct: true,
      blur_reduce: true,
    };
  }
}

export default ImageAPI;