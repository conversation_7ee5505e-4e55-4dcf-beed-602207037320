import apiClient from './client';
import { 
  APIResponse, 
  PDFResponse, 
  PDFGenerationOptions,
  ProcessedImage
} from '../types/api';

export class PDFAPI {
  /**
   * Generate PDF from processed images (using image IDs)
   */
  static async generatePDF(
    imageIds: string[],
    options: PDFGenerationOptions
  ): Promise<PDFResponse> {
    const response = await apiClient.post<APIResponse<PDFResponse>>(
      '/pdf/generate',
      {
        imageIds,
        options,
      }
    );

    if (!response.success || !response.data) {
      throw new Error(response.error || 'PDF generation failed');
    }

    return response.data;
  }

  /**
   * Generate PDF from processed image data directly
   */
  static async generatePDFFromImages(
    images: ProcessedImage[],
    options: PDFGenerationOptions
  ): Promise<PDFResponse> {
    // Prepare image data for PDF generation
    const imageData = images.map(img => ({
      id: img.id,
      data: img.cropped || img.original, // Use cropped version if available
      originalName: img.originalName
    }));

    const response = await apiClient.post<APIResponse<PDFResponse>>(
      '/pdf/generate-from-data',
      {
        images: imageData,
        options,
      }
    );

    if (!response.success || !response.data) {
      throw new Error(response.error || 'PDF generation failed');
    }

    return response.data;
  }

  /**
   * Download PDF file
   */
  static async downloadPDF(fileName: string, suggestedName?: string): Promise<void> {
    const downloadUrl = `/pdf/download/${fileName}`;
    await apiClient.downloadFile(downloadUrl, suggestedName || fileName);
  }

  /**
   * Get PDF file information
   */
  static async getPDFInfo(fileName: string): Promise<any> {
    const response = await apiClient.get<APIResponse>(`/pdf/info/${fileName}`);

    if (!response.success) {
      throw new Error(response.error || 'Failed to get PDF info');
    }

    return response.data;
  }

  /**
   * Delete PDF file
   */
  static async deletePDF(fileName: string): Promise<void> {
    const response = await apiClient.delete<APIResponse>(`/pdf/${fileName}`);

    if (!response.success) {
      throw new Error(response.error || 'Failed to delete PDF');
    }
  }

  /**
   * Validate PDF generation options
   */
  static validatePDFOptions(options: PDFGenerationOptions): string[] {
    const errors: string[] = [];

    if (!['A4', 'Letter'].includes(options.pageSize)) {
      errors.push('Invalid page size');
    }

    if (!['portrait', 'landscape'].includes(options.orientation)) {
      errors.push('Invalid orientation');
    }

    if (options.margin < 0 || options.margin > 100) {
      errors.push('Margin must be between 0 and 100');
    }

    if (options.quality < 1 || options.quality > 100) {
      errors.push('Quality must be between 1 and 100');
    }

    return errors;
  }

  /**
   * Get default PDF options
   */
  static getDefaultOptions(): PDFGenerationOptions {
    return {
      pageSize: 'A4',
      orientation: 'portrait',
      margin: 0,
      quality: 90,
    };
  }

  /**
   * Generate download URL for direct download
   */
  static getDownloadURL(fileName: string): string {
    return `${apiClient.getBaseURL()}/api/pdf/download/${fileName}`;
  }
}

export default PDFAPI;