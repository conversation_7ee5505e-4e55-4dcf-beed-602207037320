import React from 'react'

interface WeChatDownloadGuidanceProps {
  show: boolean
  onClose: () => void
  downloadUrl: string
}

const WeChatDownloadGuidance: React.FC<WeChatDownloadGuidanceProps> = ({
  show,
  onClose,
  downloadUrl
}) => {
  if (!show) return null

  const handleBrowserOpen = () => {
    // 尝试直接打开链接（在某些版本的微信中可能工作）
    window.open(downloadUrl, '_blank')
    onClose()
  }

  return (
    <>
      {/* Bootstrap Modal Backdrop */}
      <div className="modal-backdrop fade show" onClick={onClose}></div>
      
      {/* Bootstrap Modal */}
      <div className="modal fade show d-block" tabIndex={-1} role="dialog">
        <div className="modal-dialog modal-dialog-centered" role="document">
          <div className="modal-content">
            <div className="modal-header border-0">
              <h5 className="modal-title">
                <i className="fab fa-weixin text-success me-2"></i>
                在浏览器中下载应用
              </h5>
              <button 
                type="button" 
                className="btn-close" 
                onClick={onClose}
                aria-label="关闭"
              ></button>
            </div>
            
            <div className="modal-body text-center">
              {/* 步骤说明 */}
              <div className="row g-3 mb-4">
                <div className="col-12">
                  <div className="d-flex align-items-center">
                    <div className="flex-shrink-0">
                      <span className="badge bg-primary rounded-pill">1</span>
                    </div>
                    <div className="flex-grow-1 ms-3 text-start">
                      点击微信右上角的 <strong>三个点</strong> <i className="fas fa-ellipsis-h"></i>
                    </div>
                  </div>
                </div>
                <div className="col-12">
                  <div className="d-flex align-items-center">
                    <div className="flex-shrink-0">
                      <span className="badge bg-primary rounded-pill">2</span>
                    </div>
                    <div className="flex-grow-1 ms-3 text-start">
                      选择 <strong>"在浏览器打开"</strong> 或 <strong>"复制链接"</strong>
                    </div>
                  </div>
                </div>
                <div className="col-12">
                  <div className="d-flex align-items-center">
                    <div className="flex-shrink-0">
                      <span className="badge bg-primary rounded-pill">3</span>
                    </div>
                    <div className="flex-grow-1 ms-3 text-start">
                      在浏览器中重新访问此页面下载应用
                    </div>
                  </div>
                </div>
              </div>

              {/* 说明文字 */}
              <p className="text-muted small">
                由于微信限制，无法直接下载安装包。<br />
                请按照上述步骤在浏览器中下载。
              </p>
            </div>

            <div className="modal-footer border-0 justify-content-center">
              <button 
                type="button" 
                className="btn btn-outline-primary me-2"
                onClick={handleBrowserOpen}
              >
                <i className="fas fa-external-link-alt me-1"></i>
                尝试打开浏览器
              </button>
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={onClose}
              >
                我知道了
              </button>
            </div>
          </div>
        </div>
      </div>

    </>
  )
}

export default WeChatDownloadGuidance