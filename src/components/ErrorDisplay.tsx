import React from 'react';

interface ErrorDetails {
  currentSize?: number;
  currentSizeFormatted?: string;
  maxSize?: number;
  maxSizeFormatted?: string;
  excessSize?: number;
  excessSizeFormatted?: string;
  currentCount?: number;
  maxCount?: number;
  currentType?: string;
  allowedTypes?: string[];
  suggestion?: string;
  reason?: string;
}

interface ExtendedError extends Error {
  code?: string;
  details?: ErrorDetails;
  fileName?: string;
  status?: number;
}

interface ErrorDisplayProps {
  error: ExtendedError | null;
  onDismiss?: () => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error, onDismiss }) => {
  if (!error) return null;

  const getErrorIcon = (code?: string) => {
    switch (code) {
      case 'BATCH_TOO_LARGE':
      case 'FILE_TOO_LARGE':
        return '📏';
      case 'TOO_MANY_FILES':
        return '📁';
      case 'INVALID_FILE_TYPE':
        return '🚫';
      case 'INVALID_FILE_NAME':
        return '📝';
      case 'INVALID_IMAGE_FORMAT':
        return '🖼️';
      case 'PAYLOAD_TOO_LARGE':
        return '📦';
      case 'RATE_LIMITED':
        return '⏰';
      case 'NETWORK_ERROR':
        return '🌐';
      case 'TIMEOUT':
        return '⏱️';
      default:
        return '⚠️';
    }
  };

  const getAlertClass = (code?: string) => {
    switch (code) {
      case 'RATE_LIMITED':
        return 'alert-warning';
      case 'NETWORK_ERROR':
      case 'SERVER_ERROR':
        return 'alert-danger';
      default:
        return 'alert-info';
    }
  };

  const renderDetails = (details?: ErrorDetails) => {
    if (!details) return null;

    return (
      <div className="mt-3">
        {/* 文件大小信息 */}
        {details.currentSizeFormatted && details.maxSizeFormatted && (
          <div className="mb-2">
            <strong>文件大小:</strong>
            <div className="d-flex justify-content-between mt-1">
              <span>当前: {details.currentSizeFormatted}</span>
              <span>限制: {details.maxSizeFormatted}</span>
            </div>
            {details.excessSizeFormatted && (
              <div className="text-danger">
                <small>超出: {details.excessSizeFormatted}</small>
              </div>
            )}
          </div>
        )}

        {/* 文件数量信息 */}
        {details.currentCount && details.maxCount && (
          <div className="mb-2">
            <strong>文件数量:</strong>
            <div className="d-flex justify-content-between mt-1">
              <span>当前: {details.currentCount} 个</span>
              <span>限制: {details.maxCount} 个</span>
            </div>
          </div>
        )}

        {/* 文件类型信息 */}
        {details.currentType && details.allowedTypes && (
          <div className="mb-2">
            <strong>文件类型:</strong>
            <div className="mt-1">
              <div>当前: <code>{details.currentType}</code></div>
              <div className="mt-1">
                <small>支持的格式: {details.allowedTypes.join(', ')}</small>
              </div>
            </div>
          </div>
        )}

        {/* 解决建议 */}
        {details.suggestion && (
          <div className="alert alert-light mt-3 mb-0">
            <strong>💡 建议:</strong>
            <div className="mt-1">{details.suggestion}</div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`alert ${getAlertClass(error.code)} alert-dismissible`} role="alert">
      <div className="d-flex align-items-start">
        <div className="me-2" style={{ fontSize: '1.5em' }}>
          {getErrorIcon(error.code)}
        </div>
        <div className="flex-grow-1">
          <h6 className="alert-heading mb-2">
            {error.fileName ? `文件处理错误 - ${error.fileName}` : '上传错误'}
          </h6>
          <p className="mb-2">{error.message}</p>
          {renderDetails(error.details)}
        </div>
        {onDismiss && (
          <button 
            type="button" 
            className="btn-close" 
            onClick={onDismiss}
            aria-label="Close"
          />
        )}
      </div>
    </div>
  );
};

export default ErrorDisplay;