import React, { useState, useEffect } from 'react'
import { isMobileDevice } from '../utils/deviceDetection'

interface ImageListProps {
  files: File[]
  onFilesReordered: (files: File[]) => void
  onContinue: () => void
  onCallFileInput: () => void
}

const ImageList: React.FC<ImageListProps> = ({
  files,
  onFilesReordered,
  onContinue,
  onCallFileInput
}) => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)
  const [imageUrls, setImageUrls] = useState<string[]>([])
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    setIsMobile(isMobileDevice())
  }, [])

  useEffect(() => {
    // 为每个文件创建预览URL
    const urls = files.map((file) => URL.createObjectURL(file))
    setImageUrls(urls)

    // 清理函数
    return () => {
      urls.forEach((url) => URL.revokeObjectURL(url))
    }
  }, [files])

  // 创建自定义拖拽图像 - 使用DOM元素方式
  const createDragImageElement = (
    imgElement: HTMLImageElement
  ): HTMLElement => {
    console.log('🎨 开始创建DOM拖拽图像', imgElement)

    // 创建容器
    const container = document.createElement('div')
    container.style.cssText = `
      position: absolute;
      top: -1000px;
      left: -1000px;
      width: 120px;
      height: 90px;
      opacity: 0.7;
      border: 2px solid #007bff;
      border-radius: 8px;
      overflow: hidden;
      background: white;
      pointer-events: none;
    `

    // 创建图片副本
    const imgCopy = document.createElement('img')
    imgCopy.src = imgElement.src
    imgCopy.style.cssText = `
      width: 100%;
      height: 100%;
      object-fit: cover;
    `

    container.appendChild(imgCopy)
    document.body.appendChild(container)

    console.log('✅ DOM拖拽图像创建成功')

    // 设置清理定时器
    setTimeout(() => {
      if (document.body.contains(container)) {
        document.body.removeChild(container)
        console.log('🧹 清理DOM拖拽图像')
      }
    }, 1000)

    return container
  }

  // 创建Canvas拖拽图像
  const createDragImageCanvas = (
    imgElement: HTMLImageElement
  ): HTMLCanvasElement => {
    console.log('🎨 开始创建Canvas拖拽图像', imgElement)

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      console.error('❌ 无法获取Canvas 2D上下文')
      throw new Error('Canvas context not available')
    }

    // 设置 canvas 尺寸
    const targetWidth = 120
    const targetHeight = 90
    canvas.width = targetWidth
    canvas.height = targetHeight

    console.log('📏 Canvas尺寸设置:', targetWidth, 'x', targetHeight)
    console.log('🖼️ 原图尺寸:', imgElement.width, 'x', imgElement.height)

    // 设置半透明效果
    ctx.globalAlpha = 0.7

    try {
      // 简化绘制逻辑 - 直接缩放绘制
      ctx.drawImage(imgElement, 0, 0, targetWidth, targetHeight)
      console.log('✅ 图片绘制成功')

      // 添加边框效果
      ctx.globalAlpha = 1
      ctx.strokeStyle = '#007bff'
      ctx.lineWidth = 2
      ctx.strokeRect(0, 0, targetWidth, targetHeight)
      console.log('✅ 边框绘制成功')
    } catch (error) {
      console.error('❌ Canvas绘制失败:', error)
      throw error
    }

    return canvas
  }

  const handleDragStart = (e: React.DragEvent, index: number) => {
    console.log('🚀 开始拖拽，索引:', index)
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'

    // 获取被拖拽的图片元素
    const target = e.target as HTMLElement
    console.log('🎯 拖拽目标元素:', target.tagName, target.className)

    // 尝试多种方式获取图片元素
    let imgElement: HTMLImageElement | null = null

    // 方式1: 如果拖拽的就是图片
    if (target.tagName === 'IMG') {
      imgElement = target as HTMLImageElement
      console.log('✅ 直接获取到图片元素')
    } else {
      // 方式2: 在父容器中查找图片
      imgElement = target
        .closest('.card')
        ?.querySelector('img') as HTMLImageElement
      console.log('🔍 在父容器中查找图片:', imgElement ? '找到' : '未找到')
    }

    if (imgElement) {
      console.log('🖼️ 找到图片元素:', imgElement.src.substring(0, 50) + '...')
      console.log(
        '📊 图片状态 - complete:',
        imgElement.complete,
        'width:',
        imgElement.width,
        'height:',
        imgElement.height
      )

      try {
        // 方案1: 创建临时DOM元素作为拖拽图像
        console.log('🔧 创建临时DOM拖拽图像...')

        // 计算合适的拖拽尺寸
        const isMobileView = isMobile
        const dragWidth = isMobileView ? 80 : 100
        const dragHeight = isMobileView ? 60 : 75

        // 创建容器
        const dragContainer = document.createElement('div')
        dragContainer.style.cssText = `
          position: absolute;
          top: -2000px;
          left: -2000px;
          width: ${dragWidth}px;
          height: ${dragHeight}px;
          opacity: 0.7;
          border: 2px solid #007bff;
          border-radius: 8px;
          overflow: hidden;
          background: white;
          box-shadow: 0 4px 8px rgba(0,0,0,0.2);
          pointer-events: none;
        `

        // 创建图片副本
        const imgClone = document.createElement('img')
        imgClone.src = imgElement.src
        imgClone.style.cssText = `
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        `

        dragContainer.appendChild(imgClone)
        document.body.appendChild(dragContainer)

        console.log(`📦 临时容器已添加到DOM (${dragWidth}x${dragHeight})`)

        // 直接设置拖拽图像（必须在dragstart事件中同步调用）
        e.dataTransfer.setDragImage(
          dragContainer,
          dragWidth / 2,
          dragHeight / 2
        )
        console.log(`✅ DOM拖拽图像设置成功 (${dragWidth}x${dragHeight})`)

        // 延迟清理临时元素
        setTimeout(() => {
          if (document.body.contains(dragContainer)) {
            document.body.removeChild(dragContainer)
            console.log('🧹 清理临时拖拽容器')
          }
        }, 1000)
      } catch (directError) {
        console.warn('⚠️ 直接方式失败，尝试DOM方式:', directError)

        try {
          // 方案2: DOM元素方式
          console.log('🔧 尝试DOM方式创建拖拽图像...')
          const dragImageElement = createDragImageElement(imgElement)
          e.dataTransfer.setDragImage(dragImageElement, 60, 45)
          console.log('✅ DOM拖拽图像设置成功')
        } catch (domError) {
          console.warn('⚠️ DOM方式失败，尝试Canvas方式:', domError)

          try {
            // 方案3: 回退到Canvas方式
            const dragImageCanvas = createDragImageCanvas(imgElement)
            e.dataTransfer.setDragImage(dragImageCanvas, 60, 45)
            console.log('✅ Canvas拖拽图像设置成功')
          } catch (canvasError) {
            console.error('❌ 所有方式都失败:', canvasError)
            // 如果都失败，使用默认拖拽效果
          }
        }
      }
    } else {
      console.warn('⚠️ 未找到图片元素，使用默认拖拽效果')
    }
  }

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    setDragOverIndex(index)
  }

  const handleDragLeave = () => {
    setDragOverIndex(null)
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null)
      setDragOverIndex(null)
      return
    }

    const newFiles = [...files]
    const draggedFile = newFiles[draggedIndex]

    // 移除被拖拽的元素
    newFiles.splice(draggedIndex, 1)

    // 在新位置插入
    newFiles.splice(dropIndex, 0, draggedFile)

    onFilesReordered(newFiles)
    setDraggedIndex(null)
    setDragOverIndex(null)
  }

  const handleDragEnd = () => {
    setDraggedIndex(null)
    setDragOverIndex(null)
  }

  const moveUp = (index: number) => {
    if (index > 0) {
      const newFiles = [...files]
      const temp = newFiles[index]
      newFiles[index] = newFiles[index - 1]
      newFiles[index - 1] = temp
      onFilesReordered(newFiles)
    }
  }

  const moveDown = (index: number) => {
    if (index < files.length - 1) {
      const newFiles = [...files]
      const temp = newFiles[index]
      newFiles[index] = newFiles[index + 1]
      newFiles[index + 1] = temp
      onFilesReordered(newFiles)
    }
  }

  return (
    <div className='row justify-content-center'>
      <div className='col-12'>
        {/* 独立的主操作区域 */}
        <div className='card shadow-sm mb-4'>
          <div className='card-body text-center'>
            <h5 className='mb-3'>准备设置裁剪区域</h5>
            <p className='text-muted mb-3'>
              将使用第一张图片设置裁剪区域，所有图片将应用相同的裁剪设置
            </p>
            <div className='mb-2'>
              <button
                className={`btn btn-primary ${
                  isMobile ? 'btn-lg w-100' : 'btn-lg px-4'
                }`}
                onClick={onCallFileInput}
                style={{
                  minHeight: isMobile ? '50px' : 'auto',
                  fontSize: isMobile ? '16px' : 'inherit'
                }}
              >
                继续上传图片
              </button>
            </div>

            <button
              className={`btn btn-primary ${
                isMobile ? 'btn-lg w-100' : 'btn-lg px-4'
              }`}
              onClick={onContinue}
              style={{
                minHeight: isMobile ? '50px' : 'auto',
                fontSize: isMobile ? '16px' : 'inherit'
              }}
            >
              下一步：设置裁剪
            </button>
            <div className='mt-3'>
              <small className='text-muted'>
                也可以先在下方调整图片顺序，再进行设置
              </small>
            </div>
          </div>
        </div>

        {/* 图片列表区域 */}
        <div className='card shadow-sm'>
          <div className='card-header'>
            <h5 className='mb-0'>调整图片顺序（可选）</h5>
            <small className='text-muted'>
              共 {files.length} 张图片 •{' '}
              {isMobile ? '使用 ↑↓ 按钮调整' : '拖拽调整顺序'}
            </small>
          </div>

          <div className='card-body'>
            <div className={`row ${isMobile ? 'g-2' : 'g-3'}`}>
              {files.map((file, index) => (
                <div
                  key={`${file.name}-${index}`}
                  className={isMobile ? 'col-6' : 'col-lg-3 col-md-4 col-sm-6'}
                >
                  <div
                    className={`card h-100 ${
                      draggedIndex === index ? 'opacity-50' : ''
                    } ${
                      dragOverIndex === index ? 'border-primary shadow' : ''
                    }`}
                    draggable={!isMobile}
                    onDragStart={(e) => handleDragStart(e, index)}
                    onDragOver={(e) => handleDragOver(e, index)}
                    onDragLeave={handleDragLeave}
                    onDrop={(e) => handleDrop(e, index)}
                    onDragEnd={handleDragEnd}
                    style={{
                      cursor: isMobile ? 'default' : 'move',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    <div className='position-relative'>
                      <img
                        src={imageUrls[index]}
                        alt={file.name}
                        className='card-img-top'
                        style={{
                          height: isMobile ? '120px' : '150px',
                          objectFit: 'cover'
                        }}
                      />
                      <div className='position-absolute top-0 start-0 m-2'>
                        <span className='badge bg-primary'>{index + 1}</span>
                      </div>
                      {isMobile && (
                        <div className='position-absolute top-0 end-0 m-1'>
                          <div className='btn-group-vertical' role='group'>
                            <button
                              className='btn btn-sm btn-outline-light'
                              onClick={() => moveUp(index)}
                              disabled={index === 0}
                              style={{ fontSize: '10px', padding: '2px 6px' }}
                            >
                              ↑
                            </button>
                            <button
                              className='btn btn-sm btn-outline-light'
                              onClick={() => moveDown(index)}
                              disabled={index === files.length - 1}
                              style={{ fontSize: '10px', padding: '2px 6px' }}
                            >
                              ↓
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className='card-body p-2'>
                      <p
                        className={`card-text small text-muted mb-0 ${
                          isMobile ? 'text-truncate' : ''
                        }`}
                      >
                        {file.name}
                      </p>
                      <small className='text-muted'>
                        {(file.size / 1024 / 1024).toFixed(1)} MB
                      </small>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ImageList
