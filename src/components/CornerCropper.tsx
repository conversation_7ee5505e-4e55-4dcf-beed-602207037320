import React, { useRef, useEffect, useState, useCallback } from 'react'
import { isMobileDevice, getSafeViewportHeight } from '../utils/deviceDetection'
import { applyPerspectiveTransform, calculateOutputDimensions, Point } from '../utils/perspectiveTransform'

interface Corner {
  x: number
  y: number
}

interface CornerCropperProps {
  image: {
    id: string
    originalName: string
    original: string
    cropped?: string
    corners?: [number, number][]
  }
  onCropComplete: (imageId: string, croppedImageData: string) => void
  onCancel: () => void
}

const CornerCropper: React.FC<CornerCropperProps> = ({
  image,
  onCropComplete,
  onCancel
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [corners, setCorners] = useState<Corner[]>([])
  const [dragIndex, setDragIndex] = useState<number>(-1)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const imageRef = useRef<HTMLImageElement>()

  useEffect(() => {
    setIsMobile(isMobileDevice())
  }, [])

  // 加载图片并初始化Corners
  useEffect(() => {
    if (!imageRef.current) {
      imageRef.current = new Image()
    }
    
    const img = imageRef.current
    const imageData = image.original
    
    img.onload = () => {
      const { naturalWidth, naturalHeight } = img
      
      if (image.corners && image.corners.length === 4) {
        // 使用AI检测到的corners，并将其从绝对坐标转换为相对坐标
        const relativeCorners = image.corners.map(([x, y]) => ({
          x: x / naturalWidth,
          y: y / naturalHeight,
        }))
        setCorners(relativeCorners)
      } else {
        // 默认设置为图片四个角 (10% inset)
        setCorners([
          { x: 0.1, y: 0.1 },
          { x: 0.9, y: 0.1 },
          { x: 0.9, y: 0.9 },
          { x: 0.1, y: 0.9 },
        ])
      }
      
      setImageLoaded(true)
    }
    
    img.src = `data:image/jpeg;base64,${imageData}`
    
    return () => {
      img.onload = null
    }
  }, [image])

  // 绘制canvas
  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current
    const img = imageRef.current
    if (!canvas || !img || !imageLoaded) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const container = containerRef.current
    if (!container) return

    // 计算适合容器的图片尺寸
    const containerWidth = container.clientWidth
    const containerHeight = isMobile ? getSafeViewportHeight() * 0.6 : 500
    
    const imgAspect = img.width / img.height
    const containerAspect = containerWidth / containerHeight

    let displayWidth, displayHeight
    if (imgAspect > containerAspect) {
      displayWidth = containerWidth
      displayHeight = containerWidth / imgAspect
    } else {
      displayHeight = containerHeight
      displayWidth = containerHeight * imgAspect
    }

    // 设置canvas尺寸
    canvas.width = displayWidth
    canvas.height = displayHeight
    canvas.style.width = `${displayWidth}px`
    canvas.style.height = `${displayHeight}px`

    // 清除画布
    ctx.clearRect(0, 0, displayWidth, displayHeight)
    
    // 绘制图片
    ctx.drawImage(img, 0, 0, displayWidth, displayHeight)
    
    // 绘制半透明遮罩
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
    ctx.fillRect(0, 0, displayWidth, displayHeight)
    
    // 绘制裁剪区域（清除遮罩）
    if (corners.length === 4) {
      ctx.globalCompositeOperation = 'destination-out'
      ctx.beginPath()
      ctx.moveTo(corners[0].x * displayWidth, corners[0].y * displayHeight)
      for (let i = 1; i < corners.length; i++) {
        ctx.lineTo(corners[i].x * displayWidth, corners[i].y * displayHeight)
      }
      ctx.closePath()
      ctx.fill()
      
      // 重置混合模式
      ctx.globalCompositeOperation = 'source-over'
      
      // 绘制边框
      ctx.strokeStyle = '#007bff'
      ctx.lineWidth = 2
      ctx.beginPath()
      ctx.moveTo(corners[0].x * displayWidth, corners[0].y * displayHeight)
      for (let i = 1; i < corners.length; i++) {
        ctx.lineTo(corners[i].x * displayWidth, corners[i].y * displayHeight)
      }
      ctx.closePath()
      ctx.stroke()
      
      // 绘制控制点
      corners.forEach((corner) => {
        const x = corner.x * displayWidth
        const y = corner.y * displayHeight
        
        ctx.fillStyle = '#007bff'
        ctx.beginPath()
        ctx.arc(x, y, isMobile ? 12 : 8, 0, 2 * Math.PI)
        ctx.fill()
        
        ctx.fillStyle = 'white'
        ctx.beginPath()
        ctx.arc(x, y, isMobile ? 8 : 5, 0, 2 * Math.PI)
        ctx.fill()
      })
    }
  }, [corners, imageLoaded, isMobile])

  // 重绘canvas
  useEffect(() => {
    drawCanvas()
  }, [drawCanvas])

  // 获取鼠标/触摸位置
  const getEventPos = (e: React.MouseEvent | React.TouchEvent) => {
    const canvas = canvasRef.current
    if (!canvas) return { x: 0, y: 0 }

    const rect = canvas.getBoundingClientRect()
    let clientX, clientY

    if ('touches' in e) {
      clientX = e.touches[0]?.clientX || e.changedTouches[0]?.clientX || 0
      clientY = e.touches[0]?.clientY || e.changedTouches[0]?.clientY || 0
    } else {
      clientX = e.clientX
      clientY = e.clientY
    }

    return {
      x: (clientX - rect.left) / canvas.clientWidth,
      y: (clientY - rect.top) / canvas.clientHeight
    }
  }

  // 开始拖拽
  const handleStart = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault()
    const pos = getEventPos(e)
    
    // 查找最近的控制点
    let minDistance = Infinity
    let closestIndex = -1
    
    corners.forEach((corner, index) => {
      const distance = Math.sqrt(
        Math.pow(corner.x - pos.x, 2) + Math.pow(corner.y - pos.y, 2)
      )
      if (distance < minDistance) {
        minDistance = distance
        closestIndex = index
      }
    })
    
    // 如果点击在控制点附近（阈值）
    const threshold = isMobile ? 0.05 : 0.03
    if (minDistance < threshold) {
      setDragIndex(closestIndex)
    }
  }

  // 拖拽移动
  const handleMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (dragIndex === -1) return
    
    e.preventDefault()
    const pos = getEventPos(e)
    
    // 限制在画布范围内
    const clampedPos = {
      x: Math.max(0, Math.min(1, pos.x)),
      y: Math.max(0, Math.min(1, pos.y))
    }
    
    setCorners(prev => {
      const newCorners = [...prev]
      newCorners[dragIndex] = clampedPos
      return newCorners
    })
  }

  // 结束拖拽
  const handleEnd = () => {
    setDragIndex(-1)
  }

  // 完成裁剪
  const handleComplete = async () => {
    const img = imageRef.current
    if (!img) return

    try {
      // 转换corners为绝对坐标
      const { naturalWidth, naturalHeight } = img
      const absoluteCorners: Point[] = corners.map(corner => ({
        x: corner.x * naturalWidth,
        y: corner.y * naturalHeight,
      }))

      // 计算输出尺寸
      const outputDimensions = calculateOutputDimensions(absoluteCorners, 800)
      
      // 创建canvas进行透视变换
      const outputCanvas = document.createElement('canvas')
      applyPerspectiveTransform(
        outputCanvas,
        img,
        absoluteCorners,
        outputDimensions.width,
        outputDimensions.height
      )

      // 转换为base64
      const croppedImageData = outputCanvas.toDataURL('image/jpeg', 0.9).split(',')[1]
      
      onCropComplete(image.id, croppedImageData)
    } catch (error) {
      console.error('透视矫正失败:', error)
      alert('图片处理失败，请重试')
    }
  }

  // 重置为AI检测结果
  const handleReset = () => {
    const img = imageRef.current
    if (img && image.corners && image.corners.length === 4) {
      const { naturalWidth, naturalHeight } = img
      const relativeCorners = image.corners.map(([x, y]) => ({
        x: x / naturalWidth,
        y: y / naturalHeight,
      }))
      setCorners(relativeCorners)
    }
  }

  return (
    <div className="row justify-content-center">
      <div className="col-12">
        <div className="card shadow-sm">
          <div className="card-header">
            <h5 className="mb-0">手动调整裁剪区域</h5>
          </div>
          
          <div className="card-body p-0">
            <div 
              ref={containerRef}
              className="position-relative d-flex justify-content-center align-items-center bg-dark"
              style={{ 
                minHeight: isMobile ? '300px' : '500px',
                maxHeight: isMobile ? `${getSafeViewportHeight() * 0.7}px` : '600px',
                overflow: 'hidden'
              }}
            >
              <canvas
                ref={canvasRef}
                onMouseDown={handleStart}
                onMouseMove={handleMove}
                onMouseUp={handleEnd}
                onMouseLeave={handleEnd}
                onTouchStart={handleStart}
                onTouchMove={handleMove}
                onTouchEnd={handleEnd}
                style={{ 
                  cursor: dragIndex !== -1 ? 'grabbing' : 'grab',
                  touchAction: 'none'
                }}
              />
            </div>
          </div>
          
          <div className="card-footer">
            <div className={`d-flex ${isMobile ? 'flex-column gap-2' : 'justify-content-between align-items-center'}`}>
              <div className={isMobile ? 'text-center mb-2' : ''}>
                <small className="text-muted">
                  💡 拖拽蓝色圆点调整裁剪区域的四个角
                </small>
              </div>
              
              <div className={`d-flex gap-2 ${isMobile ? 'w-100' : ''}`}>
                {image.corners && (
                  <button
                    className={`btn btn-outline-secondary ${isMobile ? 'flex-fill' : ''}`}
                    onClick={handleReset}
                    style={{ minHeight: isMobile ? '44px' : 'auto' }}
                  >
                    恢复初始的裁剪区域
                  </button>
                )}
                <button
                  className={`btn btn-secondary ${isMobile ? 'flex-fill' : ''}`}
                  onClick={onCancel}
                  style={{ minHeight: isMobile ? '44px' : 'auto' }}
                >
                  取消裁剪调整
                </button>
                <button
                  className={`btn btn-primary ${isMobile ? 'flex-fill' : ''}`}
                  onClick={handleComplete}
                  style={{ minHeight: isMobile ? '44px' : 'auto' }}
                >
                  保存裁剪区域
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CornerCropper