import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect
} from 'react'
import {
  extractFilesFromDataTransfer,
  supportsFolderDrop
} from '../utils/webkitFolderReader'
import { isMobileDevice } from '../utils/deviceDetection'
import { validateFiles, ValidationResult } from '../utils/fileValidation'
import { isPermissions } from '../utils/jsBridge';

export interface FileUploadHandles {
  onCallFileInput: () => void
}

interface FileUploadProps {
  onFilesSelected: (files: File[], folderName?: string) => void
  onValidationError?: (validation: ValidationResult) => void
  currentStep: 'upload' | 'list' | 'crop' | 'processing' | 'complete' | 'manual-adjust'
}

const FileUpload = forwardRef<FileUploadHandles, FileUploadProps>(
  (props, ref) => {
    useImperativeHandle(ref, () => ({
      onCallFileInput: () => {
        handleButtonClick()
      }
    }))

    const [isDragOver, setIsDragOver] = useState(false)
    const [isScanning, setIsScanning] = useState(false)
    const [scanMessage, setScanMessage] = useState('')
    const [isMobile, setIsMobile] = useState(false)
    const fileInputRef = useRef<HTMLInputElement>(null)

    useEffect(() => {
      setIsMobile(isMobileDevice())
    }, [])

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || [])
      handleFiles(files)
      // 清空input值，允许重新选择相同文件
      event.target.value = ''
    }

    const handleFiles = (files: File[], folderName?: string) => {
      const imageFiles = files.filter((file) => file.type.startsWith('image/'))
      
      if (imageFiles.length === 0) {
        return
      }

      // 执行客户端验证
      const validation = validateFiles(imageFiles)
      
      if (!validation.isValid) {
        // 如果有验证错误，通知父组件
        props.onValidationError?.(validation)
        return
      }

      // 如果有警告，可以考虑显示提示但仍然继续
      if (validation.warnings.length > 0) {
        console.warn('File validation warnings:', validation.warnings)
      }

      // 验证通过，传递文件给父组件
      props.onFilesSelected(imageFiles, folderName)
    }

    const handleDragOver = (event: React.DragEvent) => {
      event.preventDefault()
      setIsDragOver(true)
    }

    const handleDragLeave = (event: React.DragEvent) => {
      event.preventDefault()
      setIsDragOver(false)
    }

    const handleDrop = async (event: React.DragEvent) => {
      event.preventDefault()
      setIsDragOver(false)

      // 如果浏览器支持文件夹拖拽，使用高级处理
      if (supportsFolderDrop() && event.dataTransfer.items.length > 0) {
        setIsScanning(true)
        setScanMessage('正在分析拖拽内容...')

        try {
          const result = await extractFilesFromDataTransfer(
            event.dataTransfer,
            (message) => setScanMessage(message)
          )

          if (result.files.length > 0) {
            handleFiles(result.files, result.folderName)
          } else {
            setScanMessage('未找到图片文件')
            setTimeout(() => {
              setIsScanning(false)
              setScanMessage('')
            }, 2000)
          }
        } catch (error) {
          console.error('文件夹扫描失败:', error)
          setScanMessage('文件夹扫描失败，请重试')
          setTimeout(() => {
            setIsScanning(false)
            setScanMessage('')
          }, 2000)
        } finally {
          setTimeout(() => {
            setIsScanning(false)
            setScanMessage('')
          }, 1000)
        }
      } else {
        // 回退到传统文件处理
        const files = Array.from(event.dataTransfer.files)
        handleFiles(files)
      }
    }

    const handleButtonClick = async () => {
      const res = await isPermissions('CAMERA')
      if (!res) return
      fileInputRef.current?.click()
    }

    return (
      <div
        className='row justify-content-center mt-4'
        style={{ display: props.currentStep === 'upload' ? 'flex' : 'none' }}
      >
        <div className='col-12'>
          <div className='card border-0 bg-light'>
            <div className='card-body p-lg-5'>
              <div
                className={`border border-2 border-dashed rounded-3 text-center ${
                  isDragOver ? 'border-primary bg-white' : 'border-light-subtle'
                } ${isMobile ? 'p-4' : 'p-5'}`}
                style={{
                  minHeight: isMobile ? '200px' : '300px',
                  cursor: 'pointer'
                }}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={handleButtonClick}
              >
                <div className='d-flex flex-column align-items-center justify-content-center h-100'>
                  <div className='mb-4'>
                    <svg
                      width='64'
                      height='64'
                      fill='currentColor'
                      className='text-muted'
                    >
                      <use xlinkHref='#upload-icon' />
                    </svg>
                    <svg style={{ display: 'none' }}>
                      <symbol id='upload-icon' viewBox='0 0 16 16'>
                        <path d='M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z' />
                        <path d='M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z' />
                      </symbol>
                    </svg>
                  </div>

                  {isScanning ? (
                    <>
                      <div className='mb-4'>
                        <div
                          className='spinner-border text-primary'
                          role='status'
                        >
                          <span className='visually-hidden'>扫描中...</span>
                        </div>
                      </div>
                      <h4 className='mb-3'>正在扫描文件...</h4>
                      <p className='text-muted mb-4'>{scanMessage}</p>
                    </>
                  ) : (
                    <>
                      <h4 className='mb-3'>上传会议照片或PPT截图</h4>
                      {isMobile ? (
                        <p className='text-muted mb-4'>点击选择文件</p>
                      ) : (
                        <p className='text-muted mb-4'>
                          拖拽文件{supportsFolderDrop() ? '或文件夹' : ''}到此处，或点击选择文件
                        </p>
                      )}
                      <p className='small text-muted'>
                        支持 PNG、JPG、JPEG 等图片格式
                        {supportsFolderDrop() && !isMobile && (
                          <>
                            <br />
                            📁 支持拖拽整个文件夹，自动扫描所有图片
                          </>
                        )}
                      </p>
                    </>
                  )}

                  {!isScanning && (
                    <button
                      type='button'
                      className={`btn btn-primary mt-3 ${
                        isMobile ? 'btn-lg w-100' : 'btn-lg'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleButtonClick()
                      }}
                      style={{
                        minHeight: isMobile ? '50px' : 'auto',
                        fontSize: isMobile ? '16px' : 'inherit'
                      }}
                    >
                      选择文件
                    </button>
                  )}
                </div>
              </div>

              <input
                ref={fileInputRef}
                type='file'
                multiple
                accept='image/*'
                onChange={handleFileChange}
                style={{ display: 'none' }}
              />
            </div>
          </div>

        </div>
      </div>
    )
  }
)

export default FileUpload
