import React, { useEffect, useRef, useState } from 'react'
import <PERSON>ropper from 'cropperjs'
import { isMobileDevice, getSafeViewportHeight } from '../utils/deviceDetection'

interface ImageCropperProps {
  file: File
  onCropComplete: (cropData: any) => void
  totalFiles: number
}

const ImageCropper: React.FC<ImageCropperProps> = ({ file, onCropComplete, totalFiles }) => {
  const imageRef = useRef<HTMLImageElement>(null)
  const cropperRef = useRef<Cropper | null>(null)
  const [imageUrl, setImageUrl] = useState<string>('')
  const [isReady, setIsReady] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // 检测移动设备
    setIsMobile(isMobileDevice())
    
    if (file) {
      const url = URL.createObjectURL(file)
      setImageUrl(url)
      
      return () => {
        URL.revokeObjectURL(url)
      }
    }
  }, [file])

  useEffect(() => {
    if (imageRef.current && imageUrl) {
      imageRef.current.onload = () => {
        if (cropperRef.current) {
          cropperRef.current.destroy()
        }
        
        cropperRef.current = new Cropper(imageRef.current!, {
          aspectRatio: NaN,
          viewMode: 1,
          minCropBoxWidth: 50,
          minCropBoxHeight: 50,
          autoCrop: true,
          autoCropArea: 1, // 默认选择整个图片区域 (1 = 100%)
          restore: false,
          guides: true,
          center: true,
          highlight: true,
          cropBoxMovable: true,
          cropBoxResizable: true,
          toggleDragModeOnDblclick: false,
          ready() {
            setIsReady(true)
          }
        })
      }
    }

    return () => {
      if (cropperRef.current) {
        cropperRef.current.destroy()
        cropperRef.current = null
      }
    }
  }, [imageUrl])

  const handleCropAndProcess = () => {
    if (cropperRef.current) {
      const cropData = cropperRef.current.getData()
      onCropComplete(cropData)
    }
  }

  return (
    <div className="row justify-content-center">
      <div className="col-12">
        <div className="card shadow-sm">
          <div className="card-header">
            <h5 className="mb-0">设置裁剪区域</h5>
            <small className="text-muted">
              当前图片: {file.name} (共 {totalFiles} 张图片)
            </small>
          </div>
          
          <div className="card-body p-0">
            <div 
              className="position-relative" 
              style={{ 
                maxHeight: isMobile ? `${getSafeViewportHeight() * 0.5}px` : '70vh', 
                overflow: 'hidden',
                minHeight: isMobile ? '250px' : '400px'
              }}
            >
              <img
                ref={imageRef}
                src={imageUrl}
                alt="裁剪预览"
                style={{ maxWidth: '100%', display: 'block' }}
              />
            </div>
          </div>
          
          <div className="card-footer">
            <div className={`d-flex ${isMobile ? 'flex-column gap-3' : 'justify-content-between align-items-center'}`}>
              <div className={isMobile ? 'text-center' : ''}>
                <small className="text-muted">
                  💡 拖拽选择需要保留的区域，相同的裁剪将应用到所有图片
                </small>
              </div>
              <button
                className={`btn btn-primary ${isMobile ? 'btn-lg w-100' : 'btn-lg'}`}
                onClick={handleCropAndProcess}
                disabled={!isReady}
                style={{
                  minHeight: isMobile ? '50px' : 'auto',
                  fontSize: isMobile ? '16px' : 'inherit'
                }}
              >
                {isReady ? '生成PDF' : '加载中...'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ImageCropper