import React, { useEffect, useState, useCallback, useRef } from 'react'
import { ProcessedImage } from '../types/api'
import { isMobileDevice } from '../utils/deviceDetection'

interface ImageViewerProps {
  images: ProcessedImage[]
  initialIndex: number
  isOpen: boolean
  onClose: () => void
  onManualAdjust?: (imageId: string) => void
}

const ImageViewer: React.FC<ImageViewerProps> = ({
  images,
  initialIndex,
  isOpen,
  onClose,
  onManualAdjust
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [isMobile, setIsMobile] = useState(false)
  const [touchStartX, setTouchStartX] = useState(0)
  const [touchStartY, setTouchStartY] = useState(0)
  const viewerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    setIsMobile(isMobileDevice())
  }, [])

  useEffect(() => {
    setCurrentIndex(initialIndex)
  }, [initialIndex])

  const goToNext = useCallback(() => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }, [currentIndex, images.length])

  const goToPrev = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }, [currentIndex])

  const handleKeyPress = useCallback((e: KeyboardEvent) => {
    if (!isOpen) return
    
    switch (e.key) {
      case 'Escape':
        onClose()
        break
      case 'ArrowRight':
      case ' ': // Spacebar
        e.preventDefault()
        goToNext()
        break
      case 'ArrowLeft':
        e.preventDefault()
        goToPrev()
        break
    }
  }, [isOpen, onClose, goToNext, goToPrev])

  const handleWheel = useCallback((e: WheelEvent) => {
    if (!isOpen) return
    
    e.preventDefault()
    if (e.deltaY > 0) {
      goToNext()
    } else if (e.deltaY < 0) {
      goToPrev()
    }
  }, [isOpen, goToNext, goToPrev])

  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0]
    setTouchStartX(touch.clientX)
    setTouchStartY(touch.clientY)
  }

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!e.changedTouches[0]) return
    
    const touchEndX = e.changedTouches[0].clientX
    const touchEndY = e.changedTouches[0].clientY
    const deltaX = touchStartX - touchEndX
    const deltaY = touchStartY - touchEndY
    const minSwipeDistance = 50

    // Check if it's a horizontal swipe (more horizontal than vertical)
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
      if (deltaX > 0) {
        goToNext() // Swipe left = next
      } else {
        goToPrev() // Swipe right = prev
      }
    }
    // Check if it's a vertical swipe for mobile navigation
    else if (isMobile && Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > minSwipeDistance) {
      if (deltaY > 0) {
        goToNext() // Swipe up = next
      } else {
        goToPrev() // Swipe down = prev
      }
    }
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const handleManualAdjustClick = () => {
    const currentImage = images[currentIndex]
    if (onManualAdjust && currentImage) {
      onManualAdjust(currentImage.id)
      onClose() // Close viewer when going to manual adjust
    }
  }

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyPress)
      document.addEventListener('wheel', handleWheel, { passive: false })
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'auto'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyPress)
      document.removeEventListener('wheel', handleWheel)
      document.body.style.overflow = 'auto'
    }
  }, [isOpen, handleKeyPress, handleWheel])

  if (!isOpen || images.length === 0) return null

  const currentImage = images[currentIndex]

  return (
    <div
      ref={viewerRef}
      className="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        zIndex: 2000,
        cursor: 'pointer'
      }}
      onClick={handleBackdropClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      {/* Close button */}
      <button
        className="position-absolute d-flex align-items-center justify-content-center"
        style={{
          top: '20px',
          right: '20px',
          zIndex: 2001,
          width: isMobile ? '56px' : '52px',
          height: isMobile ? '56px' : '52px',
          borderRadius: '50%',
          fontSize: isMobile ? '28px' : '26px',
          border: 'none',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          backdropFilter: 'blur(8px)',
          transition: 'all 0.2s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.85)'
          e.currentTarget.style.transform = 'scale(1.05)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
          e.currentTarget.style.transform = 'scale(1)'
        }}
        onClick={onClose}
        aria-label="关闭"
      >
        ×
      </button>

      {/* Manual adjust button */}
      {onManualAdjust && (
        <button
          className="position-absolute d-flex align-items-center justify-content-center"
          style={{
            top: '20px',
            right: isMobile ? '84px' : '84px',
            zIndex: 2001,
            height: isMobile ? '56px' : '52px',
            padding: isMobile ? '12px 20px' : '10px 18px',
            fontSize: isMobile ? '16px' : '15px',
            fontWeight: '500',
            borderRadius: isMobile ? '28px' : '26px',
            border: 'none',
            backgroundColor: 'rgba(13, 110, 253, 0.9)', // Bootstrap primary blue with transparency
            color: 'white',
            backdropFilter: 'blur(8px)',
            transition: 'all 0.2s ease',
            cursor: 'pointer',
            whiteSpace: 'nowrap',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(13, 110, 253, 1)'
            e.currentTarget.style.transform = 'translateY(-1px)'
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.4)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(13, 110, 253, 0.9)'
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)'
          }}
          onClick={handleManualAdjustClick}
          aria-label="手工调整裁剪区域"
        >
          <span style={{ marginRight: '6px', fontSize: isMobile ? '18px' : '16px' }}>✂️</span>
          {isMobile ? '调整裁剪' : '手工调整裁剪区域'}
        </button>
      )}

      {/* Navigation arrows for desktop */}
      {!isMobile && images.length > 1 && (
        <>
          <button
            className="btn btn-light position-absolute"
            style={{
              left: '20px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2001,
              width: '50px',
              height: '50px',
              borderRadius: '50%',
              fontSize: '20px',
              opacity: currentIndex === 0 ? 0.5 : 1
            }}
            onClick={goToPrev}
            disabled={currentIndex === 0}
            aria-label="上一张"
          >
            ‹
          </button>
          <button
            className="btn btn-light position-absolute"
            style={{
              right: '20px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2001,
              width: '50px',
              height: '50px',
              borderRadius: '50%',
              fontSize: '20px',
              opacity: currentIndex === images.length - 1 ? 0.5 : 1
            }}
            onClick={goToNext}
            disabled={currentIndex === images.length - 1}
            aria-label="下一张"
          >
            ›
          </button>
        </>
      )}

      {/* Image container */}
      <div
        className="d-flex align-items-center justify-content-center"
        style={{
          width: '100%',
          height: '100%',
          padding: '60px 20px 20px',
          cursor: 'default'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <img
          src={`data:image/jpeg;base64,${currentImage.cropped || currentImage.original}`}
          alt={`处理后的图片 ${currentIndex + 1}`}
          style={{
            maxWidth: '100%',
            maxHeight: '100%',
            objectFit: 'contain',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
            borderRadius: '8px'
          }}
        />
      </div>

      {/* Image counter */}
      {images.length > 1 && (
        <div
          className="position-absolute bg-dark text-white px-3 py-2 rounded"
          style={{
            bottom: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            fontSize: '14px',
            opacity: 0.8
          }}
        >
          {currentIndex + 1} / {images.length}
        </div>
      )}

      {/* Mobile instructions */}
      {isMobile && images.length > 1 && (
        <div
          className="position-absolute text-white text-center"
          style={{
            bottom: '60px',
            left: '50%',
            transform: 'translateX(-50%)',
            fontSize: '12px',
            opacity: 0.6
          }}
        >
          上下滑动或左右滑动切换图片
        </div>
      )}

      {/* Desktop instructions */}
      {!isMobile && images.length > 1 && (
        <div
          className="position-absolute text-white text-center"
          style={{
            bottom: '60px',
            left: '50%',
            transform: 'translateX(-50%)',
            fontSize: '12px',
            opacity: 0.6
          }}
        >
          使用方向键、鼠标滚轮或点击箭头切换图片 • 按ESC关闭
        </div>
      )}
    </div>
  )
}

export default ImageViewer