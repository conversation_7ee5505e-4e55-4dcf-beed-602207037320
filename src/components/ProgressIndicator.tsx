import React, { useEffect, useState, useRef } from 'react'
import { isMobileDevice } from '../utils/deviceDetection'
import { ProcessedImage } from '../types/api'
import ImageViewer from './ImageViewer'
import { extractFilesFromDataTransfer, supportsFolderDrop } from '../utils/webkitFolderReader'

interface ProgressIndicatorProps {
  isProcessing: boolean
  progress: number
  totalFiles: number
  processedImages: ProcessedImage[]
  onReset: () => void
  onDownload: () => void
  onManualAdjust: (imageId: string) => void
  onImagesReordered?: (reorderedImages: ProcessedImage[]) => void
  onAdditionalFilesSelected?: (files: File[], folderName?: string) => void
  onRemoveImage?: (imageId: string) => void
  additionalProcessingCount?: number
  isAdditionalProcessing?: boolean
  generatedPDF?: {
    fileName: string
    downloadUrl: string
    fileSize: number
  } | null
  isGenerating?: boolean
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ 
  isProcessing, 
  progress, 
  totalFiles, 
  processedImages,
  onReset,
  onDownload,
  onManualAdjust,
  onImagesReordered,
  onAdditionalFilesSelected,
  onRemoveImage,
  additionalProcessingCount = 0,
  isAdditionalProcessing = false,
  generatedPDF,
  isGenerating = false
}) => {
  const [isMobile, setIsMobile] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [showImageViewer, setShowImageViewer] = useState(false)
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)
  const [pdfDownloaded, setPdfDownloaded] = useState(false)
  const [isDragOverUpload, setIsDragOverUpload] = useState(false)
  const [isScanning, setIsScanning] = useState(false)
  const [scanMessage, setScanMessage] = useState('')
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [imageToDelete, setImageToDelete] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  useEffect(() => {
    setIsMobile(isMobileDevice())
  }, [])

  // Reset PDF downloaded status when a new PDF is generated
  useEffect(() => {
    if (generatedPDF) {
      setPdfDownloaded(false)
    }
  }, [generatedPDF])

  const handleResetClick = () => {
    // Always show confirmation dialog when user tries to reset
    setShowConfirmDialog(true);
  };

  const handleConfirmReset = () => {
    onReset();
    setPdfDownloaded(false); // Reset PDF download status
    setShowConfirmDialog(false);
  };

  const handleCancelReset = () => {
    setShowConfirmDialog(false);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleImageClick = (index: number) => {
    setSelectedImageIndex(index)
    setShowImageViewer(true)
  }

  const closeImageViewer = () => {
    setShowImageViewer(false)
  }

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
    // 设置一个特殊的数据类型来标识这是图片排序拖拽，不是文件拖拽
    e.dataTransfer.setData('application/x-image-reorder', index.toString())
    
    // Create custom drag image similar to ImageList component
    const target = e.target as HTMLElement
    let imgElement: HTMLImageElement | null = null
    
    if (target.tagName === 'IMG') {
      imgElement = target as HTMLImageElement
    } else {
      imgElement = target.closest('.card')?.querySelector('img') as HTMLImageElement
    }

    if (imgElement) {
      try {
        const dragWidth = 100
        const dragHeight = 80

        const dragContainer = document.createElement('div')
        dragContainer.style.cssText = `
          position: absolute;
          top: -2000px;
          width: ${dragWidth}px;
          height: ${dragHeight}px;
          opacity: 0.8;
          border: 2px solid #007bff;
          border-radius: 8px;
          overflow: hidden;
          background: white;
          box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        `

        const imgClone = document.createElement('img')
        imgClone.src = imgElement.src
        imgClone.style.cssText = `
          width: 100%;
          height: 100%;
          object-fit: cover;
        `

        dragContainer.appendChild(imgClone)
        document.body.appendChild(dragContainer)

        e.dataTransfer.setDragImage(dragContainer, dragWidth / 2, dragHeight / 2)

        setTimeout(() => {
          if (document.body.contains(dragContainer)) {
            document.body.removeChild(dragContainer)
          }
        }, 1000)
      } catch (error) {
        console.log('Could not create custom drag image, using default')
      }
    }
  }

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    setDragOverIndex(index)
  }

  const handleDragLeave = () => {
    setDragOverIndex(null)
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    
    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null)
      setDragOverIndex(null)
      return
    }

    const newImages = [...processedImages]
    const draggedImage = newImages[draggedIndex]
    
    newImages.splice(draggedIndex, 1)
    newImages.splice(dropIndex, 0, draggedImage)
    
    if (onImagesReordered) {
      onImagesReordered(newImages)
    }
    
    setDraggedIndex(null)
    setDragOverIndex(null)
  }

  const handleDragEnd = () => {
    setDraggedIndex(null)
    setDragOverIndex(null)
  }

  const handleDownloadPDF = async () => {
    await onDownload();
    setPdfDownloaded(true);
  }

  const handleDownloadAndReset = async () => {
    await onDownload();
    setPdfDownloaded(true);
    onReset();
    setShowConfirmDialog(false);
  }

  // 上传相关函数
  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    handleFiles(files)
    // 清空input值，允许重新选择相同文件
    event.target.value = ''
  }

  const handleFiles = (files: File[]) => {
    const imageFiles = files.filter((file) => file.type.startsWith('image/'))
    if (imageFiles.length > 0 && onAdditionalFilesSelected) {
      onAdditionalFilesSelected(imageFiles)
    }
  }

  const handleUploadDragOver = (event: React.DragEvent) => {
    // 如果是图片排序拖拽，则不响应
    if (event.dataTransfer.types.includes('application/x-image-reorder')) {
      return
    }
    // 只有在拖拽外部文件时才响应
    if (event.dataTransfer.types.includes('Files')) {
      event.preventDefault()
      event.stopPropagation()
      setIsDragOverUpload(true)
    }
  }

  const handleUploadDragLeave = (event: React.DragEvent) => {
    // 如果是图片排序拖拽，则不响应
    if (event.dataTransfer.types.includes('application/x-image-reorder')) {
      return
    }
    // 只有在拖拽外部文件时才响应
    if (event.dataTransfer.types.includes('Files')) {
      event.preventDefault()
      event.stopPropagation()
      setIsDragOverUpload(false)
    }
  }

  const handleUploadDrop = async (event: React.DragEvent) => {
    // 如果是图片排序拖拽，则不响应
    if (event.dataTransfer.types.includes('application/x-image-reorder')) {
      return
    }
    // 只有在拖拽外部文件时才响应
    if (!event.dataTransfer.types.includes('Files')) {
      return
    }

    event.preventDefault()
    event.stopPropagation()
    setIsDragOverUpload(false)

    if (!onAdditionalFilesSelected) return

    // 如果浏览器支持文件夹拖拽，使用高级处理
    if (supportsFolderDrop() && event.dataTransfer.items.length > 0) {
      setIsScanning(true)
      setScanMessage('正在分析拖拽内容...')

      try {
        const result = await extractFilesFromDataTransfer(
          event.dataTransfer,
          (message) => setScanMessage(message)
        )

        if (result.files.length > 0) {
          onAdditionalFilesSelected(result.files, result.folderName)
        } else {
          setScanMessage('未找到图片文件')
          setTimeout(() => {
            setIsScanning(false)
            setScanMessage('')
          }, 2000)
        }
      } catch (error) {
        console.error('文件夹扫描失败:', error)
        setScanMessage('文件夹扫描失败，请重试')
        setTimeout(() => {
          setIsScanning(false)
          setScanMessage('')
        }, 2000)
      } finally {
        setTimeout(() => {
          setIsScanning(false)
          setScanMessage('')
        }, 1000)
      }
    } else {
      // 回退到传统文件处理
      const files = Array.from(event.dataTransfer.files)
      handleFiles(files)
    }
  }

  // 删除相关函数
  const handleDeleteClick = (imageId: string) => {
    setImageToDelete(imageId)
    setShowDeleteDialog(true)
  }

  const handleDeleteConfirm = () => {
    if (imageToDelete && onRemoveImage) {
      onRemoveImage(imageToDelete)
    }
    setShowDeleteDialog(false)
    setImageToDelete(null)
  }

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false)
    setImageToDelete(null)
  }

  return (
    <div className="row justify-content-center">
      <div className="col-12 col-md-10">
        <div className="card shadow-sm">
          <div className="card-body p-4">
            {isProcessing || isGenerating ? (
              <div className="text-center">
                <div className="mb-4">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">处理中...</span>
                  </div>
                </div>
                
                <h4 className="mb-3">
                  {isProcessing ? '🤖 AI智能处理中...' : '📄 生成PDF中...'}
                </h4>
                <p className="text-muted mb-4">
                  {isProcessing 
                    ? (isAdditionalProcessing 
                        ? `正在使用AI处理新增的 ${additionalProcessingCount} 张图片` 
                        : `正在使用AI处理 ${totalFiles} 张图片`)
                    : '正在生成PDF文件'}
                </p>
                
                <div className="progress mb-3" style={{ height: '8px' }}>
                  <div 
                    className="progress-bar progress-bar-striped progress-bar-animated" 
                    role="progressbar" 
                    style={{ width: `${progress}%` }}
                    aria-valuenow={progress} 
                    aria-valuemin={0} 
                    aria-valuemax={100}
                  />
                </div>
                <small className="text-muted">{progress}% 完成</small>
              </div>
            ) : (
              <>
                {processedImages.length > 0 ? (
                  <>
                    <div className="mb-4">
                      <h5 className="card-title">✅ AI处理结果</h5>
                      
                      {/* Image Preview Grid */}
                      <div 
                        className="row g-2 mb-3"
                        onDragOver={handleUploadDragOver}
                        onDragLeave={handleUploadDragLeave}
                        onDrop={handleUploadDrop}
                      >
                        {processedImages.map((img, index) => (
                          <div 
                            key={img.id} 
                            className="col-6 col-md-3"
                            draggable
                            onDragStart={(e) => handleDragStart(e, index)}
                            onDragOver={(e) => handleDragOver(e, index)}
                            onDragLeave={handleDragLeave}
                            onDrop={(e) => handleDrop(e, index)}
                            onDragEnd={handleDragEnd}
                          >
                            <div 
                              className="card"
                              style={{
                                opacity: draggedIndex === index ? 0.5 : 1,
                                transform: dragOverIndex === index && draggedIndex !== index ? 'scale(1.05)' : 'scale(1)',
                                transition: 'transform 0.2s, opacity 0.2s',
                                border: dragOverIndex === index && draggedIndex !== index ? '2px dashed #007bff' : '1px solid #dee2e6'
                              }}
                            >
                              <div 
                                className="position-relative" 
                                style={{ cursor: 'pointer' }} 
                                onClick={() => handleImageClick(index)}
                              >
                                <img
                                  src={`data:image/jpeg;base64,${img.cropped || img.original}`}
                                  onLoad={() => {
                                    console.log('Image loaded successfully:', img.id, img.originalName);
                                  }}
                                  onError={(e) => {
                                    console.error('Image load error for:', img.id, img.originalName, {
                                      croppedLength: img.cropped?.length,
                                      originalLength: img.original?.length,
                                      timestamp: img.timestamp
                                    });
                                    // Fallback to original image
                                    const target = e.target as HTMLImageElement;
                                    if (img.cropped && target.src.includes(img.cropped.substring(0, 50))) {
                                      console.log('Falling back to original image');
                                      target.src = `data:image/jpeg;base64,${img.original}`;
                                    }
                                  }}
                                  className="card-img-top"
                                  style={{ height: '120px', objectFit: 'cover', transition: 'transform 0.2s' }}
                                  onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
                                  onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
                                  alt={`处理后图片 ${index + 1}`}
                                />
                                {/* 低分辨率标签 */}
                                {img.isLowResolution && (
                                  <span className="position-absolute bottom-0 start-0 badge bg-danger">
                                    分辨率太低
                                  </span>
                                )}
                                {/* 增强效果标签 */}
                                {img.enhancement && !img.isLowResolution && (
                                  <span className="position-absolute bottom-0 start-0 badge bg-primary">
                                    {img.enhancement}
                                  </span>
                                )}
                                {/* 删除按钮 */}
                                {onRemoveImage && (
                                  <button
                                    className="btn btn-sm position-absolute"
                                    style={{
                                      top: '4px',
                                      right: '4px',
                                      width: '24px',
                                      height: '24px',
                                      padding: '0',
                                      backgroundColor: 'rgba(220, 53, 69, 0.9)',
                                      border: 'none',
                                      borderRadius: '50%',
                                      color: 'white',
                                      fontSize: '14px',
                                      lineHeight: '1',
                                      zIndex: 10
                                    }}
                                    onClick={(e) => {
                                      e.preventDefault()
                                      e.stopPropagation()
                                      handleDeleteClick(img.id)
                                    }}
                                    onMouseEnter={(e) => {
                                      e.currentTarget.style.backgroundColor = 'rgba(220, 53, 69, 1)'
                                      e.currentTarget.style.transform = 'scale(1.1)'
                                    }}
                                    onMouseLeave={(e) => {
                                      e.currentTarget.style.backgroundColor = 'rgba(220, 53, 69, 0.9)'
                                      e.currentTarget.style.transform = 'scale(1)'
                                    }}
                                    title="从PDF中移除这张图片"
                                  >
                                    ×
                                  </button>
                                )}
                                {/* Click overlay hint */}
                                <div 
                                  className="position-absolute top-0 end-0 p-1"
                                  style={{ opacity: 0.7, right: onRemoveImage ? '32px' : '4px' }}
                                >
                                  <span className="badge bg-dark" style={{ fontSize: '10px' }}>
                                    🔍
                                  </span>
                                </div>
                                {/* Drag handle indicator */}
                                <div 
                                  className="position-absolute top-0 start-0 p-1"
                                  style={{ opacity: 0.7 }}
                                >
                                  <span className="badge bg-secondary" style={{ fontSize: '10px' }}>
                                    ⋮⋮
                                  </span>
                                </div>
                              </div>
                              <div className="card-body p-2">
                                <button
                                  className="btn btn-outline-primary btn-sm w-100"
                                  onClick={() => onManualAdjust(img.id)}
                                  style={{ fontSize: '11px' }}
                                >
                                  ✂️ 手动调整
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                        
                        {/* 上传占位框 */}
                        {onAdditionalFilesSelected && (
                          <div className="col-6 col-md-3">
                            <div 
                              className="card"
                              style={{
                                border: isDragOverUpload ? '2px dashed #007bff' : '2px dashed #dee2e6',
                                backgroundColor: isDragOverUpload ? '#f8f9fa' : '#fafafa',
                                transition: 'all 0.2s',
                                cursor: 'pointer',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                              }}
                              onClick={handleUploadClick}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = '#f0f0f0'
                                e.currentTarget.style.borderColor = '#007bff'
                              }}
                              onMouseLeave={(e) => {
                                if (!isDragOverUpload) {
                                  e.currentTarget.style.backgroundColor = '#fafafa'
                                  e.currentTarget.style.borderColor = '#dee2e6'
                                }
                              }}
                            >
                              {/* 模拟图片区域的高度 */}
                              <div style={{ height: '120px', objectFit: 'cover' }} className="card-img-top d-flex align-items-center justify-content-center">
                                {isScanning ? (
                                  <div className="d-flex flex-column align-items-center">
                                    <div className="spinner-border text-primary" role="status" style={{ width: '2rem', height: '2rem' }}>
                                      <span className="visually-hidden">扫描中...</span>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="d-flex flex-column align-items-center">
                                    <svg width="40" height="40" fill="currentColor" className="text-primary">
                                      <use xlinkHref="#plus-circle-icon" />
                                    </svg>
                                    <svg style={{ display: 'none' }}>
                                      <symbol id="plus-circle-icon" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                      </symbol>
                                    </svg>
                                  </div>
                                )}
                              </div>
                              <div className="card-body p-2">
                                {isScanning ? (
                                  <small className="text-muted text-center d-block" style={{ fontSize: '11px' }}>
                                    {scanMessage || '正在扫描...'}
                                  </small>
                                ) : (
                                  <small className="text-primary text-center d-block font-weight-bold" style={{ fontSize: '11px' }}>
                                    添加更多图片
                                  </small>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <div className="alert alert-info">
                        <small>
                          🎯 成功处理 {processedImages.length} 张图片 • 拖拽图片可调整顺序 • 点击图片查看大图 • 点击"手动调整"可微调裁剪区域
                          {onAdditionalFilesSelected && (
                            <> • 点击占位框或拖拽文件可追加上传更多图片</>
                          )}
                        </small>
                      </div>
                    </div>

                    {generatedPDF ? (
                      <div className="text-center">
                        <div className="mb-4">
                          <svg width="64" height="64" fill="currentColor" className="text-success">
                            <use xlinkHref="#check-icon" />
                          </svg>
                          <svg style={{ display: 'none' }}>
                            <symbol id="check-icon" viewBox="0 0 16 16">
                              <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                            </symbol>
                          </svg>
                        </div>
                        
                        <h4 className="mb-3 text-success">📄 PDF生成完成！</h4>
                        <div className="alert alert-success">
                          <div><strong>文件大小:</strong> {formatFileSize(generatedPDF.fileSize)}</div>
                          <div><strong>包含图片:</strong> {processedImages.length} 张</div>
                        </div>
                        
                        <div className={`d-flex ${isMobile ? 'flex-column' : 'justify-content-center'} gap-3`}>
                          <button 
                            className={`btn btn-success ${isMobile ? 'btn-lg w-100' : 'btn-lg'}`}
                            onClick={handleDownloadPDF}
                            style={{
                              minHeight: isMobile ? '50px' : 'auto',
                              fontSize: isMobile ? '18px' : 'inherit'
                            }}
                          >
                            📥 {isMobile ? '下载PDF' : '下载PDF文件'}
                          </button>
                          <button 
                            className={`btn btn-primary ${isMobile ? 'w-100' : ''}`}
                            onClick={handleResetClick}
                            style={{
                              minHeight: isMobile ? '44px' : 'auto'
                            }}
                          >
                            🔄 回到首页
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center">
                        <h5 className="mb-3">准备生成PDF</h5>
                        <p className="text-muted mb-4">
                          AI已处理完成，点击下方按钮生成PDF文件
                        </p>
                        
                        <div className={`d-flex ${isMobile ? 'flex-column' : 'justify-content-center'} gap-3`}>
                          <button 
                            className={`btn btn-primary ${isMobile ? 'btn-lg w-100' : 'btn-lg'}`}
                            onClick={handleDownloadPDF}
                            disabled={isGenerating}
                            style={{
                              minHeight: isMobile ? '50px' : 'auto',
                              fontSize: isMobile ? '18px' : 'inherit'
                            }}
                          >
                            {isGenerating ? '生成中...' : '📄 生成PDF'}
                          </button>
                          <button 
                            className={`btn btn-outline-secondary ${isMobile ? 'w-100' : ''}`}
                            onClick={handleResetClick}
                            style={{
                              minHeight: isMobile ? '44px' : 'auto'
                            }}
                          >
                            🔄 放弃生成，回到首页
                          </button>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div 
                    className="text-center p-md-5"
                    onDragOver={handleUploadDragOver}
                    onDragLeave={handleUploadDragLeave}
                    onDrop={handleUploadDrop}
                  >
                    <h4 className="mb-3">图片列表为空</h4>
                    <p className="text-muted mb-4">
                      所有图片都已移除。您可以添加新的图片或返回首页。
                    </p>
                    <div className="d-inline-block mb-4">
                      <div 
                        className="card"
                        style={{
                          width: '150px',
                          border: isDragOverUpload ? '2px dashed #007bff' : '2px dashed #dee2e6',
                          backgroundColor: isDragOverUpload ? '#f8f9fa' : '#fafafa',
                          transition: 'all 0.2s',
                          cursor: 'pointer',
                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                        }}
                        onClick={handleUploadClick}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#f0f0f0'
                          e.currentTarget.style.borderColor = '#007bff'
                        }}
                        onMouseLeave={(e) => {
                          if (!isDragOverUpload) {
                            e.currentTarget.style.backgroundColor = '#fafafa'
                            e.currentTarget.style.borderColor = '#dee2e6'
                          }
                        }}
                      >
                        <div style={{ height: '120px' }} className="d-flex align-items-center justify-content-center">
                          {isScanning ? (
                            <div className="spinner-border text-primary" role="status">
                              <span className="visually-hidden">扫描中...</span>
                            </div>
                          ) : (
                            <div className="text-primary">
                              <svg width="40" height="40" fill="currentColor">
                                <use xlinkHref="#plus-circle-icon" />
                              </svg>
                              <svg style={{ display: 'none' }}>
                                <symbol id="plus-circle-icon" viewBox="0 0 16 16">
                                  <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                  <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </symbol>
                              </svg>
                            </div>
                          )}
                        </div>
                        <div className="card-body p-2">
                          <small className="text-primary font-weight-bold">
                            {isScanning ? (scanMessage || '正在扫描...') : '添加更多图片'}
                          </small>
                        </div>
                      </div>
                    </div>
                    <div className="mt-2">
                      <button 
                        className={`btn btn-outline-secondary ${isMobile ? 'w-100' : ''}`}
                        onClick={onReset}
                      >
                        🔄 回到首页
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
        
      </div>

      {/* 隐藏的文件输入元素 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*"
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />

      {showConfirmDialog && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1050,
          }}
        >
          <div
            className="card shadow-lg"
            style={{ maxWidth: '400px', width: '90%' }}
          >
            <div className="card-body p-4">
              {generatedPDF && !pdfDownloaded ? (
                <>
                  <h5 className="card-title">确认回到首页？</h5>
                  <p className="card-text">
                    您还没有下载生成的PDF文件。回到首页这个PDF文件就
                    <strong style={{ color: 'red' }}>丢失</strong>
                    了，您要再下载就要重新生成。您确定要回到首页吗？
                  </p>
                  <div className="d-flex justify-content-end gap-2 mt-4">
                    <button
                      className="btn btn-primary"
                      onClick={handleDownloadAndReset}
                    >
                      先下载PDF
                    </button>
                    <button
                      className="btn btn-danger"
                      onClick={handleConfirmReset}
                    >
                      PDF不要了，回首页
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <h5 className="card-title">确认放弃？</h5>
                  <p className="card-text">
                    回到首页，处理完的图片会
                    <strong style={{ color: 'red' }}>全部丢失</strong>
                    ，你真要放弃吗？
                  </p>
                  <div className="d-flex justify-content-end gap-2 mt-4">
                    <button
                      className="btn btn-secondary"
                      onClick={handleCancelReset}
                    >
                      继续编辑
                    </button>
                    <button
                      className="btn btn-danger"
                      onClick={handleConfirmReset}
                    >
                      确认放弃
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 删除图片确认对话框 */}
      {showDeleteDialog && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1060,
          }}
        >
          <div
            className="card shadow-lg"
            style={{ maxWidth: '400px', width: '90%' }}
          >
            <div className="card-body p-4">
              <h5 className="card-title">从PDF里去掉这张图片？</h5>
              <p className="card-text">
                这张图片将从最终的PDF中移除，但不会影响其他图片。
              </p>
              <div className="d-flex justify-content-end gap-2 mt-4">
                <button
                  className="btn btn-secondary"
                  onClick={handleDeleteCancel}
                >
                  不，我手滑了
                </button>
                <button
                  className="btn btn-danger"
                  onClick={handleDeleteConfirm}
                >
                  是的，去掉
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Viewer */}
      <ImageViewer
        images={processedImages}
        initialIndex={selectedImageIndex}
        isOpen={showImageViewer}
        onClose={closeImageViewer}
        onManualAdjust={onManualAdjust}
      />
    </div>
  )
}

export default ProgressIndicator