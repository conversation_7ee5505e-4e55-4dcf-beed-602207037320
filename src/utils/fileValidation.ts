// 文件上传前的客户端验证工具

export interface ValidationLimits {
  maxFileSize: number; // 单个文件最大大小 (bytes)
  maxTotalSize: number; // 批量上传总大小 (bytes)
  maxFilesPerRequest: number; // 单次请求最大文件数
  allowedMimeTypes: string[]; // 允许的MIME类型
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError extends Error {
  type: 'FILE_TOO_LARGE' | 'BATCH_TOO_LARGE' | 'TOO_MANY_FILES' | 'INVALID_FILE_TYPE' | 'INVALID_FILE_NAME';
  fileName?: string;
  suggestion?: string;
  details?: any;
}

export interface ValidationWarning {
  type: 'LARGE_FILE' | 'MANY_FILES';
  message: string;
  suggestion?: string;
}

// 默认限制配置 (应该与后端配置保持一致)
export const DEFAULT_LIMITS: ValidationLimits = {
  maxFileSize: 50 * 1024 * 1024, // 50MB
  maxTotalSize: 200 * 1024 * 1024, // 200MB  
  maxFilesPerRequest: 50,
  allowedMimeTypes: [
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
    'image/webp', 'image/bmp', 'image/tiff'
  ]
};

/**
 * 格式化文件大小为可读字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * 验证单个文件
 */
export function validateFile(file: File, limits: ValidationLimits = DEFAULT_LIMITS): ValidationError[] {
  const errors: ValidationError[] = [];

  // 检查文件大小
  if (file.size > limits.maxFileSize) {
    const error = new Error(`文件 "${file.name}" 太大，单个文件不能超过 ${formatFileSize(limits.maxFileSize)}`) as ValidationError;
    error.type = 'FILE_TOO_LARGE';
    error.fileName = file.name;
    error.suggestion = `请压缩图片或选择较小的文件`;
    error.details = {
      currentSize: file.size,
      currentSizeFormatted: formatFileSize(file.size),
      maxSize: limits.maxFileSize,
      maxSizeFormatted: formatFileSize(limits.maxFileSize)
    };
    errors.push(error);
  }

  // 检查文件类型
  if (!limits.allowedMimeTypes.includes(file.type)) {
    const error = new Error(`文件 "${file.name}" 不是支持的图片格式`) as ValidationError;
    error.type = 'INVALID_FILE_TYPE';
    error.fileName = file.name;
    error.suggestion = '请选择 JPEG、PNG、GIF、WebP、BMP 或 TIFF 格式的图片文件';
    error.details = {
      currentType: file.type,
      allowedTypes: limits.allowedMimeTypes
    };
    errors.push(error);
  }

  // 检查文件名安全性
  if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
    const error = new Error(`文件名 "${file.name}" 包含非法字符`) as ValidationError;
    error.type = 'INVALID_FILE_NAME';
    error.fileName = file.name;
    error.suggestion = '请重命名文件，移除 ".."、"/" 或 "\\" 字符';
    errors.push(error);
  }

  return errors;
}

/**
 * 验证文件列表
 */
export function validateFiles(files: File[], limits: ValidationLimits = DEFAULT_LIMITS): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // 检查文件数量
  if (files.length > limits.maxFilesPerRequest) {
    const error = new Error(`一次最多只能上传 ${limits.maxFilesPerRequest} 个文件，您选择了 ${files.length} 个文件`) as ValidationError;
    error.type = 'TOO_MANY_FILES';
    error.suggestion = `请减少文件数量或分批上传`;
    error.details = {
      currentCount: files.length,
      maxCount: limits.maxFilesPerRequest
    };
    errors.push(error);
  } else if (files.length > 20) {
    warnings.push({
      type: 'MANY_FILES',
      message: `您选择了 ${files.length} 个文件，处理可能需要一些时间`,
      suggestion: '如需加快处理速度，可以考虑分批上传'
    });
  }

  // 检查总文件大小
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  if (totalSize > limits.maxTotalSize) {
    const excessSize = totalSize - limits.maxTotalSize;
    const error = new Error(`批量上传文件总大小超过限制，当前 ${formatFileSize(totalSize)}，限制 ${formatFileSize(limits.maxTotalSize)}`) as ValidationError;
    error.type = 'BATCH_TOO_LARGE';
    error.suggestion = `请删除约 ${formatFileSize(excessSize)} 的文件，或分批上传`;
    error.details = {
      currentSize: totalSize,
      currentSizeFormatted: formatFileSize(totalSize),
      maxSize: limits.maxTotalSize,
      maxSizeFormatted: formatFileSize(limits.maxTotalSize),
      excessSize: excessSize,
      excessSizeFormatted: formatFileSize(excessSize)
    };
    errors.push(error);
  } else if (totalSize > limits.maxTotalSize * 0.8) {
    warnings.push({
      type: 'LARGE_FILE',
      message: `文件总大小较大 (${formatFileSize(totalSize)})，上传可能需要较长时间`,
      suggestion: '请确保网络连接稳定'
    });
  }

  // 验证每个文件
  files.forEach(file => {
    const fileErrors = validateFile(file, limits);
    errors.push(...fileErrors);
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 获取建议的分批上传方案
 */
export function getSplitSuggestion(files: File[], limits: ValidationLimits = DEFAULT_LIMITS): File[][] {
  const batches: File[][] = [];
  let currentBatch: File[] = [];
  let currentBatchSize = 0;

  const validFiles = files.filter(file => validateFile(file, limits).length === 0);

  for (const file of validFiles) {
    // 如果加入这个文件会超出限制，开始新的批次
    if (currentBatch.length >= limits.maxFilesPerRequest ||
        currentBatchSize + file.size > limits.maxTotalSize) {
      if (currentBatch.length > 0) {
        batches.push(currentBatch);
        currentBatch = [];
        currentBatchSize = 0;
      }
    }

    currentBatch.push(file);
    currentBatchSize += file.size;
  }

  // 添加最后一个批次
  if (currentBatch.length > 0) {
    batches.push(currentBatch);
  }

  return batches;
}