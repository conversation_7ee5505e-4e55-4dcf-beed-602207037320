// 移动端设备检测工具

// 检测是否为移动设备
export function isMobileDevice(): boolean {
  // 检查用户代理字符串
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android', 'webos', 'iphone', 'ipad', 'ipod', 
    'blackberry', 'windows phone', 'mobile'
  ]
  
  const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword))
  
  // 检查屏幕尺寸
  const isSmallScreen = window.innerWidth <= 768
  
  // 检查触摸支持
  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  
  return isMobileUA || (isSmallScreen && hasTouchSupport)
}

// 检测是否为iOS设备
export function isIOSDevice(): boolean {
  const userAgent = navigator.userAgent.toLowerCase()
  return /iphone|ipad|ipod/.test(userAgent)
}

// 检测是否为Android设备
export function isAndroidDevice(): boolean {
  const userAgent = navigator.userAgent.toLowerCase()
  return /android/.test(userAgent)
}

// 检测是否支持Web Share API
export function supportsWebShare(): boolean {
  return typeof navigator.share === 'function'
}

// 获取屏幕类型
export function getScreenType(): 'mobile' | 'tablet' | 'desktop' {
  const width = window.innerWidth
  
  if (width < 768) {
    return 'mobile'
  } else if (width < 1024) {
    return 'tablet'
  } else {
    return 'desktop'
  }
}

// 获取安全的视口高度（考虑移动端地址栏）
export function getSafeViewportHeight(): number {
  // 在移动端，使用visualViewport API获取更准确的可视区域高度
  if ('visualViewport' in window && window.visualViewport) {
    return window.visualViewport.height
  }
  
  // 回退到标准方法
  return window.innerHeight
}

// 检测是否为全屏模式（PWA或全屏浏览器）
export function isFullscreenMode(): boolean {
  // 检查PWA显示模式
  if (window.matchMedia('(display-mode: fullscreen)').matches ||
      window.matchMedia('(display-mode: standalone)').matches) {
    return true
  }
  
  // 检查全屏API
  return Boolean(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  )
}

/**
 * 检测是否在WebView环境中
 */
export function isInWebView(): boolean {
  const userAgent = navigator.userAgent
  
  // 检测Android WebView
  if (/Android.*wv|Android.*Version\/\d+\.\d+.*Chrome/i.test(userAgent)) {
    return true
  }
  
  // 检测iOS WebView (WKWebView)
  if (/iPhone.*Mobile.*Safari/i.test(userAgent) && !/Safari/i.test(userAgent)) {
    return true
  }
  
  // 检测其他WebView标识
  if (/WebView|wv/i.test(userAgent)) {
    return true
  }
  
  return false
}

/**
 * 检测是否为天燕客户端WebView环境
 */
export function isInTianyanClient(): boolean {
  // 检测iOS天燕客户端（通过webkit messageHandlers）
  if (window.webkit?.messageHandlers) {
    return true
  }
  
  // 检测Android天燕客户端（通过注入的Android对象）
  if (window.Android) {
    return true
  }
  
  return false
}

/**
 * 检测是否在微信环境中
 */
export function isInWeChat(): boolean {
  const userAgent = navigator.userAgent.toLowerCase()
  return /micromessenger/i.test(userAgent)
}

/**
 * 检测是否需要显示微信下载引导
 */
export function shouldShowWeChatGuidance(): boolean {
  return isInWeChat()
}

/**
 * 检测是否应该隐藏应用下载按钮
 * 修改：只在天燕客户端WebView中隐藏，微信环境显示但触发引导
 */
export function shouldHideAppDownloadButtons(): boolean {
  return isInTianyanClient()
}

/**
 * 检测是否支持原生桥接
 */
export function supportsNativeBridge(): boolean {
  // 检测iOS桥接
  if (window.webkit?.messageHandlers?.saveFile) {
    return true
  }
  
  // 检测Android桥接
  if (window.Android?.saveFile) {
    return true
  }
  
  return false
}
