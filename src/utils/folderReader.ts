// 扩展DataTransferItem类型以支持现代API
declare global {
  interface DataTransferItem {
    getAsFileSystemHandle(): Promise<any>
    webkitGetAsEntry(): any
  }
}

// 支持的图片格式
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
  'image/svg+xml'
]

// 文件夹扫描结果接口
export interface FolderScanResult {
  files: File[]
  folderName?: string
}

// 检查文件是否为图片
function isImageFile(file: File): boolean {
  return SUPPORTED_IMAGE_TYPES.includes(file.type.toLowerCase())
}

// 清理文件名，移除不安全字符
function sanitizeFileName(fileName: string): string {
  // 移除或替换不安全的文件名字符
  return fileName
    .replace(/[/\\:*?"<>|]/g, '_')
    .replace(/\s+/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_|_$/g, '')
    .substring(0, 50) // 限制长度
}

// 检查是否支持现代File System Access API
function supportsFileSystemAccessAPI(): boolean {
  return typeof (DataTransferItem.prototype as any).getAsFileSystemHandle === 'function'
}

// 检查是否支持webkitGetAsEntry
function supportsWebkitGetAsEntry(): boolean {
  return typeof (DataTransferItem.prototype as any).webkitGetAsEntry === 'function'
}

// 使用现代API读取文件夹
async function readDirectoryWithModernAPI(dirHandle: any, collectFolderName?: (name: string) => void): Promise<File[]> {
  const files: File[] = []
  
  // 收集文件夹名称（仅第一次调用时）
  if (collectFolderName) {
    collectFolderName(dirHandle.name)
  }
  
  try {
    for await (const entry of dirHandle.values()) {
      if (entry.kind === 'file') {
        const file = await entry.getFile()
        if (isImageFile(file)) {
          files.push(file)
        }
      } else if (entry.kind === 'directory') {
        // 递归读取子文件夹（不再收集文件夹名）
        const subFiles = await readDirectoryWithModernAPI(entry)
        files.push(...subFiles)
      }
    }
  } catch (error) {
    console.warn('读取文件夹时出错:', error)
  }
  
  return files
}

// 使用传统API读取文件夹
async function readDirectoryWithLegacyAPI(dirEntry: any, collectFolderName?: (name: string) => void): Promise<File[]> {
  return new Promise((resolve) => {
    const files: File[] = []
    
    // 收集文件夹名称（仅第一次调用时）
    if (collectFolderName) {
      collectFolderName(dirEntry.name)
    }
    
    function readEntries(dirReader: any) {
      dirReader.readEntries(async (entries: any[]) => {
        if (entries.length === 0) {
          resolve(files)
          return
        }
        
        for (const entry of entries) {
          if (entry.isFile) {
            try {
              const file = await new Promise<File>((resolve, reject) => {
                entry.file(resolve, reject)
              })
              if (isImageFile(file)) {
                files.push(file)
              }
            } catch (error) {
              console.warn('读取文件时出错:', error)
            }
          } else if (entry.isDirectory) {
            // 递归读取子文件夹（不再收集文件夹名）
            const subFiles = await readDirectoryWithLegacyAPI(entry)
            files.push(...subFiles)
          }
        }
        
        // 继续读取剩余条目（Chrome有限制，需要多次调用）
        readEntries(dirReader)
      })
    }
    
    const dirReader = dirEntry.createReader()
    readEntries(dirReader)
  })
}

// 从DataTransfer中提取所有文件（包括文件夹中的文件）
export async function extractFilesFromDataTransfer(
  dataTransfer: DataTransfer,
  onProgress?: (message: string) => void
): Promise<FolderScanResult> {
  const allFiles: File[] = []
  const items = Array.from(dataTransfer.items)
  
  // 检查是否包含文件夹
  let hasFolders = false
  let folderName: string | undefined
  
  if (onProgress) {
    onProgress('正在分析拖拽内容...')
  }
  
  for (const item of items) {
    if (item.kind !== 'file') continue
    
    let entry: any = null
    
    // 尝试使用现代API
    if (supportsFileSystemAccessAPI() && (item as any).getAsFileSystemHandle) {
      try {
        entry = await (item as any).getAsFileSystemHandle()
        if (entry.kind === 'directory') {
          hasFolders = true
          if (onProgress) {
            onProgress(`正在扫描文件夹: ${entry.name}...`)
          }
          const files = await readDirectoryWithModernAPI(entry, (name) => {
            if (!folderName) folderName = sanitizeFileName(name)
          })
          allFiles.push(...files)
        } else {
          const file = await entry.getFile()
          if (isImageFile(file)) {
            allFiles.push(file)
          }
        }
        continue
      } catch (error) {
        // 回退到传统方法
      }
    }
    
    // 尝试使用传统API
    if (supportsWebkitGetAsEntry() && (item as any).webkitGetAsEntry) {
      entry = (item as any).webkitGetAsEntry()
      if (entry) {
        if (entry.isDirectory) {
          hasFolders = true
          if (onProgress) {
            onProgress(`正在扫描文件夹: ${entry.name}...`)
          }
          const files = await readDirectoryWithLegacyAPI(entry, (name) => {
            if (!folderName) folderName = sanitizeFileName(name)
          })
          allFiles.push(...files)
        } else if (entry.isFile) {
          const file = item.getAsFile()
          if (file && isImageFile(file)) {
            allFiles.push(file)
          }
        }
        continue
      }
    }
    
    // 回退到基础文件处理
    const file = item.getAsFile()
    if (file && isImageFile(file)) {
      allFiles.push(file)
    }
  }
  
  if (onProgress) {
    if (hasFolders) {
      onProgress(`扫描完成，找到 ${allFiles.length} 个图片文件`)
    } else {
      onProgress('文件处理完成')
    }
  }
  
  return {
    files: allFiles,
    folderName: folderName
  }
}

// 检查DataTransfer是否包含文件夹
export function containsFolders(dataTransfer: DataTransfer): boolean {
  const items = Array.from(dataTransfer.items)
  
  for (const item of items) {
    if (item.kind !== 'file') continue
    
    // 检查现代API
    if (supportsFileSystemAccessAPI()) {
      // 现代API是异步的，这里只能做基础检测
      return true // 假设可能包含文件夹，让后续处理来确定
    }
    
    // 检查传统API
    if (supportsWebkitGetAsEntry() && (item as any).webkitGetAsEntry) {
      const entry = (item as any).webkitGetAsEntry()
      if (entry && entry.isDirectory) {
        return true
      }
    }
  }
  
  return false
}

// 检查浏览器是否支持文件夹拖拽
export function supportsFolderDrop(): boolean {
  return supportsFileSystemAccessAPI() || supportsWebkitGetAsEntry()
}