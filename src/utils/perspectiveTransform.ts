/**
 * 透视变换工具
 * 实现四点透视矫正，将任意四边形区域矫正为矩形
 */

export interface Point {
  x: number
  y: number
}

/**
 * 计算透视变换矩阵
 * 将源四边形变换为目标矩形
 */
export function calculatePerspectiveMatrix(
  srcPoints: Point[],
  dstWidth: number,
  dstHeight: number
): number[] {
  // 目标矩形的四个角点（左上、右上、右下、左下）
  const dst = [
    { x: 0, y: 0 },
    { x: dstWidth, y: 0 },
    { x: dstWidth, y: dstHeight },
    { x: 0, y: dstHeight }
  ]

  // 计算透视变换矩阵 (3x3)
  // 使用齐次坐标系和线性方程组求解
  const A: number[][] = []
  const B: number[] = []

  for (let i = 0; i < 4; i++) {
    const sx = srcPoints[i].x
    const sy = srcPoints[i].y
    const dx = dst[i].x
    const dy = dst[i].y

    // 对于每个点，我们有两个方程：
    // dx = (a*sx + b*sy + c) / (g*sx + h*sy + 1)
    // dy = (d*sx + e*sy + f) / (g*sx + h*sy + 1)
    
    // 重新排列得到线性方程
    A.push([sx, sy, 1, 0, 0, 0, -dx * sx, -dx * sy])
    B.push(dx)
    A.push([0, 0, 0, sx, sy, 1, -dy * sx, -dy * sy])
    B.push(dy)
  }

  // 使用高斯消元法求解线性方程组
  const solution = solveLinearSystem(A, B)
  
  // 返回3x3变换矩阵的扁平化数组
  // [a, b, c, d, e, f, g, h, 1]
  return [...solution, 1]
}

/**
 * 应用透视变换到Canvas
 */
export function applyPerspectiveTransform(
  canvas: HTMLCanvasElement,
  sourceImage: HTMLImageElement,
  srcPoints: Point[],
  outputWidth: number,
  outputHeight: number
): void {
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 设置输出尺寸
  canvas.width = outputWidth
  canvas.height = outputHeight

  // 清除画布
  ctx.clearRect(0, 0, outputWidth, outputHeight)

  // 使用双线性插值进行透视变换
  const imageData = ctx.createImageData(outputWidth, outputHeight)
  const data = imageData.data

  // 创建临时canvas来读取源图像像素
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')
  if (!tempCtx) return

  tempCanvas.width = sourceImage.width
  tempCanvas.height = sourceImage.height
  tempCtx.drawImage(sourceImage, 0, 0)
  const sourceData = tempCtx.getImageData(0, 0, sourceImage.width, sourceImage.height)

  // 计算逆变换矩阵（从目标坐标到源坐标）

  // 对每个输出像素进行变换
  for (let y = 0; y < outputHeight; y++) {
    for (let x = 0; x < outputWidth; x++) {
      // 将输出坐标映射到源图像坐标
      const srcCoord = transformPoint(x, y, srcPoints, outputWidth, outputHeight)
      
      if (srcCoord.x >= 0 && srcCoord.x < sourceImage.width && 
          srcCoord.y >= 0 && srcCoord.y < sourceImage.height) {
        
        // 使用双线性插值获取像素值
        const color = bilinearInterpolation(sourceData, srcCoord.x, srcCoord.y, sourceImage.width, sourceImage.height)
        
        const outputIndex = (y * outputWidth + x) * 4
        data[outputIndex] = color.r
        data[outputIndex + 1] = color.g
        data[outputIndex + 2] = color.b
        data[outputIndex + 3] = 255
      }
    }
  }

  ctx.putImageData(imageData, 0, 0)
}

/**
 * 简化的透视变换实现
 * 使用双线性映射近似透视变换
 */
function transformPoint(x: number, y: number, srcPoints: Point[], dstWidth: number, dstHeight: number): Point {
  // 将输出坐标归一化到[0,1]范围
  const u = x / dstWidth
  const v = y / dstHeight

  // 使用双线性插值计算源坐标
  // 四个角点的双线性插值
  const topLeft = srcPoints[0]     // 左上
  const topRight = srcPoints[1]    // 右上
  const bottomRight = srcPoints[2] // 右下
  const bottomLeft = srcPoints[3]  // 左下

  // 先在水平方向插值
  const top = {
    x: topLeft.x * (1 - u) + topRight.x * u,
    y: topLeft.y * (1 - u) + topRight.y * u
  }
  
  const bottom = {
    x: bottomLeft.x * (1 - u) + bottomRight.x * u,
    y: bottomLeft.y * (1 - u) + bottomRight.y * u
  }

  // 再在垂直方向插值
  return {
    x: top.x * (1 - v) + bottom.x * v,
    y: top.y * (1 - v) + bottom.y * v
  }
}

/**
 * 双线性插值获取像素颜色
 */
function bilinearInterpolation(
  imageData: ImageData, 
  x: number, 
  y: number, 
  width: number, 
  height: number
): { r: number, g: number, b: number } {
  const x1 = Math.floor(x)
  const y1 = Math.floor(y)
  const x2 = Math.min(x1 + 1, width - 1)
  const y2 = Math.min(y1 + 1, height - 1)

  const dx = x - x1
  const dy = y - y1

  // 获取四个邻近像素
  const getPixel = (px: number, py: number) => {
    const index = (py * width + px) * 4
    return {
      r: imageData.data[index],
      g: imageData.data[index + 1],
      b: imageData.data[index + 2]
    }
  }

  const c1 = getPixel(x1, y1) // 左上
  const c2 = getPixel(x2, y1) // 右上
  const c3 = getPixel(x1, y2) // 左下
  const c4 = getPixel(x2, y2) // 右下

  // 双线性插值
  const interpolate = (a: number, b: number, c: number, d: number) => {
    return a * (1 - dx) * (1 - dy) + 
           b * dx * (1 - dy) + 
           c * (1 - dx) * dy + 
           d * dx * dy
  }

  return {
    r: Math.round(interpolate(c1.r, c2.r, c3.r, c4.r)),
    g: Math.round(interpolate(c1.g, c2.g, c3.g, c4.g)),
    b: Math.round(interpolate(c1.b, c2.b, c3.b, c4.b))
  }
}

/**
 * 求解线性方程组 (简化版高斯消元法)
 */
function solveLinearSystem(A: number[][], B: number[]): number[] {
  const n = A.length
  const augmented = A.map((row, i) => [...row, B[i]])

  // 前向消元
  for (let i = 0; i < n; i++) {
    // 找到主元
    let maxRow = i
    for (let k = i + 1; k < n; k++) {
      if (Math.abs(augmented[k][i]) > Math.abs(augmented[maxRow][i])) {
        maxRow = k
      }
    }
    
    // 交换行
    [augmented[i], augmented[maxRow]] = [augmented[maxRow], augmented[i]]

    // 消元
    for (let k = i + 1; k < n; k++) {
      const factor = augmented[k][i] / augmented[i][i]
      for (let j = i; j <= n; j++) {
        augmented[k][j] -= factor * augmented[i][j]
      }
    }
  }

  // 回代求解
  const solution = new Array(n)
  for (let i = n - 1; i >= 0; i--) {
    solution[i] = augmented[i][n]
    for (let j = i + 1; j < n; j++) {
      solution[i] -= augmented[i][j] * solution[j]
    }
    solution[i] /= augmented[i][i]
  }

  return solution
}

/**
 * 计算四边形的面积（用于确定输出尺寸）
 */
export function calculateQuadrilateralArea(points: Point[]): number {
  // 使用鞋带公式计算多边形面积
  let area = 0
  for (let i = 0; i < points.length; i++) {
    const j = (i + 1) % points.length
    area += points[i].x * points[j].y - points[j].x * points[i].y
  }
  return Math.abs(area) / 2
}

/**
 * 计算输出图像的合适尺寸
 */
export function calculateOutputDimensions(points: Point[], maxWidth = 800): {width: number, height: number} {
  // 计算边长
  const topWidth = Math.sqrt(
    Math.pow(points[1].x - points[0].x, 2) + Math.pow(points[1].y - points[0].y, 2)
  )
  const bottomWidth = Math.sqrt(
    Math.pow(points[2].x - points[3].x, 2) + Math.pow(points[2].y - points[3].y, 2)
  )
  const leftHeight = Math.sqrt(
    Math.pow(points[3].x - points[0].x, 2) + Math.pow(points[3].y - points[0].y, 2)
  )
  const rightHeight = Math.sqrt(
    Math.pow(points[2].x - points[1].x, 2) + Math.pow(points[2].y - points[1].y, 2)
  )

  // 取平均值
  const avgWidth = (topWidth + bottomWidth) / 2
  const avgHeight = (leftHeight + rightHeight) / 2
  
  // 保持宽高比，限制最大宽度
  const aspectRatio = avgHeight / avgWidth
  const outputWidth = Math.min(maxWidth, avgWidth)
  const outputHeight = outputWidth * aspectRatio

  return {
    width: Math.round(outputWidth),
    height: Math.round(outputHeight)
  }
}