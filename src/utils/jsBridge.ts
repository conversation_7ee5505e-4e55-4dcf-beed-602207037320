type NoPermissionType = 'CAMERA' | 'ALBUM' | 'AUDIO';

export const isPermissions = (type: NoPermissionType): Promise<boolean> => {
  return new Promise(resolve => {
    if (window.webkit?.messageHandlers?.permission) {
      window.__permission_ios = undefined;
      
      let attempts = 0;
      const maxAttempts = 60;
      const checkInterval = 50;

      const checkPermission = () => {
        attempts++;
        
        if (window.__permission_ios !== undefined) {
          resolve(window.__permission_ios);
        } else if (attempts < maxAttempts) {
          setTimeout(checkPermission, checkInterval);
        } else {
          resolve(true);
        }
      };

      window.webkit.messageHandlers.permission.postMessage(type);
      setTimeout(checkPermission, checkInterval);
    } else {
      resolve(true);
    }
  });
};

// 处理flutter权限回调  1: 有权限  2: 无权限
window.handleFlutterResult = (num: number) => {
  if (+num === 2) {
    window.__permission_ios = false;
  } else {
    window.__permission_ios = true;
  }
};


export const openFilePicker = (allowMultiple: boolean): Promise<File[]> => {
  return new Promise((resolve, reject) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = allowMultiple;
    input.accept = 'image/*';

    input.onchange = (e: Event) => {
      const files = (e.target as HTMLInputElement).files;
      if (files) {
        resolve(Array.from(files));
      } else {
        reject(new Error('No files selected'));
      }
    };

    input.click();
  });
};