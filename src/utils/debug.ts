
export function createDebugLogger() {
  const debugContainer = document.createElement('div');
  debugContainer.id = 'debug-log-container';
  debugContainer.style.position = 'fixed';
  debugContainer.style.bottom = '10px';
  debugContainer.style.left = '10px';
  debugContainer.style.width = 'calc(100% - 20px)';
  debugContainer.style.maxHeight = '200px';
  debugContainer.style.overflowY = 'auto';
  debugContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  debugContainer.style.color = '#0f0';
  debugContainer.style.padding = '10px';
  debugContainer.style.zIndex = '9999';
  debugContainer.style.fontSize = '12px';
  debugContainer.style.fontFamily = 'monospace';
  debugContainer.style.whiteSpace = 'pre-wrap';
  document.body.appendChild(debugContainer);

  return (message: string) => {
    const time = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${time}] ${message}`;
    debugContainer.appendChild(logEntry);
    // Scroll to the bottom
    debugContainer.scrollTop = debugContainer.scrollHeight;
  };
}

export const debugLog = createDebugLogger();
