export interface CropData {
  x: number
  y: number
  width: number
  height: number
}

export interface ProcessedImage {
  canvas: HTMLCanvasElement
  originalFile: File
}

export async function processImages(
  files: File[],
  cropData: CropData,
  onProgress?: (progress: number) => void
): Promise<ProcessedImage[]> {
  const processedImages: ProcessedImage[] = []
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    const processedImage = await cropImage(file, cropData)
    processedImages.push(processedImage)
    
    if (onProgress) {
      onProgress(Math.round(((i + 1) / files.length) * 80))
    }
  }
  
  return processedImages
}

async function cropImage(file: File, cropData: CropData): Promise<ProcessedImage> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        if (!ctx) {
          throw new Error('无法创建canvas上下文')
        }
        
        canvas.width = cropData.width
        canvas.height = cropData.height
        
        ctx.drawImage(
          img,
          cropData.x,
          cropData.y,
          cropData.width,
          cropData.height,
          0,
          0,
          cropData.width,
          cropData.height
        )
        
        resolve({
          canvas,
          originalFile: file
        })
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = () => {
      reject(new Error(`无法加载图片: ${file.name}`))
    }
    
    img.src = URL.createObjectURL(file)
  })
}