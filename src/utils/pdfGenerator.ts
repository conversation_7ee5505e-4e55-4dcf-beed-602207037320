import jsPDF from 'jspdf'
import { saveAs } from 'file-saver'
import { ProcessedImage } from './imageProcessor'
import { isMobileDevice, supportsWebShare, isInWebView, supportsNativeBridge } from './deviceDetection'

function getDefaultFileName(folderName?: string): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const dateStr = `${year}${month}${day}`
  
  if (folderName) {
    return `${folderName}_${dateStr}.pdf`
  } else {
    return `生成的PDF_${dateStr}.pdf`
  }
}


// 移动端分享API保存
async function saveWithWebShare(blob: Blob, fileName: string): Promise<void> {
  try {
    const file = new File([blob], fileName, { type: 'application/pdf' })
    
    if (navigator.share) {
      await navigator.share({
        // title: 'PDF文件',
        // text: '生成的PDF文件',
        files: [file]
      })
    } else {
      throw new Error('Web Share API not supported')
    }
  } catch (error: any) {
    if (error.name !== 'AbortError') {
      // 如果分享失败，回退到传统下载
      throw error
    }
  }
}


// 回退方案：使用prompt + file-saver
async function saveWithFallback(
  blob: Blob,
  defaultFileName: string
): Promise<void> {
  const fileName = prompt('请输入PDF文件名:', defaultFileName)
  
  if (fileName === null) {
    return // 用户取消
  }
  
  const finalName =
    fileName.trim() === ''
      ? defaultFileName
      : fileName.endsWith('.pdf')
      ? fileName
      : fileName + '.pdf'
  
  saveAs(blob, finalName)
}

/**
 * 将Blob转换为Base64
 */
function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const result = reader.result as string
      // 移除data:application/pdf;base64,前缀，只保留base64数据
      const base64Data = result.split(',')[1]
      resolve(base64Data)
    }
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

/**
 * 使用原生桥接下载PDF
 */
async function saveWithNativeBridge(
  blob: Blob,
  fileName: string
): Promise<void> {
  try {
    const base64Data = await blobToBase64(blob)

    // iOS WebView (WKWebView)
    if (window.webkit?.messageHandlers?.saveFile) {
      const params = JSON.stringify({
        data: base64Data,
        fileName: fileName,
        mimeType: 'application/pdf'
      })
      console.log('使用iOS原生桥接下载PDF')
      window.webkit?.messageHandlers?.saveFile?.postMessage(params)
      return
    }
    
    // Android WebView
    if (window.Android?.saveFile) {
      console.log('使用Android原生桥接下载PDF')
      window.Android.saveFile(base64Data, fileName)
      return
    }
    
    throw new Error('原生桥接接口不可用')
  } catch (error) {
    console.error('原生桥接下载失败:', error)
    throw error
  }
}

/**
 * 备用下载方案：创建下载链接
 */
async function saveWithDownloadLink(
  blob: Blob,
  fileName: string
): Promise<void> {
  try {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(url), 1000)
  } catch (error) {
    console.error('下载链接创建失败:', error)
    throw error
  }
}

export async function generatePDF(
  processedImages: ProcessedImage[],
  folderName?: string
): Promise<void> {
  if (processedImages.length === 0) {
    throw new Error('没有图片可以生成PDF')
  }

  // 获取第一张图片的尺寸来创建PDF
  const firstCanvas = processedImages[0].canvas
  const pageWidth = firstCanvas.width * 0.75
  const pageHeight = firstCanvas.height * 0.75
  
  const pdf = new jsPDF({
    orientation: pageWidth > pageHeight ? 'landscape' : 'portrait',
    unit: 'pt',
    format: [pageWidth, pageHeight]
  })
  
  for (let i = 0; i < processedImages.length; i++) {
    const { canvas } = processedImages[i]
    
    // 将canvas尺寸转换为PDF点单位 (1像素 ≈ 0.75点)
    const currentPageWidth = canvas.width * 0.75
    const currentPageHeight = canvas.height * 0.75
    
    if (i > 0) {
      // 添加新页面，设置与当前图片相同的尺寸
      pdf.addPage([currentPageWidth, currentPageHeight])
    }
    
    const imgData = canvas.toDataURL('image/jpeg', 0.95)
    
    // 图片填满整个页面，无边距
    pdf.addImage(imgData, 'JPEG', 0, 0, currentPageWidth, currentPageHeight)
  }
  
  // 生成PDF Blob
  const pdfBlob = pdf.output('blob')
  const defaultFileName = getDefaultFileName(folderName)
  

  try {
    // 优先级策略：原生桥接 > Web Share > 传统下载 > 提示用户
    if (supportsNativeBridge()) {
      console.log('检测到WebView环境，使用原生桥接下载')
      await saveWithNativeBridge(pdfBlob, defaultFileName)
    } else if (isMobileDevice() && supportsWebShare()) {
      console.log('使用Web Share API下载')
      await saveWithWebShare(pdfBlob, defaultFileName)
    } else if (isInWebView()) {
      console.log('WebView环境但无原生桥接，使用下载链接')
      await saveWithDownloadLink(pdfBlob, defaultFileName)
    } else {
      console.log('使用传统下载方式')
      await saveWithFallback(pdfBlob, defaultFileName)
    }
  } catch (error) {
    console.error('PDF下载失败:', error)
    
    // 最后的备用方案
    if (isInWebView()) {
      alert('下载功能在当前环境中受限，请联系应用开发者')
    } else {
      alert('PDF下载失败，请重试')
    }
    
    throw error
  }
}