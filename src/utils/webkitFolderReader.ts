// 安全的文件夹读取器 - 只使用 webkitGetAsEntry API
// 避免使用 getAsFileSystemHandle 以防止 Chrome 浏览器崩溃

// 扩展DataTransferItem类型
declare global {
  interface DataTransferItem {
    webkitGetAsEntry(): any
  }
}

// 支持的图片格式
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
  'image/svg+xml'
]

// 文件夹扫描结果接口
export interface FolderScanResult {
  files: File[]
  folderName?: string
}

// 检查文件是否为图片
function isImageFile(file: File): boolean {
  return SUPPORTED_IMAGE_TYPES.includes(file.type.toLowerCase())
}

// 清理文件名，移除不安全字符
function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[/\\:*?"<>|]/g, '_')
    .replace(/\s+/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_|_$/g, '')
    .substring(0, 50)
}

// 检查是否支持webkitGetAsEntry
function supportsWebkitGetAsEntry(): boolean {
  return typeof (DataTransferItem.prototype as any).webkitGetAsEntry === 'function'
}

// 递归读取文件夹内容（只使用 webkit API）
async function readDirectoryWithWebkitAPI(dirEntry: any, collectFolderName?: (name: string) => void): Promise<File[]> {
  return new Promise((resolve) => {
    const files: File[] = []
    
    // 收集文件夹名称（仅第一次调用时）
    if (collectFolderName) {
      collectFolderName(dirEntry.name)
    }
    
    function readEntries(dirReader: any) {
      dirReader.readEntries(async (entries: any[]) => {
        if (entries.length === 0) {
          resolve(files)
          return
        }
        
        for (const entry of entries) {
          if (entry.isFile) {
            try {
              const file = await new Promise<File>((resolve, reject) => {
                entry.file(resolve, reject)
              })
              if (isImageFile(file)) {
                files.push(file)
              }
            } catch (error) {
              console.warn('读取文件时出错:', error)
            }
          } else if (entry.isDirectory) {
            // 递归读取子文件夹（不再收集文件夹名）
            const subFiles = await readDirectoryWithWebkitAPI(entry)
            files.push(...subFiles)
          }
        }
        
        // 继续读取剩余条目（Chrome有限制，需要多次调用）
        readEntries(dirReader)
      })
    }
    
    const dirReader = dirEntry.createReader()
    readEntries(dirReader)
  })
}

// 安全的文件提取函数 - 只使用 webkitGetAsEntry
export async function extractFilesFromDataTransfer(
  dataTransfer: DataTransfer,
  onProgress?: (message: string) => void
): Promise<FolderScanResult> {
  const allFiles: File[] = []
  const items = Array.from(dataTransfer.items)
  
  let hasFolders = false
  let folderName: string | undefined
  
  if (onProgress) {
    onProgress('正在分析拖拽内容...')
  }
  
  for (const item of items) {
    if (item.kind !== 'file') continue
    
    // 只使用 webkitGetAsEntry API
    if (supportsWebkitGetAsEntry() && (item as any).webkitGetAsEntry) {
      const entry = (item as any).webkitGetAsEntry()
      if (entry) {
        if (entry.isDirectory) {
          hasFolders = true
          if (onProgress) {
            onProgress(`正在扫描文件夹: ${entry.name}...`)
          }
          const files = await readDirectoryWithWebkitAPI(entry, (name) => {
            if (!folderName) folderName = sanitizeFileName(name)
          })
          allFiles.push(...files)
        } else if (entry.isFile) {
          const file = item.getAsFile()
          if (file && isImageFile(file)) {
            allFiles.push(file)
          }
        }
        continue
      }
    }
    
    // 回退到基础文件处理
    const file = item.getAsFile()
    if (file && isImageFile(file)) {
      allFiles.push(file)
    }
  }
  
  if (onProgress) {
    if (hasFolders) {
      onProgress(`扫描完成，找到 ${allFiles.length} 个图片文件`)
    } else {
      onProgress('文件处理完成')
    }
  }
  
  return {
    files: allFiles,
    folderName: folderName
  }
}

// 检查浏览器是否支持文件夹拖拽（只检查 webkit API）
export function supportsFolderDrop(): boolean {
  return supportsWebkitGetAsEntry()
}