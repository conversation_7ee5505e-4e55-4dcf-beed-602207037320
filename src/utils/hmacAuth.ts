/**
 * 前端HMAC签名工具库
 * 基于Web Crypto API实现HMAC签名认证
 */

export interface HMACClientConfig {
  secretKey: string;
}

/**
 * HMAC客户端签名工具类
 */
export class HMACClient {
  private secretKey: string;

  constructor(config: HMACClientConfig) {
    this.secretKey = config.secretKey;
  }

  /**
   * 生成随机nonce
   */
  generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * 参数值标准化为字符串
   */
  private stringify(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  /**
   * 构建签名字符串
   */
  private buildSignString(params: Record<string, any>, timestamp: number, method: string, path: string): string {
    // 1. 获取所有参数
    const allParams = { ...params };
    
    // 2. 添加系统参数
    allParams._timestamp = timestamp;
    allParams._method = method.toUpperCase();
    allParams._path = path;
    
    // 3. 按键名排序
    const sortedKeys = Object.keys(allParams).sort();
    
    // 4. 构建键值对字符串
    const paramString = sortedKeys
      .map(key => `${key}=${this.stringify(allParams[key])}`)
      .join('&');
    
    return paramString;
  }

  /**
   * 生成HMAC签名 (使用Web Crypto API)
   */
  async generateSignature(
    params: Record<string, any>,
    method: string,
    path: string,
    nonce: string
  ): Promise<{ signature: string; timestamp: number }> {
    const timestamp = Math.floor(Date.now() / 1000);
    
    // 添加nonce到参数中
    const paramsWithNonce = {
      ...params,
      _nonce: nonce
    };
    
    // 1. 构建签名字符串
    const signString = this.buildSignString(paramsWithNonce, timestamp, method, path);
    
    // 2. 使用Web Crypto API生成HMAC-SHA256签名
    const encoder = new TextEncoder();
    const keyData = encoder.encode(this.secretKey);
    const messageData = encoder.encode(signString);
    
    // 导入密钥
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );
    
    // 生成签名
    const signatureBuffer = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
    const signatureArray = new Uint8Array(signatureBuffer);
    const signature = Array.from(signatureArray, byte => byte.toString(16).padStart(2, '0')).join('');
    
    return { signature, timestamp };
  }

  /**
   * 为请求添加HMAC认证头
   */
  async addAuthHeaders(
    params: Record<string, any>,
    method: string,
    path: string
  ): Promise<Record<string, string>> {
    console.log('🔐 HMAC addAuthHeaders called with:', { method, path, params });
    
    const nonce = this.generateNonce();
    const timestamp = Math.floor(Date.now() / 1000);
    console.log('🎲 Generated nonce:', nonce);
    console.log('⏰ Generated timestamp:', timestamp);
    
    // 添加nonce到参数中，用于签名生成
    const paramsWithNonce = {
      ...params,
      _nonce: nonce
    };
    
    // 构建签名字符串
    const signString = this.buildSignString(paramsWithNonce, timestamp, method, path);
    console.log('📝 Sign string:', signString.substring(0, 100) + '...');
    
    // 使用Web Crypto API生成HMAC-SHA256签名
    const encoder = new TextEncoder();
    const keyData = encoder.encode(this.secretKey);
    const messageData = encoder.encode(signString);
    
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );
    
    const signatureBuffer = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
    const signatureArray = new Uint8Array(signatureBuffer);
    const signature = Array.from(signatureArray, byte => byte.toString(16).padStart(2, '0')).join('');
    
    console.log('📝 Generated signature and timestamp:', { signature: signature.substring(0, 16) + '...', timestamp });
    
    const headers = {
      'x-auth-token': signature,
      'x-timestamp': timestamp.toString(),
      'x-trace-id': nonce
    };
    
    console.log('🎯 Returning HMAC headers:', Object.keys(headers));
    return headers;
  }
}

/**
 * 创建HMAC客户端
 */
export const createHMACClient = (secretKey: string) => new HMACClient({ secretKey });

/**
 * 认证请求选项接口
 */
export interface AuthenticatedRequestOptions extends RequestInit {
  hmacSecretKey: string;
  sessionId?: string;
  csrfToken?: string;
  params?: Record<string, any>;
}

/**
 * 封装的认证请求函数
 */
export async function authenticatedFetch(
  url: string, 
  options: AuthenticatedRequestOptions
): Promise<Response> {
  const { hmacSecretKey, sessionId, csrfToken, params = {}, ...fetchOptions } = options;
  
  // 解析URL路径（移除 /api 前缀，因为后端验证时使用相对路径）
  const urlObj = new URL(url, window.location.origin);
  let path = urlObj.pathname;
  if (path.startsWith('/api')) {
    path = path.substring(4); // 移除 '/api' 前缀
  }
  
  const method = (fetchOptions.method || 'GET').toUpperCase();
  
  // 创建HMAC客户端并生成认证头
  const hmacClient = createHMACClient(hmacSecretKey);
  const authHeaders = await hmacClient.addAuthHeaders(params, method, path);
  
  // 合并请求头
  const headers: Record<string, string> = {
    ...authHeaders,
    ...(fetchOptions.headers as Record<string, string> || {})
  };
  
  // 只在提供了session和csrf token时添加
  if (sessionId) {
    headers['x-client-id'] = sessionId;
  }
  if (csrfToken) {
    headers['x-request-id'] = csrfToken;
  }
  
  return fetch(url, {
    ...fetchOptions,
    method,
    headers
  });
}

/**
 * 获取CSRF token和session ID
 */
export interface SecurityTokens {
  sessionId: string;
  handshake: string; // obfuscated session handshake token (v2)
}

export async function getSecurityTokens(): Promise<SecurityTokens> {
  console.log('🔄 Fetching security tokens from /api/init/session...');
  
  const response = await fetch('/api/init/session');
  console.log('📡 Security token response status:', response.status);
  
  if (!response.ok) {
    console.error('❌ Failed to get security tokens, status:', response.status);
    throw new Error(`Failed to get security tokens: ${response.status}`);
  }
  
  const tokens = await response.json();
  console.log('🎫 Received security tokens:', { 
    hasSessionId: !!tokens.sessionId, 
    hasHandshake: !!tokens.handshake,
    sessionIdPrefix: tokens.sessionId?.substring(0, 8) + '...',
    handshakePrefix: String(tokens.handshake || '').substring(0, 8) + '...'
  });

  return {
    sessionId: tokens.sessionId,
    handshake: tokens.handshake
  } as SecurityTokens;
}

/**
 * Decodes a base64url string.
 */
function decodeBase64Url(base64url: string): string {
  base64url = base64url.replace(/-/g, '+').replace(/_/g, '/');
  return atob(base64url);
}

/**
 * From a base64url string to a Uint8Array
 */
function base64UrlToUint8Array(base64url: string): Uint8Array {
  const binaryString = decodeBase64Url(base64url);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

/**
 * From a Uint8Array to a base64url string
 */
function uint8ArrayToBase64Url(bytes: Uint8Array): string {
  const binaryString = String.fromCharCode.apply(null, Array.from(bytes));
  return btoa(binaryString).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

/**
 * Client-side XOR obfuscation function that mirrors the backend implementation.
 */
function xorObfuscateClient(data: string, key: string): string {
  const dataBytes = base64UrlToUint8Array(data);
  const keyBytes = new TextEncoder().encode(key);
  const resultBytes = new Uint8Array(dataBytes.length);

  for (let i = 0; i < dataBytes.length; i++) {
    resultBytes[i] = dataBytes[i] ^ keyBytes[i % keyBytes.length];
  }

  return uint8ArrayToBase64Url(resultBytes);
}

/**
 * 从增强的CSRF token中提取动态HMAC密钥
 */
export function extractHMACKeyFromToken(enhancedToken: string): string | null {
  try {
    console.log('🔓 Extracting HMAC key from enhanced token...');
    let keyData: any = null;
    if (enhancedToken.includes('.')) {
      // v1 format
      const parts = enhancedToken.split('.');
      if (parts.length !== 2) {
        console.warn('⚠️ Token format invalid');
        return null;
      }
      const [csrfToken, obfuscatedKey] = parts;
      console.log('📦 Token parts (v1):', {
        csrfTokenLength: csrfToken.length,
        obfuscatedKeyLength: obfuscatedKey.length,
      });
      const keyDataEncoded = xorObfuscateClient(obfuscatedKey, csrfToken);
      const keyDataJson = decodeBase64Url(keyDataEncoded);
      keyData = JSON.parse(keyDataJson);
    } else {
      // v2 format: base64url([0x02][csrfRaw(32)][xor(payload, csrf)])
      const packed = base64UrlToUint8Array(enhancedToken);
      if (packed.length < 33 || packed[0] !== 0x02) {
        console.warn('⚠️ Unsupported token format');
        return null;
      }
      const csrfBytes = packed.slice(1, 33);
      const xored = packed.slice(33);
      const unxored = new Uint8Array(xored.length);
      for (let i = 0; i < xored.length; i++) {
        unxored[i] = xored[i] ^ csrfBytes[i % csrfBytes.length];
      }
      // unxored bytes are ASCII for the base64url string produced on the server
      const keyDataEncoded = (typeof TextDecoder !== 'undefined'
        ? new TextDecoder('utf-8').decode(unxored)
        : String.fromCharCode.apply(null, Array.from(unxored as unknown as number[]))
      );
      const keyDataJson = decodeBase64Url(keyDataEncoded);
      keyData = JSON.parse(keyDataJson);
      console.log('📦 Token parts (v2):', {
        hasCsrfBytes: true,
        payloadLength: xored.length
      });
    }

    console.log('🔑 Extracted key data:', {
      hasKey: !!keyData.k,
      version: keyData.v,
      keyLength: keyData.k?.length,
      expiresAt: new Date(keyData.e).toISOString(),
    });

    // Check if the key has expired
    if (Date.now() > keyData.e) {
      console.warn('⏰ Extracted key has expired; dynamic key required (no static fallback)');
      return null;
    }

    return keyData.k;
  } catch (error) {
    console.error('❌ Failed to extract HMAC key from token:', error);
    return null;
  }
}
