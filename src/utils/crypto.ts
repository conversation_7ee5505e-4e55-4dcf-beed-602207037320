/**
 * 计算文件的 SHA-256 哈希值
 * @param file 要计算哈希值的文件
 * @returns Promise<string> 文件的 SHA-256 哈希值
 */
export async function calculateFileHash(file: File): Promise<string> {
  const buffer = await file.arrayBuffer();
  const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return hashHex;
}
