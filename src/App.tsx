import { useState, useRef } from 'react'
import FileUpload, { type FileUploadHandles } from './components/FileUpload'
import ImageList from './components/ImageList'
import ImageCropper from './components/ImageCropper'
import CornerCropper from './components/CornerCropper'
import ProgressIndicator from './components/ProgressIndicator'
import ErrorDisplay from './components/ErrorDisplay'
import QueryProvider from './contexts/QueryProvider'
import { naturalSort } from './utils/sorting'
import { useImageProcessingState } from './hooks/useImageProcessing'
import { usePDFGenerationState } from './hooks/usePDFGeneration'
import { ImageProcessingOptions } from './types/api'
import { ValidationResult, ValidationError } from './utils/fileValidation'
import { shouldHideAppDownloadButtons, shouldShowWeChatGuidance } from './utils/deviceDetection'
import WeChatDownloadGuidance from './components/WeChatDownloadGuidance'
import { useAppDownloads } from './hooks/useAppDownloads'

interface AppState {
  files: File[]
  currentStep: 'upload' | 'list' | 'crop' | 'processing' | 'complete' | 'manual-adjust'
  cropData: any
  folderName?: string
  adjustingImageId?: string
  additionalProcessingCount?: number
  isAdditionalProcessing?: boolean
}

function AppContent() {
  const { androidUrl, iosUrl } = useAppDownloads()
  const [state, setState] = useState<AppState>({
    files: [],
    currentStep: 'upload',
    cropData: null,
    folderName: undefined,
    adjustingImageId: undefined,
    additionalProcessingCount: 0,
    isAdditionalProcessing: false
  })

  const [clientValidationError, setClientValidationError] = useState<ValidationError | null>(null)
  const [showWeChatGuidance, setShowWeChatGuidance] = useState(false)
  const [guidanceUrl, setGuidanceUrl] = useState('')
  const fileInputRef = useRef<FileUploadHandles>(null)

  const isValidUrl = (url: string | undefined): url is string => {
    return typeof url === 'string' && (url.startsWith('http://') || url.startsWith('https://'));
  };
  
  // Use custom hooks for state management
  const {
    processedImages,
    processingMode: _processingMode,
    uploadProgress,
    isProcessing,
    processImages,
    clearProcessedImages,
    updateImage,
    updateImageOrder,
    removeImage,
    uploadError,
  } = useImageProcessingState()

  const {
    isGenerating,
    generatedPDF,
    generateError,
    downloadError,
    generatePDFFromImages,
    downloadPDF,
    clearGeneratedPDF,
  } = usePDFGenerationState()

  const handleCallFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.onCallFileInput()
    }
  }

  const handleValidationError = (validation: ValidationResult) => {
    // 显示第一个错误
    if (validation.errors.length > 0) {
      setClientValidationError(validation.errors[0])
    }
  }

  const handleFilesSelected = (files: File[], folderName?: string) => {
    // 清除之前的客户端验证错误
    setClientValidationError(null)

    const sortedFiles = [...files, ...state.files].sort((a, b) =>
      naturalSort(a.name, b.name)
    )
    setState((prev) => ({
      ...prev,
      files: sortedFiles,
      currentStep: 'processing',
      folderName: folderName || prev.folderName
    }))

    // Always use auto mode with AI processing
    handleAutoProcess(sortedFiles)
  }

  const handleAdditionalFilesSelected = (files: File[], folderName?: string) => {
    // 追加上传模式：将新文件添加到现有文件列表
    const sortedFiles = [...state.files, ...files].sort((a, b) =>
      naturalSort(a.name, b.name)
    )
    setState((prev) => ({
      ...prev,
      files: sortedFiles,
      currentStep: 'processing',
      folderName: folderName || prev.folderName,
      additionalProcessingCount: files.length,
      isAdditionalProcessing: true
    }))

    // 只处理新上传的文件
    handleAutoProcessAdditional(files)
  }

  const handleFilesReordered = (reorderedFiles: File[]) => {
    setState((prev) => ({
      ...prev,
      files: reorderedFiles
    }))
  }


  const handleAutoProcess = async (files: File[]) => {
    setState((prev) => ({ ...prev, currentStep: 'processing' }))

    try {
      const options: ImageProcessingOptions = {
        mode: 'auto',
        enhancement: undefined,
        quality: 90,
        crop: true,
        dewarp: true,
        direction_correct: true,
        blur_reduce: true,
      }

      await processImages(files, options)
      setState((prev) => ({ ...prev, currentStep: 'complete' }))
    } catch (error) {
      console.error('AI处理失败:', error)
      setState((prev) => ({ ...prev, currentStep: 'upload' }))
    }
  }

  const handleAutoProcessAdditional = async (files: File[]) => {
    // 追加处理模式：保持在complete状态，但显示处理进度
    setState((prev) => ({ ...prev, currentStep: 'processing' }))

    try {
      const options: ImageProcessingOptions = {
        mode: 'auto',
        enhancement: undefined,
        quality: 90,
        crop: true,
        dewarp: true,
        direction_correct: true,
        blur_reduce: true,
      }

      // 只处理新上传的文件，结果会自动合并到现有的processedImages中
      await processImages(files, options)
      setState((prev) => ({ 
        ...prev, 
        currentStep: 'complete',
        additionalProcessingCount: 0,
        isAdditionalProcessing: false
      }))
    } catch (error) {
      console.error('追加AI处理失败:', error)
      // 处理失败时回到complete状态，保留已有结果
      setState((prev) => ({ 
        ...prev, 
        currentStep: 'complete',
        additionalProcessingCount: 0,
        isAdditionalProcessing: false
      }))
    }
  }

  const handleContinueToCrop = () => {
    setState((prev) => ({
      ...prev,
      currentStep: 'crop'
    }))
  }

  const handleCropComplete = async (cropData: any) => {
    setState((prev) => ({
      ...prev,
      cropData,
      currentStep: 'processing'
    }))

    try {
      const options: ImageProcessingOptions = {
        mode: 'manual',
        enhancement: undefined,
        quality: 90,
      }

      await processImages(state.files, options)
      setState((prev) => ({ ...prev, currentStep: 'complete' }))
    } catch (error) {
      console.error('处理失败:', error)
      setState((prev) => ({ ...prev, currentStep: 'upload' }))
    }
  }

  const handleDownload = async () => {
    try {
      if (processedImages.length === 0) {
        alert('没有处理好的图片可以生成PDF')
        return
      }

      // Use generatePDFFromImages to pass the actual image data including manual adjustments
      const result = await generatePDFFromImages(processedImages)
      
      if (result && result.filename) {
        // Generate a more meaningful filename based on folder name or date
        const suggestedName = state.folderName 
          ? `${state.folderName}.pdf`
          : `会议照片_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.pdf`
        
        await downloadPDF(result.filename, suggestedName)
      }
    } catch (error) {
      console.error('PDF生成失败:', error)
      alert('PDF生成失败，请重试')
    }
  }

  const handleManualAdjust = (imageId: string) => {
    setState(prev => ({
      ...prev,
      currentStep: 'manual-adjust',
      adjustingImageId: imageId
    }))
  }

  const handleManualAdjustComplete = async (imageId: string, croppedImageData: string) => {
    try {
      // 找到对应的图片
      const targetImage = processedImages.find(img => img.id === imageId)
      if (!targetImage) {
        throw new Error('找不到对应的图片')
      }

      // 直接更新图片的裁剪数据（本地几何矫正结果）
      const updatedImage = {
        ...targetImage,
        cropped: croppedImageData,
        timestamp: Date.now()
      }
      
      // 更新processedImages状态
      updateImage(updatedImage)
      
      setState(prev => ({ 
        ...prev, 
        currentStep: 'complete',
        adjustingImageId: undefined 
      }))
    } catch (error) {
      console.error('手动调整失败:', error)
      alert('手动调整失败，请重试')
      setState(prev => ({ 
        ...prev, 
        currentStep: 'complete',
        adjustingImageId: undefined 
      }))
    }
  }

  const handleManualAdjustCancel = () => {
    setState(prev => ({
      ...prev,
      currentStep: 'complete',
      adjustingImageId: undefined
    }))
  }

  const handleReset = () => {
    setState({
      files: [],
      currentStep: 'upload',
      cropData: null,
      folderName: undefined,
      adjustingImageId: undefined,
      additionalProcessingCount: 0,
      isAdditionalProcessing: false
    })
    clearProcessedImages()
    clearGeneratedPDF()
  }

  const handleDownloadClick = (downloadUrl: string) => (e: React.MouseEvent) => {
    e.preventDefault()
    
    if (shouldShowWeChatGuidance()) {
      setGuidanceUrl(downloadUrl);
      setShowWeChatGuidance(true)
    } else {
      window.open(downloadUrl, '_blank')
    }
  }

  return (
    <div className='container-fluid px-sm-4'>
      <div className='row justify-content-center'>
        <div className='col-12 col-lg-10 col-xl-8'>
          <header className='text-center py-4'>
            {!shouldHideAppDownloadButtons() && (
              <div className="row justify-content-center justify-content-md-end mb-3 g-2">
                  <div className="col-auto">
                    {isValidUrl(androidUrl) ? (
                      <button
                        className="btn btn-outline-dark"
                        onClick={handleDownloadClick(androidUrl)}
                      >
                        <i className="fab fa-android me-2"></i>
                        Android应用下载
                      </button>
                    ) : (
                      <div className="text-center">
                        <button className="btn btn-outline-dark opacity-50" disabled title="即将推出">
                          <i className="fab fa-android me-2"></i>
                          Android应用下载
                        </button>
                        <div className="small text-muted mt-1">即将推出</div>
                      </div>
                    )}
                  </div>
                  <div className="col-auto">
                    {isValidUrl(iosUrl) ? (
                      <button
                        className="btn btn-outline-dark"
                        onClick={handleDownloadClick(iosUrl)}
                      >
                        <i className="fab fa-apple me-2"></i>
                        iOS应用下载
                      </button>
                    ) : (
                      <div className="text-center">
                        <button className="btn btn-outline-dark opacity-50" disabled title="即将推出">
                          <i className="fab fa-apple me-2"></i>
                          iOS应用下载
                        </button>
                        <div className="small text-muted mt-1">即将推出</div>
                      </div>
                    )}
                  </div>
              </div>
            )}
            <h1 className='display-4 mb-3 fw-bold'>会议PPT拍照神器</h1>
            <p className='lead text-muted'>
            自动整理会议照片，一键裁剪合并成PDF
            </p>
          </header>

          <main className="px-md-4">
            {/* Error Display */}
            <ErrorDisplay 
              error={clientValidationError || uploadError || generateError || downloadError} 
              onDismiss={() => {
                setClientValidationError(null)
                // Server errors will clear automatically when new actions are taken
              }} 
            />

            {state.currentStep === 'upload' && (
              <div className="text-center my-4">
                <div className="row align-items-stretch">
                  <div className="col-12 col-md-5 d-flex flex-column">
                    <h6 className="mb-2">原始照片</h6>
                    <div className="flex-grow-1 d-flex position-relative">
                      <img src="/examples/before.jpg" alt="原始照片" className="img-fluid rounded shadow-sm align-self-end" />
                      <div className="position-absolute p-2 bg-dark bg-opacity-50" style={{ left: 0, right: 0, bottom: 0 }}>
                        <span className="badge bg-secondary me-1">角度倾斜</span>
                        <span className="badge bg-secondary">多余背景</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-12 col-md-2 d-flex align-items-center justify-content-center py-3 py-md-0">
                    <i className="fas fa-arrow-down fa-2x text-primary opacity-75 d-md-none"></i>
                    <i className="fas fa-arrow-right fa-3x text-primary opacity-75 d-none d-md-inline"></i>
                  </div>
                  <div className="col-12 col-md-5 d-flex flex-column">
                    <h6 className="mb-2">自动生成PDF</h6>
                    <div className="flex-grow-1 d-flex position-relative">
                      <img src="/examples/after.jpg" alt="自动处理后的照片" className="img-fluid rounded shadow-sm align-self-end" />
                      <div className="position-absolute p-2 bg-dark bg-opacity-50" style={{ left: 0, right: 0, bottom: 0 }}>
                        <span className="badge bg-primary me-1">AI透视矫正</span>
                        <span className="badge bg-primary me-1">智能裁剪</span>
                        <span className="badge bg-primary">批量合并</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="text-center mt-4">
                  <p className="lead text-muted fs-6 fs-md-5">
                    处理流程：上传照片 → AI智能处理 → 下载PDF
                  </p>
                </div>
              </div>
            )}

            <FileUpload
              onFilesSelected={handleFilesSelected}
              onValidationError={handleValidationError}
              currentStep={state.currentStep}
              ref={fileInputRef}
            />

            {state.currentStep === 'list' && state.files.length > 0 && (
              <ImageList
                files={state.files}
                onFilesReordered={handleFilesReordered}
                onContinue={handleContinueToCrop}
                onCallFileInput={handleCallFileInput}
              />
            )}

            {state.currentStep === 'crop' && state.files.length > 0 && (
              <ImageCropper
                file={state.files[0]}
                onCropComplete={handleCropComplete}
                totalFiles={state.files.length}
              />
            )}

            {(state.currentStep === 'processing' ||
              state.currentStep === 'complete') && (
              <ProgressIndicator
                isProcessing={isProcessing || isGenerating}
                progress={uploadProgress?.percentage || 0}
                totalFiles={state.files.length}
                processedImages={processedImages}
                onReset={handleReset}
                onDownload={handleDownload}
                onManualAdjust={handleManualAdjust}
                onImagesReordered={updateImageOrder}
                onAdditionalFilesSelected={handleAdditionalFilesSelected}
                onRemoveImage={removeImage}
                additionalProcessingCount={state.additionalProcessingCount}
                isAdditionalProcessing={state.isAdditionalProcessing}
                generatedPDF={generatedPDF}
                isGenerating={isGenerating}
              />
            )}

            {state.currentStep === 'manual-adjust' && state.adjustingImageId && (
              <CornerCropper
                image={processedImages.find(img => img.id === state.adjustingImageId)!}
                onCropComplete={handleManualAdjustComplete}
                onCancel={handleManualAdjustCancel}
              />
            )}
          </main>
        </div>
      </div>

      {/* 微信下载引导弹窗 */}
      <WeChatDownloadGuidance
        show={showWeChatGuidance}
        onClose={() => setShowWeChatGuidance(false)}
        downloadUrl={guidanceUrl}
      />
    </div>
  )
}

// Main App component with QueryProvider
function App() {
  return (
    <QueryProvider>
      <AppContent />
    </QueryProvider>
  )
}

export default App
