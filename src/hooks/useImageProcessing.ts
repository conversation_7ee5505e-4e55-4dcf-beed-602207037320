import React from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ImageAPI } from '../api/imageAPI';
import { 
  ProcessedImage, 
  ImageProcessingOptions, 
  UploadProgress 
} from '../types/api';

// Query keys for React Query
export const imageQueryKeys = {
  all: ['images'] as const,
  stats: () => [...imageQueryKeys.all, 'stats'] as const,
  image: (id: string) => [...imageQueryKeys.all, 'image', id] as const,
};

/**
 * Hook for uploading and processing images
 */
export const useImageUpload = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      files,
      options,
      onProgress,
    }: {
      files: File[];
      options: ImageProcessingOptions;
      onProgress?: (progress: UploadProgress) => void;
    }) => {
      return ImageAPI.uploadAndProcess(files, options, onProgress);
    },
    onSuccess: () => {
      // Invalidate and refetch stats after successful upload
      queryClient.invalidateQueries({ queryKey: imageQueryKeys.stats() });
    },
    onError: (error) => {
      console.error('Image upload failed:', error);
    },
  });
};

/**
 * Hook for getting processing statistics
 */
export const useProcessingStats = () => {
  return useQuery({
    queryKey: imageQueryKeys.stats(),
    queryFn: ImageAPI.getStats,
    refetchInterval: 10000, // Refetch every 10 seconds
    staleTime: 5000, // Data is fresh for 5 seconds
  });
};

/**
 * Hook for getting a specific processed image
 */
export const useProcessedImage = (imageId: string) => {
  return useQuery({
    queryKey: imageQueryKeys.image(imageId),
    queryFn: () => ImageAPI.getImage(imageId),
    enabled: !!imageId,
  });
};

/**
 * Hook for clearing processed images
 */
export const useClearImages = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (imageIds?: string[]) => ImageAPI.clearImages(imageIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: imageQueryKeys.all });
    },
  });
};

/**
 * Custom hook for managing image processing state
 */
export const useImageProcessingState = () => {
  const [processedImages, setProcessedImages] = React.useState<ProcessedImage[]>([]);
  const [processingMode, setProcessingMode] = React.useState<'auto' | 'manual' | 'hybrid'>('auto');
  const [uploadProgress, setUploadProgress] = React.useState<UploadProgress | null>(null);
  const [isProcessing, setIsProcessing] = React.useState(false);

  const uploadMutation = useImageUpload();

  const processImages = async (files: File[], options: ImageProcessingOptions) => {
    setIsProcessing(true);
    setUploadProgress(null);

    try {
      const result = await uploadMutation.mutateAsync({
        files,
        options,
        onProgress: setUploadProgress,
      });

      setProcessedImages(prev => [...prev, ...result.images]);
      return result;
    } finally {
      setIsProcessing(false);
      setUploadProgress(null);
    }
  };

  const clearProcessedImages = () => {
    setProcessedImages([]);
  };

  const removeImage = (imageId: string) => {
    setProcessedImages(prev => prev.filter(img => img.id !== imageId));
  };

  const updateImageOrder = (newOrder: ProcessedImage[]) => {
    setProcessedImages(newOrder);
  };

  const updateImage = (updatedImage: ProcessedImage) => {
    setProcessedImages(prev => 
      prev.map(img => img.id === updatedImage.id ? updatedImage : img)
    );
  };

  return {
    processedImages,
    processingMode,
    uploadProgress,
    isProcessing,
    setProcessingMode,
    processImages,
    clearProcessedImages,
    removeImage,
    updateImageOrder,
    updateImage,
    uploadError: uploadMutation.error,
    uploadSuccess: uploadMutation.isSuccess,
  };
};