import { useState, useEffect } from 'react'
import axios from 'axios'

export interface AppDownloadConfig {
  android?: {
    url?: string
  }
  ios?: {
    url?: string
  }
}

export function useAppDownloads() {
  const [downloadConfig, setDownloadConfig] = useState<AppDownloadConfig>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchDownloadConfig = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await axios.get<{
          success: boolean
          data: AppDownloadConfig
          error?: string
        }>('/api/config/app-downloads')
        
        if (response.data.success) {
          setDownloadConfig(response.data.data || {})
        } else {
          setError(response.data.error || 'Failed to load download config')
        }
      } catch (err) {
        console.error('Failed to fetch app download config:', err)
        setError('Failed to fetch download configuration')
        // Use empty config on error
        setDownloadConfig({})
      } finally {
        setLoading(false)
      }
    }

    fetchDownloadConfig()
    
    // Refresh config every 5 minutes
    const interval = setInterval(fetchDownloadConfig, 5 * 60 * 1000)
    
    return () => clearInterval(interval)
  }, [])

  return {
    androidUrl: downloadConfig.android?.url,
    iosUrl: downloadConfig.ios?.url,
    loading,
    error
  }
}