import React from 'react';
import { useMutation } from '@tanstack/react-query';
import { PDFAPI } from '../api/pdfAPI';
import { PDFGenerationOptions, ProcessedImage } from '../types/api';

/**
 * Hook for PDF generation (using image IDs)
 */
export const usePDFGeneration = () => {
  return useMutation({
    mutationFn: async ({
      imageIds,
      options,
    }: {
      imageIds: string[];
      options: PDFGenerationOptions;
    }) => {
      return PDFAPI.generatePDF(imageIds, options);
    },
    onError: (error) => {
      console.error('PDF generation failed:', error);
    },
  });
};

/**
 * Hook for PDF generation from image data
 */
export const usePDFGenerationFromImages = () => {
  return useMutation({
    mutationFn: async ({
      images,
      options,
    }: {
      images: ProcessedImage[];
      options: PDFGenerationOptions;
    }) => {
      return PDFAPI.generatePDFFromImages(images, options);
    },
    onError: (error) => {
      console.error('PDF generation from images failed:', error);
    },
  });
};

/**
 * Hook for PDF download
 */
export const usePDFDownload = () => {
  return useMutation({
    mutationFn: ({ fileName, suggestedName }: { fileName: string; suggestedName?: string }) => 
      PDFAPI.downloadPDF(fileName, suggestedName),
    onError: (error) => {
      console.error('PDF download failed:', error);
    },
  });
};

/**
 * Custom hook for managing PDF generation state
 */
export const usePDFGenerationState = () => {
  const [pdfOptions, setPDFOptions] = React.useState<PDFGenerationOptions>(
    PDFAPI.getDefaultOptions()
  );
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [generatedPDF, setGeneratedPDF] = React.useState<{
    fileName: string;
    downloadUrl: string;
    fileSize: number;
  } | null>(null);

  const generateMutation = usePDFGeneration();
  const generateFromImagesMutation = usePDFGenerationFromImages();
  const downloadMutation = usePDFDownload();

  const generatePDF = async (imageIds: string[]) => {
    setIsGenerating(true);

    try {
      const result = await generateMutation.mutateAsync({
        imageIds,
        options: pdfOptions,
      });

      setGeneratedPDF({
        fileName: result.filename,
        downloadUrl: result.downloadUrl,
        fileSize: result.fileSize,
      });

      return result;
    } finally {
      setIsGenerating(false);
    }
  };

  const generatePDFFromImages = async (images: ProcessedImage[]) => {
    setIsGenerating(true);

    try {
      const result = await generateFromImagesMutation.mutateAsync({
        images,
        options: pdfOptions,
      });

      setGeneratedPDF({
        fileName: result.filename,
        downloadUrl: result.downloadUrl,
        fileSize: result.fileSize,
      });

      return result;
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadPDF = async (fileName: string, suggestedName?: string) => {
    await downloadMutation.mutateAsync({ fileName, suggestedName });
  };

  const clearGeneratedPDF = () => {
    setGeneratedPDF(null);
  };

  const updatePDFOptions = (newOptions: Partial<PDFGenerationOptions>) => {
    setPDFOptions(prev => ({ ...prev, ...newOptions }));
  };

  return {
    pdfOptions,
    isGenerating,
    generatedPDF,
    generateError: generateMutation.error || generateFromImagesMutation.error,
    downloadError: downloadMutation.error,
    generatePDF,
    generatePDFFromImages,
    downloadPDF,
    clearGeneratedPDF,
    updatePDFOptions,
    setPDFOptions,
  };
};