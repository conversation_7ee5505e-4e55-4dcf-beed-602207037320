import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'

// Import CSS dependencies
import 'bootstrap/dist/css/bootstrap.min.css'
import '@fortawesome/fontawesome-free/css/all.min.css'
import 'cropperjs/dist/cropper.min.css'

// Silence debug logs in production deployments.
// Keeps console.error/warn intact for real issues.
if (import.meta.env.PROD && import.meta.env.VITE_KEEP_CONSOLE !== 'true') {
  const noop = () => {}
  console.log = noop
  console.debug = noop
  console.info = noop
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
