export interface ProcessedImage {
  id: string;
  originalName: string;
  original: string;
  cropped?: string;
  corners?: [number, number][];
  confidence?: number;
  enhancement?: string;
  timestamp: number;
  isLowResolution?: boolean;
}

export interface ImageProcessingOptions {
  mode: 'auto' | 'manual' | 'hybrid';
  enhancement?: 'brighten' | 'sharpen' | 'bw' | 'grayscale' | 'remove_shadow';
  quality?: number;
  crop?: boolean;
  dewarp?: boolean;
  direction_correct?: boolean;
  blur_reduce?: boolean;
}

export interface PDFGenerationOptions {
  pageSize: 'A4' | 'Letter';
  orientation: 'portrait' | 'landscape';
  margin: number;
  quality: number;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface UploadResponse {
  images: ProcessedImage[];
}

export interface PDFResponse {
  downloadUrl: string;
  fileSize: number;
  filename: string;
}

export interface ProcessingStats {
  totalImages: number;
  memoryUsage: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}