# 重构计划：优化图片处理与PDF生成流程

## 1. 目标

当前应用在前后端之间通过Base64传输所有图片数据，并在生成PDF时将全部图片数据重新发回后端，导致网络带宽和服务器内存的巨大浪费。本次重构旨在优化这一流程，通过引入后端缓存和URL访问机制，显著提升应用性能和可扩展性。

## 2. 核心思路

-   **统一缓存策略**: 为保证后端逻辑统一和性能，所有图片上传入口（包括为B/S端设计的 `/api/images/process` 和为C/S端保留的 `/api/images/upload`）都将完全接入 `ImageCacheService`，共享同一套缓存机制。
-   **B/S端 (本项目)**:
    1.  **后端负责缓存**：后端服务在从TextIn API获取处理结果或接收用户修改的图片后，将图片文件（而非Base64）存储在服务器的临时目录中。
    2.  **前端使用URL**：后端不再向前端发送图片Base64，而是提供指向缓存文件的直接URL，格式为 `/api/images/get/{type}/{hash}`。前端通过这些URL来显示图片。
    3.  **按需传输**：生成PDF时，前端只发送图片ID列表。仅当用户在前端对图片进行了修改（如手动裁剪），才将修改后的图片Base64与ID一同发送。
-   **C/S端 (`/api/images/upload`)**: 此接口同样利用缓存，但 API 协议不变，返回 Base64，仅精简去掉 original、originalName、confidence、enhancement 四个字段。

---

## 3. Backend 重构

### 3.1. 引入图片缓存与管理 (`ImageCacheService`)

创建一个新的服务 `ImageCacheService` 来管理图片的物理缓存和缓存策略。

-   **文件存储**：
    -   在 `backend/temp/` 目录下创建缓存目录：`backend/temp/image-cache/`。
    -   该目录包含 `original/` (存放原始图片)、`processed/` (存放处理后的图片) 和 `metadata/` (存放缓存元数据) 三个子目录。
    -   所有图片文件都以其**自身内容**的SHA256 Hash作为文件名。原始图片存放在 `original/` 目录，处理后图片存放在 `processed/` 目录，以实现清晰的分类和最大限度的去重。
-   **缓存逻辑 (使用元数据文件)**：
    -   **TextIn结果缓存**:
        1.  对用户上传的原始图片计算其SHA256 Hash，我们称之为 `originalHash`。
        2.  检查 `metadata/${originalHash}.json` 文件是否存在。
        3.  **缓存命中**：如果元数据文件存在，直接读取它。文件内容将包含处理后图片的Hash (`processedHash`) 和 `corners` 数据。根据这两个Hash可以构建完整的URL。
        4.  **缓存未命中**:
            -   调用TextIn API。
            -   如果成功，将返回的处理后图片内容计算其SHA256 Hash，我们称之为 `processedHash`。
            -   将原始图片以 `originalHash` 为名存入 `original/` 目录。
            -   将处理后图片以 `processedHash` 为名存入 `processed/` 目录。
            -   创建一个新的元数据文件 `metadata/${originalHash}.json`，内容如下：
                ```json
                {
                  "originalHash": "...",
                  "processedHash": "...",
                  "corners": [[x1, y1], [x2, y2], ...],
                  "createdAt": "2025-08-28T12:00:00.000Z" 
                }
                ```
-   **缓存清理 (与PDF缓存清理集成)**：
    -   **触发时机**: 图片缓存的清理逻辑将不再由独立的定时器 (`setInterval`) 触发，而是与现有的 `PDFCacheService` 的清理机制进行集成。`PDFCacheService` 在每次生成文件后，会检查是否需要执行清理（`checkAndCleanup`），这是一个理想的触发点。
    -   **实现方式**:
        1.  在 `ImageCacheService` 中实现一个 `cleanup()` 方法，该方法负责扫描并清理过期的图片缓存文件（元数据和图片文件）。
        2.  修改 `PDFCacheService` 的 `cleanup()` 方法，在执行自身清理逻辑的同时，调用 `ImageCacheService.getInstance().cleanup()`。
    -   **清理逻辑 (简化)**:
        -   扫描 `metadata/` 目录，检查每个元数据文件的 `createdAt` 时间戳。
        -   如果文件超过设定的缓存周期（例如7天），则删除该元数据文件。
        -   删除元数据文件的同时，直接删除其在 `original/` 和 `processed/` 目录中引用的对应图片文件。这基于一个简化的假设：一个处理后的图片基本不会被多个不同的原始图片共享。
    -   **优点**:
        -   **减少空转**: 清理任务只在系统活动（生成PDF）后按需触发，避免了无效的定时器轮询。
        -   **统一管理**: 将所有缓存清理逻辑集中管理，便于维护和监控。
        -   **逻辑简化**: 清理逻辑更直接，无需实现引用计数，降低了复杂性。

    -   **风险与修订建议**:
        -   **清理机制冲突**: 需注意项目中可能存在的通用临时文件清理任务（如 `FileUtils.startCleanupScheduler()`），它可能会意外清理本方案的缓存。建议将 `image-cache` 和 `pdf-cache` 放置在**独立于**通用清理路径的专用目录中。
        -   **触发时机单一**: 仅在PDF生成后触发清理，在“只上传不导出”的场景下有缓存无限增长的风险。因此要有混合触发机制，除了在PDF生成后触发，还在用户上传完图片以后检查时间间隔是否到了可以触发的时间。

### 3.2. `ImageService` 修改

-   **`processImages` / `processSingleImage`**:
    1.  计算上传文件的 `originalHash`。
    2.  查询 `ImageCacheService` 是否有该 `originalHash` 的元数据。
    3.  **命中缓存**：从 `ImageCacheService` 获取 `originalHash` 和 `processedHash`，并构建图片URL。
    4.  **未命中缓存**：调用TextIn，然后将结果（原始图片、处理后图片、corners）交给 `ImageCacheService` 创建新的元数据和图片文件。
-   **不再需要 `reprocessImageWithManualCrop`**:
    -   根据新的流程，用户在前端手动裁剪的结果**仅保存在前端状态中**。
    -   这些修改后的图片数据（Base64）只会在最后生成PDF时，作为请求的一部分一次性发送到后端。
    -   后端**不需要**提供专门的接口来接收或缓存用户手动修改的图片。这大大简化了后端的逻辑。

### 3.3. `ImageController` 和 `ImageRoutes` 修改

-   **提供图片访问**:
    -   这里的逻辑依然正确。`express.static` 是最高效的方式。
        ```typescript
        // in backend/src/server.ts
        // 将 /api/images/get/original/HASH 映射到 backend/temp/image-cache/original/HASH
        app.use('/api/images/get/original', express.static(path.join(__dirname, '..', 'temp', 'image-cache', 'original')));
        // 将 /api/images/get/processed/HASH 映射到 backend/temp/image-cache/processed/HASH
        app.use('/api/images/get/processed', express.static(path.join(__dirname, '..', 'temp', 'image-cache', 'processed')));
        ```
    -   **注意**: 上述 `path.join` 使用了 `__dirname`，在TS编译后和Docker环境下可能指向错误位置。所有缓存路径的构建都应**统一使用从Nacos获取的 `config.storage.tempDir` 作为根路径**，以确保环境一致性。
    -   `ImageCacheService` 可以提供一个辅助函数 `getUrls(metadata)` 来生成 `originalUrl` 和 `processedUrl`，保持Controller的整洁。

### 3.4. `PDFController` 和 `PDFService` 修改

-   **`pdfController.generatePDF`**:
    -   此方法将成为唯一的PDF生成入口。
    -   修改其逻辑以接受新的请求体（见API契约）。
    -   遍历请求中的 `images` 数组：
        -   如果一个项只有 `id` (即 `originalHash`)，则从 `ImageCacheService` 读取元数据，找到缓存中处理后图片的**文件路径**。
        -   如果一个项同时有 `id` 和 `data` (Base64)，则将 `data` 解码并保存为一个**临时文件**。此文件仅用于本次PDF生成，**不会**被添加到 `image-cache` 中，并在生成后被清理。
    -   **临时文件管理**: 为防止PDF生成失败时临时文件泄漏，Controller层应使用 `try...finally` 块来确保无论成功或失败，本次请求创建的临时文件都能被可靠地删除。
    -   将最终收集到的图片**文件路径**列表（包括缓存文件路径和临时文件路径）传递给 `pdfService`。
-   **`pdfService.generatePDF`**:
    -   此方法需重构为接收一个图片文件路径的数组。**注意：这并非小改动**，其内部需要增加文件I/O操作（异步读取文件到Buffer），然后再执行现有的jsPDF嵌入逻辑。

---

## 4. Frontend 重构

### 4.1. API层 (`client.ts`, `pdfAPI.ts`, `imageAPI.ts`)

-   **`imageAPI.ts`**:
    -   `uploadAndProcess` 的返回值类型 `UploadResponse` 中的 `ProcessedImage` 将变为URL格式。
-   **`pdfAPI.ts`**:
    -   `generatePDFFromImages` 方法将重构为主要的PDF生成函数。
    -   它将接收 `ProcessedImage[]` 数组，并根据每个图片对象是否包含用户修改的 `cropped` (Base64) 数据来构建不同的请求体。
    -   `generatePDF` (基于ID) 方法将被移除。

### 4.2. State & Hooks (`useImageProcessing.ts`, `usePDFGeneration.ts`)

-   **`useImageProcessing.ts`**:
    -   `ProcessedImage` 状态中，`processedUrl` 最初指向后端缓存的AI处理结果。
    -   `updateImage` 函数（在手动裁剪后调用）需要更新 `processedImages` 状态中对应图片：
        1.  将用户裁剪生成的图片Base64数据保存在 `modifiedData` 字段。
        2.  设置 `isModified: true` 标志。
        3.  **（可选）** 为了在界面上立即显示裁剪结果，可以将 `processedUrl` 临时更新为一个指向 `modifiedData` 的 `data:` URL。
-   **`usePDFGeneration.ts`**:
    -   `generatePDFFromImages` hook将调用重构后的 `pdfAPI.generatePDFFromImages`，该API负责根据 `isModified` 标志构建正确的请求体。

### 4.3. 组件层

-   **`ImageList.tsx`, `CornerCropper.tsx`, `ProgressIndicator.tsx` 等**:
    -   所有使用 `<img>` 标签的地方，`src` 属性不再是 `data:image/jpeg;base64,...`，并且为支持CORS下的Canvas操作，**必须设置 `crossOrigin="anonymous"` 属性**。
    -   `src` 将直接设置为从 `ProcessedImage` 对象中获取的 `processedUrl` 或 `originalUrl`。
-   **`App.tsx`**:
    -   `handleDownload` 方法：
        -   调用 `generatePDFFromImages` hook。
        -   传递 `processedImages` 状态。
        -   `pdfAPI` 层会处理如何根据 `isModified` 标志构建正确的请求。
    -   `handleManualAdjustComplete` 方法：
        -   当 `CornerCropper` 完成裁剪并返回新的Base64时，调用 `useImageProcessing` 的 `updateImage` 方法。
        -   这个 `updateImage` 方法将图片标记为已修改。

---

## 5. API 契约变更

### 5.1. `POST /api/images/upload` - 响应体

-   **变更前**:
    ```json
    {
      "success": true,
      "data": {
        "images": [
          {
            "id": "c8a0c568-2349-4af2-8921-82355331a551",
            "originalName": "image1.jpg",
            "original": "base64...",
            "cropped": "base64...",
            "corners": [[...], [...], [...], [...]],
            "confidence": 0.95,
            "enhancement": "brighten",
            "timestamp": 1693200000000
          }
        ]
      }
    }
    ```
-   **变更后**:
    ```json
    {
      "success": true,
      "data": {
        "images": [
          {
            "id": "c8a0c568-2349-4af2-8921-82355331a551",
            "cropped": "base64...",
            "corners": [[...], [...], [...], [...]],
            "timestamp": 1693200000000
          }
        ]
      }
    }
    ```
    

### 5.2. `POST /api/pdf/generate` - 请求体 (替代 `generate-from-data`)

-   **变更前** (`generate-from-data`):
    ```json
    {
      "images": [
        { "id": "...", "data": "base64...", "originalName": "..." }
      ],
      "options": { ... }
    }
    ```
-   **变更后**:
    ```json
    {
      "images": [
        { "id": "image_id_1" }, // 用户未修改的图片
        { "id": "image_id_2", "data": "user_modified_base64..." }, // 用户手动裁剪的图片
        { "id": "image_id_3" }
      ],
      "options": { ... }
    }
    ```

### 5.3. 类型定义 (`types/index.ts` 和 `types/api.ts`)

-   `ProcessedImage` 接口将同步修改：
    ```typescript
    // 变更前
    export interface ProcessedImage {
      id: string;
      original: string; // base64
      cropped?: string; // base64
      // ...
    }

    // 变更后
    export interface ProcessedImage {
      id: string; // 语义：图片的 originalHash
      originalUrl: string;
      processedUrl: string;
      // ...
      // 前端临时字段，用于追踪用户修改
      isModified?: boolean;
      // 前端在手动修改后，临时保存新生成的base64，用于发送给后端
      modifiedData?: string; 
    }
    ```

---

## 6. 数据流变更

-   **图片上传流程**
    -   **之前**: `FE: File` -> `BE: (TextIn) -> Base64` -> `FE: Store Base64`
    -   **之后**: `FE: File` -> `BE: (Cache Check) -> (TextIn) -> Save to Disk` -> `FE: Store URL`
-   **PDF生成流程**
    -   **之前**: `FE: Send all Base64s` -> `BE: Generate PDF`
    -   **之后**: `FE: Send IDs + modified Base64s` -> `BE: Read from Cache/New Data -> Generate PDF`

---

## 7. 实施步骤

为降低风险、确保平稳过渡，建议将本次重构按模块拆分，避免一次性进行“大爆炸”式的变更。可以考虑以下顺序：

1.  **后端基础建设**: 首先在后端实现 `ImageCacheService`，完成图片和元数据的存储、读取和清理逻辑，并添加 `express.static` 路由。此阶段可以独立开发和测试，不影响现有前端功能。

    **独立测试策略**:
    -   **单元测试**:
        -   为 `ImageCacheService` 创建专门的单元测试文件（如 `backend/src/services/imageCacheService.test.ts`）。
        -   使用测试框架（如 Jest）和 `mock-fs` 或类似的库来模拟文件系统。这可以避免在真实磁盘上创建文件，使测试更快、更可靠。
        -   **测试用例**:
            1.  **缓存未命中**: 模拟一个新图片的首次处理流程，断言元数据文件和图片文件是否在模拟的文件系统中被正确创建。
            2.  **缓存命中**: 模拟同一张图片的第二次请求，断言服务是否直接返回了已缓存的数据，而不是再次执行写入操作。
            3.  **缓存清理**: 在模拟文件系统中创建一些过期的缓存文件，调用 `cleanup()` 方法，然后断言这些过期文件是否已被成功删除。
    -   **API接口测试**:
        -   在 `ImageController` 中添加 `express.static` 路由后，通过 `curl` 或 API 测试工具（如 Postman、Insomnia）进行端到端测试。
        -   **测试步骤**:
            1.  手动在 `backend/temp/image-cache/processed/` 目录下放置一个测试图片文件，例如 `test-image.jpg`，并将其重命名为一个SHA256哈希值，如 `e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855`。
            2.  启动后端服务。
            3.  使用 `curl` 或浏览器访问 `http://localhost:PORT/api/images/get/processed/e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855`。
            4.  **预期结果**: 浏览器或工具应成功下载并显示该图片。这验证了静态文件服务路由配置正确。
2.  **API改造与兼容**: 修改 `ImageService` 和 `ImageController`，使其在内部使用新的缓存服务，但暂时保持对外的API响应格式不变（仍返回Base64）。同时，可以新增一个使用新URL格式的API端点（如 `/v2/images/process`）用于测试。
3.  **前端适配**: 前端开始基于新的 `/v2` 端点进行开发，更新状态管理、Hooks和组件，使其能够处理URL。
4.  **切换与清理**: 当前后两端都准备就绪后，进行一次集成的切换，将前端API请求指向新的实现，后端正式切换为返回URL。确认系统稳定运行后，再移除旧的、基于Base64传输的代码和API。

---

## 8. 安全性考虑

### 8.1. 输入校验

-   **问题**: `GET /api/images/get/{type}/{hash}` 接口直接使用 `hash` 作为文件名的一部分。尽管SHA256哈希本身是安全的，但不校验用户输入始终存在风险。
-   **措施**: 必须在处理该请求的路由或中间件中添加严格的输入校验：
    -   `type`: 必须是 `'original'` 或 `'processed'` 之一。
    -   `hash`: 必须匹配正则表达式 `^[a-f0-9]{64}$`，以拒绝任何格式不符的请求，防止潜在的路径遍历攻击。

### 8.2. 访问控制

-   **问题**: `express.static` 方案会绕过应用安全体系，公开所有缓存图片。任何知道有效哈希值的人都可以直接访问，存在资源枚举和未授权访问风险。
-   **修订建议**: **不应使用 `express.static` 直接提供服务**。推荐的方案是：
    -   **权限验证路由**: 将图片访问收归到一个专门的Controller方法（如 `ImageController.getCacheImage`）。该方法必须集成到应用现有的安全中间件链中，对每个请求进行HMAC或会话校验。校验通过后，再从缓存目录读取文件并响应。
    -   **备选方案 (签名URL)**: 如果需要将URL用于邮件或第三方集成，可以考虑为每个图片URL生成带有时效性和签名的临时链接。

### 8.3. Content-Type 问题
- **问题**: 以纯 Hash 作为文件名（无扩展名）通过 `express.static` 提供时，MIME 类型可能被识别为 `application/octet-stream`，导致浏览器 `<img>` 标签加载失败。
- **措施**: 建议在元数据中记录图片的 `mimeType`，并在提供文件的Controller（见8.2方案）中主动设置正确的 `Content-Type` 响应头。

### 8.4. CORS与Canvas画布污染
- **问题**: 前端裁剪组件如基于 Canvas，跨源图片会污染 Canvas，导致 `toDataURL()` 等操作失败。
- **措施**:
    -   **后端**: 在图片响应头中必须包含 `Access-Control-Allow-Origin`。
    -   **前端**: 在所有加载缓存图片的 `<img>` 标签上，必须设置 `crossOrigin="anonymous"` 属性。

---

## 9. 风险、兼容性与异常处理

### 9.1. 技术实现风险

-   **并发写入冲突**: 多个用户或进程同时上传相同的图片时，可能在写入元数据和图片文件时发生竞争。例如，进程A检查到缓存未命中，开始调用TextIn，此时进程B也上传相同图片，同样未命中。两个进程可能尝试同时写入同一个文件 (`metadata/${originalHash}.json` 或图片文件)，导致文件损坏或状态不一致。
    -   **解决方案**: 引入跨进程的文件锁。在Node.js中，可以使用成熟的库如 `proper-lockfile` 来实现。在创建或修改元数据文件前获取锁，操作完成后释放锁，可以有效防止竞争。
        ```typescript
        // 伪代码示例
        import lockfile from 'proper-lockfile';

        async function cacheImage(originalHash, data) {
          const metadataPath = `metadata/${originalHash}.json`;
          try {
            // 在写入元数据前获取锁，防止并发写入
            await lockfile.lock(metadataPath, { retries: 3 });
            // ... 检查缓存、写入文件、写入元数据等核心逻辑 ...
          } finally {
            // 确保操作完成后，无论成功或失败，都释放锁
            await lockfile.unlock(metadataPath);
          }
        }
        ```

-   **元数据与文件不一致**: 当系统在写入操作期间异常终止时（如断电、崩溃），可能出现元数据文件已创建/更新，但其引用的图片文件（`original/` 或 `processed/`）未完全写入或损坏。
    -   **解决方案**: 采用“先写内容，后写元数据”的策略。先将图片文件完全写入临时位置，然后重命名为最终的Hash文件名（重命名是原子操作），最后再创建或更新元数据文件。这样可以最大限度地保证元数据引用的文件是完整的。

-   **文件系统性能风险 (目录分桶)**:
    -   **问题**: 在单个目录下存放海量（数百万）文件会拖慢文件系统操作。
    -   **解决方案**: 建议对缓存文件采用**分桶存储**策略。例如，根据哈希值的前缀创建二级子目录（如 `ab/cd/ef123...`），避免单目录文件过多。

### 9.2. API契约兼容性问题

-   **破坏性变更 (`ProcessedImage` 接口)**: `ProcessedImage` 接口的字段从 `original`/`cropped` (Base64) 完全变更为 `originalUrl`/`processedUrl`。
    -   **前端适配**: 前端所有使用 `ProcessedImage` 对象的地方都需要修改。这包括 `ImageList`, `ImageViewer`, `CornerCropper` 等组件，它们之前直接从Base64读取数据，现在需要通过URL从网络加载图片。需要仔细检查并确保所有相关组件都已正确适配 `src` 属性。

### 9.3. 错误处理和异常情况

-   **磁盘空间不足**: 缓存目录（`backend/temp/image-cache/`）的容量可能被耗尽。
    -   **处理策略**: 在写入任何文件之前，应检查可用磁盘空间。如果空间不足，应立即停止操作，并返回一个明确的服务器错误（如 `507 Insufficient Storage`）。同时，应触发一次强制的、更积极的缓存清理（过期时间从7天缩短到3天），并记录详细的错误日志以供运维人员分析。

-   **图片文件损坏**: 缓存的图片文件可能因磁盘错误等原因损坏。当 `express.static` 尝试提供一个损坏的文件时，前端可能会收到一个不完整的或错误的响应。
    -   **处理策略**:
        -   **前端**: 前端在 `<img>` 标签上可以添加 `onError` 事件处理器。当图片加载失败时，可以显示一个占位符，并提供一个“重新加载”或“重新处理”的按钮。
        -   **后端**: 当检测到重新处理请求时，后端可以删除损坏的缓存文件（图片和元数据），然后重新执行完整的处理流程（调用TextIn API等）。

-   **TextIn API异常 (修订)**:
    -   **问题**: 原降级方案（不创建元数据）会导致状态不一致。若前端后续仅凭ID请求生成PDF，后端会因找不到元数据而失败。
    -   **修订方案 (有状态的降级)**:
        1.  当TextIn API调用失败时，后端**仍需创建元数据文件**，以保证状态的完整性。
        2.  在该元数据文件中，进行特殊标记：
            -   设置 `status` 字段为 `"fallback"`。
            -   将 `processedHash` 字段设置为与 `originalHash` 相同的值。
            -   将 `corners` 设置为 `null`。
        3.  **API响应**: 返回给前端的 `processedUrl` 指向原图URL，并包含 `status: 'fallback'` 字段。
        4.  **后续流程**:
            -   **PDF生成**: 后端收到仅含 `id` 的请求时，能查到 `fallback` 状态的元数据，并使用原图生成PDF，保证了流程闭环。
            -   **自动重试**: 下次同一图片上传时，后端检查到 `status` 为 `fallback`，可触发重试调用TextIn API，实现自动修复。

-   **网络中断处理**: 前端通过URL获取图片时，可能会因为网络问题（如用户网络不稳定）而加载失败。
    -   **重试机制**: 前端在 `<img>` 标签的 `onError` 事件处理器中，应根据错误类型决定行为。可以实现一个简单的自动重试机制（例如，延迟1-2秒后重试2-3次），但需要排除某些特定错误。如果错误是由于网络超时或服务器临时不可用（如503），则进行重试。但如果失败是由于客户端错误（如 `403 Forbidden`，可能由HMAC或CORS策略导致）或资源不存在（`404 Not Found`），则不应重试，并应直接向用户显示明确的错误提示。

-   **缓存清理失败**: 在执行缓存清理时，可能会因为文件被其他进程锁定或遇到权限问题而导致删除失败。
    -   **处理策略**: 清理逻辑应包含在 `try...catch` 块中，确保单个文件的失败不会中断整个清理过程。对于删除失败的文件，应记录错误日志，并在下一次清理周期中重试。不应让清理失败影响主业务流程。

这个方案将大大减少网络流量和前后端的内存占用，使应用更健壮、更快速。

---

## 10. 配置管理 (Nacos)

为了使缓存策略更灵活，所有与图片缓存相关的参数都应通过Nacos进行集中管理。

**Nacos Data ID**: `security.yaml`
**Group**: `image-to-pdf`

建议在 `security.yaml` 文件中添加以下配置：

```yaml
# 图像缓存配置 - 用于优化图片处理流程
imageCache:
  enabled: true                # 是否启用图片缓存功能
  directory: "temp/image-cache" # 缓存存储目录 (相对于后端根目录)
  ttlDays: 7                   # 缓存有效期(天)
```
-   **注意**: 在Nacos中添加此配置后，**必须**同步更新后端 `NacosService` 中解析该配置的 `SecurityConfig` 等类型定义，以确保新增配置能被类型安全地访问。