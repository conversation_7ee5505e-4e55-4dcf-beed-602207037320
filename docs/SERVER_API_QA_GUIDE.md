# 服务端接口技术文档 (QA 参考)

## 0. 简介

本文档旨在为QA团队提供服务端核心接口的技术说明和测试指导。它详细描述了应用的认证流程、关键业务接口、数据流转方式以及核心业务逻辑，以帮助测试人员设计测试用例、理解系统行为和定位问题。

---

## 1. 核心架构与流程图

系统核心功能主要包括两部分：**图片处理与缓存** 和 **PDF生成**。

- **图片处理**: 接收客户端上传的图片，通过外部 `TextIn` API进行智能裁剪和增强，然后将处理前后的图片进行缓存，并返回可访问的URL给客户端。
- **PDF生成**: 根据客户端指定的图片列表（包含缓存ID和用户手动修改的图片数据），动态计算合适的页面尺寸，生成统一、规整的PDF文档。

### 流程图 1: 图片处理与缓存流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 后端服务
    participant Cache as 图片缓存 (ImageCacheService)
    participant TextIn as TextIn API

    Client->>+Server: POST /api/images/process (上传图片)
    Server->>Cache: 检查图片哈希是否存在
    alt 缓存命中
        Cache-->>Server: 返回已缓存的图片URL
    else 缓存未命中
        Server->>+TextIn: 调用API处理图片
        TextIn-->>-Server: 返回处理后的图片数据和四角坐标
        Server->>Cache: 存储原始图片、处理后图片及元数据
        Cache-->>Server: 返回新生成的图片URL
    end
    Server-->>-Client: 返回包含图片URL的JSON数据
```

### 流程图 2: PDF 生成流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 后端服务
    participant Cache as 图片缓存 (ImageCacheService)
    participant PDF as PDF服务 (PDFService)

    Client->>+Server: POST /api/pdf/generate (请求体含图片ID列表和少量修改后图片数据)
    Server->>Server: 遍历图片列表
    loop 针对每张图片
        alt 图片仅有ID (未被前端修改)
            Server->>Cache: 根据ID读取处理后的图片文件路径
        else 图片有ID和data (被前端修改)
            Server->>Server: 将data(Base64)解码为临时文件
        end
    end
    Server->>+PDF: 传递所有图片的文件路径列表
    PDF->>PDF: 1. 计算所有图片尺寸的中位数，确定统一页面尺寸<br/>2. 调整所有图片至统一尺寸(拉伸或填充白边)<br/>3. 生成PDF
    PDF-->>-Server: 返回生成的PDF文件流
    Server-->>-Client: 返回PDF文件下载
```

---

## 2. 认证机制

系统支持两种客户端：**原生客户端 (C/S)** 和 **Web 客户端 (B/S)**，它们使用不同的认证策略。

### 2.1. 原生客户端 (C/S) 认证

原生客户端认证分为两步：

**第 1 步：获取动态会话密钥**

- **接口**: `POST /api/native/session`
- **请求**: 发送包含预置静态密钥的请求。
  ```json
  {"credential": "YOUR_STATIC_MOBILE_CLIENT_SECRET"}
  ```
- **响应**: 服务器返回一个经AES加密的 `data` 字段，客户端需用静态密钥解密，得到动态的会话ID (`sid`) 和票据 (`ticket`)。

**第 2 步：调用业务接口**

所有业务接口都必须在请求头中携带以下签名信息：

- `X-Client-Type`: 固定为 `mobile`
- `x-client-id`: 从步骤1获取的 `sid`
- `X-Timestamp`: 当前的Unix时间戳
- `X-Trace-ID`: 随机生成的唯一字符串 (Nonce)
- `X-Auth-Token`: 使用步骤1获取的 `ticket` 对请求内容计算出的HMAC签名

### 2.2. Web 客户端 (B/S) 认证

Web端继续使用 `CSRF Token` + `动态 HMAC 签名` 的双重安全机制，通过 `GET /api/init/session` 初始化会话。

---

## 3. 主要 API 端点

### 3.1. `POST /api/native/session`

- **功能**: 为原生客户端初始化一个安全的会话，并下发加密的动态密钥。
- **请求体**:
  ```json
  {
    "credential": "YOUR_STATIC_MOBILE_CLIENT_SECRET"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "data": "BASE64_ENCODED_AES_ENCRYPTED_PAYLOAD"
  }
  ```
- **关键逻辑**: 验证静态密钥，生成动态 `sid` 和 `ticket`，加密后返回。

### 3.2. `POST /api/images/process`

- **功能**: (主要供Web端使用) 接收上传的图片，进行处理、缓存，并返回图片的访问URL。
- **请求体**: `multipart/form-data` 格式，包含一个或多个图片文件。
- **成功响应**:
  ```json
  {
    "success": true,
    "data": {
      "images": [
        {
          "id": "图片的originalHash",
          "originalUrl": "/api/images/get/original/...",
          "processedUrl": "/api/images/get/processed/...",
          "corners": [[x1, y1], ...],
          "timestamp": 1693200000000
        }
      ]
    }
  }
  ```
- **关键逻辑**:
    1.  为上传的图片计算SHA256哈希值 (`originalHash`)。
    2.  检查该哈希值是否存在于缓存中。
    3.  如果**缓存命中**，直接返回已存的URL。
    4.  如果**缓存未命中**，调用 `TextIn` API处理图片，将处理前后的图片存入磁盘，记录元数据，然后返回新生成的URL。

### 3.3. `GET /api/images/get/:type/:hash`

- **功能**: 提供对缓存图片的访问。
- **URL参数**:
    - `:type`: `original` 或 `processed`。
    - `:hash`: 图片内容的SHA256哈希值。
- **请求头**: 需要携带与业务接口相同的认证头（如 `X-Auth-Token` 等）。
- **成功响应**: 返回图片文件的二进制流。
- **关键逻辑**:
    1.  **此接口受应用的HMAC安全中间件保护，并非公开的静态文件服务。**
    2.  在响应请求前，会校验 `hash` 参数的格式 (`^[a-f0-9]{64}$`)，防止路径遍历攻击。
    3.  验证通过后，从缓存目录 (`temp/image-cache/`) 读取相应文件并返回。

### 3.4. `POST /api/pdf/generate`

- **功能**: 将一批图片（部分来自缓存，部分由用户修改）合成为一个PDF文件。
- **请求体**:
  ```json
  {
    "images": [
      { "id": "image_hash_1" }, // 用户未修改的图片，从缓存读取
      { "id": "image_hash_2", "data": "user_modified_base64..." }, // 用户手动裁剪的图片
      { "id": "image_hash_3" }
    ],
    "options": { ... } // PDF生成选项，如页边距
  }
  ```
- **成功响应**: 返回 `application/pdf` 类型的PDF文件流。
- **关键逻辑**:
    1.  遍历 `images` 数组，从缓存或请求体中的 `data` 字段收齐所有图片。
    2.  解码所有图片，获取它们的尺寸。
    3.  计算所有图片宽度和宽高比的**中位数**，从而得出一个统一的**目标页面尺寸**。
    4.  将所有图片调整到此目标尺寸（尺寸差异小的直接拉伸，差异大的则填充白边以保持比例）。
    5.  使用 `jsPDF` 库将调整后的图片逐页写入PDF并返回。

---

## 4. 测试建议

### 4.1. 关键测试点

1.  **认证流程**:
    -   确保原生客户端必须先调用 `/api/native/session` 才能访问业务接口。
    -   尝试使用错误的/过期的 `sid` 或 `ticket` 调用业务接口，预期会收到401/403错误。
    -   检查 `X-Timestamp` 和 `X-Trace-ID` 的校验是否生效。

2.  **图片缓存**:
    -   **缓存未命中**: 首次上传一张新图片，记录返回的 `id` (hash)。
    -   **缓存命中**: 再次上传同一张图片，确认返回的 `id` 与第一次完全相同，且接口响应速度显著加快。
    -   **缓存访问**: 使用返回的 `.../get/.../:hash` URL，携带正确的认证头，确认能成功获取图片。不带认证头则应失败。

3.  **PDF生成**:
    -   **尺寸统一性**: 上传一批尺寸差异巨大的图片（例如，一张横向长图，一张纵向长图，几张普通4:3照片），生成PDF后，检查所有页面的尺寸是否完全一致。
    -   **内容保真度**: 检查尺寸差异大的图片在PDF中是否被添加了白边（Letterboxing）而不是被压扁或拉长。
    -   **混合数据源**: 调用 `/api/pdf/generate` 时，混合使用仅带 `id` 的图片和带 `id` + `data` 的图片，确认最终生成的PDF包含了正确的（缓存中的或新提供的）图片内容。

### 4.2. 调试指引

- **追踪请求**: 所有API请求的日志都应包含 `requestId` 和 `sessionId`。当遇到问题时（如图片处理失败），可使用 `sessionId` 在Kibana中筛选出从用户请求开始到处理完成的完整日志链路，以快速定位问题环节。
- **API测试工具**: 使用 Postman 或类似工具时，请严格遵循原生客户端的认证流程：先请求 `/api/native/session`，手动解密/解析出 `sid` 和 `ticket`，然后在后续请求中手动构建 `X-Auth-Token` 等请求头。
