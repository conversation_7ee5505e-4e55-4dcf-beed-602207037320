# C/S 架构接口安全设计方案 (V4 - 最终优化版)

## 1. 目标

在现有 B/S (Browser/Server) 架构的基础上，增加对 C/S (Client/Server) 架构的原生客户端（iOS App）的支持。新方案旨在为原生客户端提供一套安全、高效的 API 认证机制，同时确保现有 Web 客户端的功能和安全性不受影响。

## 2. 核心原则

- **客户端区分**: 后端能够清晰、安全地识别出请求是来自 Web 浏览器还是原生 App。
- **安全策略分离**:
    - **Web 端**: 继续使用现有的 `CSRF Token` + `动态 HMAC 签名` 的双重安全机制。
    - **原生客户端**: 绕过不适用于 App 的 CSRF 防护，仅使用增强的 `动态 HMAC 签名` 机制来保证接口安全。
- **密钥管理与分发**:
    - **原生客户端**: 内置一个静态的、长期的`客户端密钥 (Client Secret)`。
    - **密钥交换加密**: 原生客户端使用静态密钥向服务器请求动态密钥时，服务器下发的动态密钥本身必须经过加密，防止在传输过程中被截获。
    - **动态会话密钥**: 所有业务请求都使用服务器动态生成的、与会话绑定的短期 HMAC 密钥进行签名。
- **语义混淆与信息最小化**:
    - 对请求和响应中的敏感字段名进行混淆，避免直接暴露其用途。
    - 采用“约定优于配置”的原则，仅在必要时下发非默认配置（如 `ttl`），最小化信息暴露。

## 3. 认证流程设计

### 3.1. Web 客户端流程 (保持不变)

保持现有流程，使用 `GET /api/init/session` 获取包含 HMAC 密钥的 `handshake` Token，后续请求通过 `x-request-id` 进行 CSRF 和 HMAC 验证。

### 3.2. 原生客户端 (iOS) 流程 (最终优化版)

#### 步骤 A: 静态密钥与默认 TTL 预置

- 在 iOS App 的代码中，安全地预置一个静态的 `CLIENT_SECRET` 和一个默认的会话生命周期 `DEFAULT_TTL` (例如: 3600)。

#### 步骤 B: 加密并混淆的会话初始化

- **接口**: `POST /api/native/session`
- **请求 Body**: `{"credential": "YOUR_STATIC_MOBILE_CLIENT_SECRET"}`
- **后端逻辑**:
    1.  验证 `credential`。
    2.  生成会话 ID (`sid`) 和动态 HMAC 密钥 (`ticket`)。
    3.  决定本次会话的 `ttl`。如果使用默认值，则不在载荷中体现；如果需要覆盖，则指定其值。
    4.  构建载荷 (Payload) JSON 对象。`ttl` 字段为可选：`{"sid": "...", "ticket": "...", "ttl": 7200}` 或 `{"sid": "...", "ticket": "..."}`。
    5.  使用 `CLIENT_SECRET` 作为加密密钥，通过 AES-256-GCM 算法加密 Payload。
    6.  将加密后的数据进行 Base64 编码后放入响应的 `data` 字段。
- **响应 Body**: `{"success": true, "data": "BASE64_ENCODED_AES_ENCRYPTED_PAYLOAD"}`
- **客户端操作**:
    1.  解密 `data` 字段，得到 Payload JSON。
    2.  从中解析出 `sid` 和 `ticket`。
    3.  检查是否存在 `ttl` 字段。如果存在，则使用该值作为会话生命周期；如果不存在，则使用客户端内置的 `DEFAULT_TTL`。
    4.  在客户端安全地存储 `sid` 和 `ticket`，并根据最终确定的 `ttl` 管理会话生命周期。

#### 步骤 C: 业务 API 调用

- **接口**: 例如 `POST /api/images/upload`
- **请求 Header**:
    - `X-Client-Type: mobile`
    - `x-client-id`: `sid`
    - `X-Timestamp`: Unix 时间戳
    - `X-Trace-ID`: Nonce
    - `X-Auth-Token`: 使用 `ticket` 计算的 HMAC 签名
- **后端验证逻辑**: (与 V3 方案一致)
    1.  CSRF 中间件根据 `X-Client-Type` 跳过检查。
    2.  HMAC 中间件根据 `X-Client-Type` 选择原生客户端验证路径，使用 `sid` 查询到对应的 `ticket` (HMAC 密钥) 并完成验签。

## 4. 加密规范

- **算法**: `AES-256-GCM`
- **密钥**: 预置的 `CLIENT_SECRET` (需为32字节)。
- **初始化向量 (IV)**: 每次加密生成一个随机、唯一的12字节 IV，并与密文拼接传输: `Base64(IV + Ciphertext + AuthTag)`。

## 5. 后端代码改造点

1.  **新增原生会话接口**: 实现 `POST /api/native/session`。
2.  **修改 CSRF 中间件**: 增加 `X-Client-Type` 判断，跳过检查。
3.  **修改 HMAC 中间件**: 增加 `X-Client-Type` 判断，分离 Web 和 Native 的验证逻辑。
4.  **增加加密/解密辅助函数**: 在 `utils` 目录下创建 `cryptoUtils.ts`。