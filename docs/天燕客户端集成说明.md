# Web 应用在 iOS/Android WebView 环境下的兼容性与功能增强技术指南

## 1. 概述

将现代 Web 应用嵌入到移动端原生应用的 WebView 中是一种常见的混合开发模式。然而，这种模式常常带来一系列挑战，因为 Web 应用运行在一个受限的、与标准浏览器不同的环境中。本文档旨在总结一套在实践中行之有效的技术方案，用于解决在 WebView 环境下常见的文件下载、原生能力调用和 UI 适配等问题。

---

## 2. 问题一：在 WebView 中实现可靠的文件下载

### 2.1. 挑战

标准的 Web 下载方式（如 `<a>` 标签的 `download` 属性或 `window.location.href`）在移动端 WebView 中通常会失效或体验不佳。WebView 默认没有直接访问设备文件系统的权限，导致下载操作无法完成或无法让用户选择存储位置。

### 2.2. 解决方案：原生桥接 (Native Bridge)

核心思想是通过前端 JavaScript 与原生应用（iOS/Android）预先注入的接口进行通信，将文件数据传递给原生端，由原生代码负责将其保存到设备的文件系统中。

#### 2.3. 实现步骤

**第一步：能力检测**

前端必须首先检查原生桥接是否可用。

```typescript
// file: src/utils/deviceDetection.ts

/**
 * 检测是否支持原生桥接
 */
export function supportsNativeBridge(): boolean {
  // iOS (WKWebView)
  if (window.webkit?.messageHandlers?.saveFile) {
    return true;
  }
  // Android
  if (window.Android?.saveFile) {
    return true;
  }
  return false;
}
```

**第二步：定义与原生 App 绑定的桥接接口**

与原生开发团队约定一个清晰的接口。原生代码需要向 WebView 的 `window` 对象注入相应的处理器。

- **Android**:
  ```javascript
  // Android 端注入的对象和方法签名
  window.Android = {
    saveFile: function(base64Data: string, fileName: string) { /* ... */ }
  };
  ```
- **iOS (WKWebView)**:
  ```javascript
  // iOS 端注入的 messageHandler 和期望的参数结构
  window.webkit.messageHandlers.saveFile.postMessage(
    // 参数必须是 JSON 序列化后的字符串
    JSON.stringify({
      data: "...",     // Base64 编码的文件数据
      fileName: "...", // 文件名
      mimeType: "application/pdf" // 文件MIME类型
    })
  );
  ```

**第三步：前端调用与数据准备**

由于文件数据无法直接传递，需要将其转换为 Base64 编码的字符串。

```typescript
// file: src/utils/pdfGenerator.ts

/**
 * 将Blob转换为Base64
 */
function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      // 移除data:application/pdf;base64,前缀，只保留base64数据
      const base64Data = (reader.result as string).split(',')[1];
      resolve(base64Data);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * 使用原生桥接下载PDF
 */
async function saveWithNativeBridge(blob: Blob, fileName: string): Promise<void> {
  try {
    const base64Data = await blobToBase64(blob);

    // iOS WebView (WKWebView)
    if (window.webkit?.messageHandlers?.saveFile) {
      const params = JSON.stringify({
        data: base64Data,
        fileName: fileName,
        mimeType: 'application/pdf'
      });
      console.log('使用iOS原生桥接下载PDF');
      window.webkit?.messageHandlers?.saveFile?.postMessage(params);
      return;
    }

    // Android WebView
    if (window.Android?.saveFile) {
      console.log('使用Android原生桥接下载PDF');
      window.Android.saveFile(base64Data, fileName);
      return;
    }

    throw new Error('原生桥接接口不可用');
  } catch (error) {
    console.error('原生桥接下载失败:', error);
    throw error; // 将错误继续抛出，以便上层调用者可以捕获
  }
}
```
*注意*: iOS 的 `postMessage` 只能接受特定的数据类型，将复杂的参数 `JSON.stringify` 成字符串是当前代码中采用的可靠做法。

---

## 3. 问题二：从 WebView 请求原生设备权限

### 3.1. 挑战

Web 应用本身无法直接触发 iOS 或 Android 的系统级权限请求对话框（如相机、相册访问）。这些敏感操作必须由原生应用本身发起。

### 3.2. 解决方案：双向异步通信桥接

利用原生桥接进行双向通信。前端向原生发送权限请求，原生处理后，再调用前端预设在 `window` 上的全局函数来返回结果。

#### 3.3. 实现步骤

**第一步：前端发起权限请求**

通过 `isPermissions` 函数，向 iOS 原生代码发送一个请求，指明需要什么权限（如 `CAMERA`）。

**第二步：原生处理并回调**

原生应用接收到消息后，执行系统级的权限请求。当用户做出选择后，原生应用会执行 `window.handleFlutterResult` 函数，将权限结果传回给前端。

- **原生调用的 JS 函数**: `window.handleFlutterResult(num: number)`
  - `num = 1`: 有权限
  - `num = 2`: 无权限

**第三步：前端封装异步等待逻辑**

`isPermissions` 函数将整个异步过程封装成一个 `Promise`，并通过轮询 `window.__permission_ios` 变量来获取原生回调的结果。

```typescript
// file: src/utils/jsBridge.ts

type NoPermissionType = 'CAMERA' | 'ALBUM' | 'AUDIO';

export const isPermissions = (type: NoPermissionType): Promise<boolean> => {
  return new Promise(resolve => {
    if (window.webkit?.messageHandlers?.permission) {
      window.__permission_ios = undefined; // 重置状态

      // 轮询检查原生回调是否完成
      let attempts = 0;
      const maxAttempts = 60; // 增加轮询次数以应对慢速设备
      const checkInterval = 50; // ms

      const checkPermission = () => {
        attempts++;
        if (window.__permission_ios !== undefined) {
          resolve(window.__permission_ios);
        } else if (attempts < maxAttempts) {
          setTimeout(checkPermission, checkInterval);
        } else {
          // 超时则默认为 true，避免阻塞流程
          resolve(true);
        }
      };

      // 先发送消息，再开始轮询
      window.webkit.messageHandlers.permission.postMessage(type);
      setTimeout(checkPermission, checkInterval);
    } else {
      // 在非 iOS WebView 或不支持的环境中，直接认为是拥有权限
      resolve(true);
    }
  });
};

// 全局回调函数，由原生端调用
// 处理flutter权限回调  1: 有权限  2: 无权限
window.handleFlutterResult = (num: number) => {
  if (+num === 2) {
    window.__permission_ios = false;
  } else {
    window.__permission_ios = true;
  }
};
```
*设计说明*: 使用轮询 (`setTimeout`) 检查一个特定命名的全局变量 (`__permission_ios`) 的状态，是当前代码中处理原生异步回调的方式。它解耦了请求发起和结果接收的过程。

