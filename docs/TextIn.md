# TextIn API 集成经验总结

## API 基本信息

- **API URL**: `https://api.textin.com/ai/service/v1/crop_enhance_image`
- **认证方式**: 通过请求头传递 `x-ti-app-id` 和 `x-ti-secret-code`
- **功能**: 文档图像智能裁剪、增强和校正

## 请求方式

### 错误的方式: multipart/form-data
```bash
curl -X POST "https://api.textin.com/ai/service/v1/crop_enhance_image" \
  -H "x-ti-app-id: YOUR_APP_ID" \
  -H "x-ti-secret-code: YOUR_SECRET" \
  -F "image=@image.jpg" \
  -F "crop=true" \
  -F "dewarp=true"
```

**问题**: 会返回错误码 40303 "文件类型不支持"，即使文件格式正确。

### 正确的方式: 二进制上传
```bash
curl -X POST "https://api.textin.com/ai/service/v1/crop_enhance_image" \
  -H "x-ti-app-id: YOUR_APP_ID" \
  -H "x-ti-secret-code: YOUR_SECRET" \
  -H "Content-Type: application/octet-stream" \
  --data-binary "@image.jpg"
```

**优点**: 稳定可靠，成功率高。

## 响应格式

```json
{
  "x_request_id": "95e7cd08fd815e9bb2c22aa60d880a43",
  "version": "v2.0.4",
  "code": 200,
  "duration": 135,
  "message": "success",
  "msg": "success",
  "result": {
    "image_list": [
      {
        "angle": 0,
        "cropped_height": 544,
        "cropped_width": 688,
        "image": "base64_encoded_image_data",
        "position": [x1, y1, x2, y2, x3, y3, x4, y4]
      }
    ],
    "origin_height": 960,
    "origin_width": 1280
  }
}
```

## 常见错误码

| 错误码 | 说明 | 可能原因 |
|--------|------|----------|
| 200 | 成功 | - |
| 40101 | 参数错误 | 请求参数格式不正确 |
| 40301 | 签名错误 | API密钥无效或过期 |
| 40303 | 文件类型不支持 | 1. 使用了multipart/form-data上传<br>2. 文件格式不支持<br>3. 文件大小超过50MB<br>4. 图片尺寸不在20-10000像素范围内<br>5. API密钥无效 |
| 40401 | 余额不足 | 账户余额不足 |

## 技术限制

### 文件要求
- **支持格式**: JPG, PNG, GIF, WebP, BMP, TIFF
- **文件大小**: 最大 50MB
- **图片尺寸**: 20-10000 像素范围内
- **最小尺寸**: 宽度和高度都不能小于 20 像素

### 处理参数
- `crop`: 是否裁剪文档边界
- `dewarp`: 是否校正透视变形
- `direction_correct`: 是否自动旋转校正
- `blur_reduce`: 是否减少模糊
- `quality`: 输出质量 (1-100)

## 集成最佳实践

### 1. 使用二进制上传
```javascript
const response = await axios.post(
  'https://api.textin.com/ai/service/v1/crop_enhance_image',
  imageBuffer,
  {
    headers: {
      'Content-Type': 'application/octet-stream',
      'x-ti-app-id': appId,
      'x-ti-secret-code': secret,
    },
    timeout: 30000,
  }
);
```

### 2. 响应格式兼容处理
```javascript
// 处理新旧版本API响应格式
if (response.data.result?.image_list?.length && !response.data.data) {
  const firstImage = response.data.result.image_list[0];
  response.data.data = {
    result: firstImage.image,
    corners: [
      [firstImage.position[0], firstImage.position[1]],
      [firstImage.position[2], firstImage.position[3]],
      [firstImage.position[4], firstImage.position[5]],
      [firstImage.position[6], firstImage.position[7]]
    ]
  };
}
```

### 3. 错误处理策略
```javascript
if (response.data.code === 40303) {
  // 详细分析40303错误原因
  logger.error('文件类型错误分析', {
    fileSize: imageBuffer.length,
    exceedsLimit: imageBuffer.length > 50 * 1024 * 1024,
    tooSmall: imageBuffer.length < 1024,
    dimensions: imageDimensions,
    dimensionsOutOfRange: width < 20 || height < 20 || width > 10000 || height > 10000
  });
}
```

### 4. 批量处理优化
```javascript
// 请求间添加延迟避免频率限制
for (let i = 0; i < images.length; i++) {
  const result = await processImage(images[i]);
  results.push(result);
  
  // 避免过快请求
  if (i < images.length - 1) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}
```

## 调试技巧

### 1. 日志记录
```javascript
logger.debug('调用TextIn API', {
  filename,
  fileSize: imageBuffer.length,
  fileSizeMB: (imageBuffer.length / 1024 / 1024).toFixed(2),
  dimensions: { width, height },
  appIdFirst4: appId.substring(0, 4) + '****',
});
```

### 2. 使用测试脚本
项目提供了 `test/test-textin-api.sh` 脚本用于快速测试API可用性：

```bash
# 使用默认测试图片
./test-textin-api.sh YOUR_APP_ID YOUR_SECRET

# 使用自定义图片
./test-textin-api.sh YOUR_APP_ID YOUR_SECRET /path/to/image.jpg
```

## 配置管理

### 优先级顺序
1. Nacos配置中心 (生产环境推荐)
2. 环境变量 (开发/测试环境)

### Nacos配置
- **Data ID**: `textin-api.json`
- **Group**: `image-to-pdf`
- **配置内容**:
```json
{
  "apiUrl": "https://api.textin.com/ai/service/v1/crop_enhance_image",
  "appId": "your_app_id",
  "secret": "your_secret"
}
```

### 环境变量配置
```bash
TEXTIN_APP_ID=your_app_id
TEXTIN_SECRET=your_secret
```

## 性能优化建议

1. **缓存机制**: 对相同图片的处理结果进行缓存
2. **异步处理**: 使用队列系统处理大批量图片
3. **失败重试**: 实现指数退避重试机制
4. **监控告警**: 监控API调用成功率和响应时间
5. **降级策略**: API不可用时切换到本地图片处理

## 版本历史

- **v2.0.4**: 当前版本，使用新的响应格式
- **v1.x**: 旧版本，使用 `data.result` 格式 (已废弃)

## 注意事项

1. **API密钥安全**: 不要在前端代码或日志中暴露完整的API密钥
2. **请求频率**: 避免过于频繁的API调用，建议添加适当延迟
3. **错误处理**: 40303错误可能有多种原因，需要详细分析
4. **版本兼容**: 保持对新旧响应格式的兼容性处理
5. **测试环境**: 使用测试脚本验证API配置的正确性