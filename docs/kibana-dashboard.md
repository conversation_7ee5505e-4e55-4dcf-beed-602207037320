# Kibana 日志分析指南

本文档提供了在 Kibana 中分析图片转PDF应用日志的指导。

## 📊 关键日志字段

### 通用字段
- `timestamp`: 时间戳
- `level`: 日志级别 (error, warn, info, debug, trace)
- `message`: 日志消息
- `service`: 服务名 (image-to-pdf-backend)
- `environment`: 环境 (development, production)
- `requestId`: 请求唯一标识
- `sessionId`: 会话唯一标识
- `ip`: 客户端IP地址

### 性能字段
- `duration`: 处理时长(毫秒)
- `statusCode`: HTTP状态码
- `performance.memoryUsage`: 内存使用情况
- `performance.cpuUsage`: CPU使用情况

### 业务字段
- `metadata.totalFiles`: 上传文件数量
- `metadata.processedCount`: 处理成功数量
- `metadata.processingMode`: 处理模式
- `metadata.fileName`: 文件名
- `metadata.fileSize`: 文件大小

## 🔍 常用搜索查询

### 1. 用户上传图片但处理数量不匹配
```
message:"Image batch processing completed" AND metadata.discrepancies.inputOutputMismatch:true
```

### 2. TextIn API 调用失败
```
level:error AND (message:"TextIn API call failed" OR message:"Textin API processing failed")
```

### 3. 特定会话的处理链路
```
sessionId:"your-session-id" AND (message:"Request started" OR message:"Image batch processing" OR message:"TextIn API processing" OR message:"Request completed")
```

### 4. 高错误率时段
```
metadata.errorRate:>10 AND level:warn
```

### 5. 性能问题排查
```
duration:>5000 OR metadata.avgResponseTime:>3000
```

### 6. 文件验证失败
```
message:"Invalid files detected" OR message:"File processing failed"
```

### 7. 内存使用过高
```
metadata.memoryUsagePercent:>85 AND level:warn
```

## 📈 推荐 Dashboard

### 1. 系统健康概览
- 请求总数和错误率趋势
- 平均响应时间
- 内存和CPU使用率
- 服务状态分布

### 2. 图片处理分析
- 上传文件数 vs 处理成功数
- 处理模式分布 (auto/manual/hybrid)
- TextIn API 成功率
- 低分辨率图片统计

### 3. 错误分析
- 错误类型分布
- 错误频率趋势
- 失败的文件类型统计
- TextIn API 错误码分析

### 4. 用户行为分析
- 会话时长分布
- 用户上传模式
- 地理位置分析 (基于IP)

## 🚨 告警规则建议

### 1. 严重告警 (Critical)
```
# 错误率超过20%
metadata.errorRate:>20

# 内存使用率超过90%
metadata.memoryUsagePercent:>90

# 系统状态为critical
metadata.status:"critical"
```

### 2. 警告告警 (Warning)
```
# 平均响应时间超过5秒
metadata.avgResponseTime:>5000

# TextIn API 连续失败
message:"TextIn API call failed" AND @timestamp:[now-5m TO now]

# 处理失败率超过10%
metadata.failedCount:>0 AND (metadata.failedCount / metadata.totalFiles):>0.1
```

## 🔧 Kibana 可视化配置

### 1. 时间序列图
- X轴: @timestamp
- Y轴: duration (avg), metadata.errorRate (avg)
- 分组: level, metadata.processingMode

### 2. 饼图
- 切片: metadata.processingMode
- 指标: 文档计数

### 3. 数据表
- 列: @timestamp, requestId, sessionId, level, message, duration
- 过滤: level:error OR level:warn

### 4. 热力图
- X轴: @timestamp (按小时)
- Y轴: ip 或 sessionId
- 指标: 文档计数

## 📋 故障排查流程

### 问题: "上传10张图片只处理了8张"

1. **查找相关会话**:
   ```
   metadata.totalFiles:10 AND metadata.processedCount:8
   ```

2. **检查文件验证阶段**:
   ```
   sessionId:"found-session-id" AND message:"File validation"
   ```

3. **分析每个文件处理**:
   ```
   sessionId:"found-session-id" AND message:"Processing individual file"
   ```

4. **查看失败详情**:
   ```
   sessionId:"found-session-id" AND (level:error OR level:warn)
   ```

5. **检查TextIn API调用**:
   ```
   sessionId:"found-session-id" AND message:"TextIn API processing"
   ```

### 问题排查要点
- 文件格式是否支持
- 文件大小是否超限
- 分辨率是否符合要求
- TextIn API是否正常响应
- 网络是否存在超时

## 🎯 性能优化建议

基于日志分析的优化方向：
1. **高频错误**: 优先修复出现频率最高的错误
2. **慢查询**: 识别和优化响应时间超过阈值的请求
3. **资源使用**: 监控内存和CPU使用趋势
4. **用户体验**: 分析用户流失点和卡顿环节

此日志系统将为问题诊断提供完整的数据支撑，确保快速定位和解决用户反馈的问题。