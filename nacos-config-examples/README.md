# Nacos 配置示例

本目录包含了在Nacos配置中心中需要配置的示例文件。

> **注意**: 如需了解如何部署和配置Nacos服务器，请参考 [backend/nacos-config-examples/README.md](../backend/nacos-config-examples/README.md)。

## 配置项

### 1. security.yaml (推荐) 或 security.json
**Data ID**: `security.yaml` (推荐) 或 `security.json`  
**Group**: `image-to-pdf`

安全配置，包含允许的域名、CORS设置、CSRF保护和多层Rate Limiting配置。

**推荐使用YAML格式**：支持注释，配置更清晰易懂。系统会优先加载YAML格式，如果不存在则回退到JSON格式。

**YAML格式 (推荐，详见 security.yaml 文件)**：
```yaml
# 安全配置文件 - 包含详细的使用说明和计算方法示例
allowedOrigins:
  - "https://your-domain.com"
corsEnabled: true
csrfEnabled: true
rateLimiting:
  general:
    enabled: true
    windowMs: 900000    # 15分钟
    maxRequests: 200    # ✅ 已调整，避免分次上传被阻
  fileUpload:
    enabled: true
    windowMs: 60000     # 1分钟
    maxRequests: 20     # 1分钟内最多20次上传请求
  imageProcessing:
    enabled: true
    windowMs: 60000     # 1分钟  
    maxRequests: 50     # ✅ 已调整为50次
  # ... 完整配置请参考 security.yaml
```

**JSON格式 (兼容)**：
```json
{
  "allowedOrigins": ["https://your-domain.com"],
  "corsEnabled": true,
  "csrfEnabled": true,
  "rateLimiting": {
    "general": {"enabled": true, "windowMs": 900000, "maxRequests": 200},
    "fileUpload": {"enabled": true, "windowMs": 60000, "maxRequests": 20},
    "imageProcessing": {"enabled": true, "windowMs": 60000, "maxRequests": 50}
  }
}
```

**字段说明**:
- `allowedOrigins`: 生产环境中允许的前端域名列表
- `corsEnabled`: 是否启用CORS（默认true）
- `csrfEnabled`: 是否启用CSRF保护（默认true）
- `rateLimiting`: 多层Rate Limiting配置
  - `general`: 通用API请求限制 (15分钟/200次) ✅ **已调整避免分次上传被阻**
  - `fileUpload`: 文件上传限制 (1分钟/20次)
    - `maxFileSize`: 单文件最大大小(字节) - 50MB
    - `maxTotalSize`: 总上传大小限制(字节) - 200MB  
    - `maxFilesPerRequest`: 单次请求最大文件数 - 50个
    - **计算方式**: 每调用一次 /api/images/upload 接口算1次，无论上传多少张图片
  - `imageProcessing`: 图像处理限制 (1分钟/50次) ✅ **已调整**
    - `maxImagesPerBatch`: 批量处理最大图片数 - 100张
    - `maxConcurrentJobs`: 最大并发处理任务数 - 5个
    - **计算方式**: 每张图片调用TextIn API处理算1次，与上传次数无关
  - `pdfGeneration`: PDF生成限制 (5分钟/10次)
    - `maxPagesPerPdf`: 单个PDF最大页数 - 200页
    - `maxConcurrentGeneration`: 最大并发生成数 - 3个
    - **计算方式**: 每调用一次 /api/pdf/generate 接口算1次
  - `externalApiCalls`: 外部API调用限制 (1分钟/50次)
    - `retryAttempts`: 失败重试次数 - 3次
    - `retryDelay`: 重试延迟(毫秒) - 1000ms
    - **计算方式**: 每调用一次外部API算1次，包括重试
  - `perIpStrict`: 按IP严格限制 (1分钟/300次) ✅ **已调整**
    - `blockDuration`: 超限后阻断时长(毫秒) - 5分钟
    - `enabled`: 默认false，生产环境可启用防止滥用
    - **计算方式**: 按客户端IP统计所有请求次数

### 2. textin-api.json
**Data ID**: `textin-api.json`  
**Group**: `image-to-pdf`

TextIn API配置（现有配置）。

## 在Nacos中配置

### 方法1: 通过Nacos控制台
1. 登录Nacos控制台
2. 进入"配置管理" -> "配置列表"
3. 点击"+"创建配置
4. 填写配置信息：
   - **Data ID**: `security.yaml` (推荐) 或 `security.json`
   - **Group**: `image-to-pdf`
   - **配置格式**: YAML 或 JSON
   - **配置内容**: 复制 security.yaml 文件内容 (推荐)

### 方法2: 通过API配置

**使用YAML格式 (推荐)**：
```bash
# 使用security.yaml文件内容
curl -X POST 'http://nacos-server:8848/nacos/v1/cs/configs' \
  -d 'dataId=security.yaml' \
  -d 'group=image-to-pdf' \
  -d @security.yaml
```

**使用JSON格式 (兼容)**：
```bash
# 简化的JSON配置示例
curl -X POST 'http://nacos-server:8848/nacos/v1/cs/configs' \
  -d 'dataId=security.json' \
  -d 'group=image-to-pdf' \
  -d 'content={"allowedOrigins":["https://your-domain.com"],"corsEnabled":true,"csrfEnabled":true,"rateLimiting":{"general":{"enabled":true,"windowMs":900000,"maxRequests":200}}}'
```

## 环境变量回退

如果Nacos不可用，系统会回退到环境变量：

```bash
# Docker Compose 或环境变量
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
```

## 开发vs生产环境

### 开发环境
- 自动允许本地网络IP地址
- 从Origin头动态添加本地网络域名
- CSRF可以通过`DISABLE_CSRF=true`禁用

### 生产环境
- 严格验证`allowedOrigins`列表
- 必须通过Nacos或环境变量明确配置
- 所有安全检查默认启用

## 实时配置更新

应用会监听Nacos配置变更，配置更新后会立即生效，无需重启服务。

**注意**: 
- Rate Limiting配置有1小时缓存机制，配置更新后最多1小时生效
- 系统会同时监听 `security.yaml` 和 `security.json`，任一格式更新都会触发配置刷新
- 优先使用YAML格式，JSON格式作为兼容回退

**Rate Limiting计算说明**：
详细的计算方法和使用场景示例请参考 `security.yaml` 文件头部注释。

配置更新日志示例：
```json
{
  "level": "audit", 
  "message": "Security config updated from Nacos",
  "dataId": "security.yaml",
  "allowedOriginsCount": 3,
  "corsEnabled": true,
  "rateLimitingLevels": {
    "general": true,
    "fileUpload": true,
    "imageProcessing": true,
    "pdfGeneration": true,
    "externalApiCalls": true,
    "perIpStrict": false
  }
}
```

## 配置说明与推荐值

### 生产环境推荐配置
- **general**: 15分钟200次请求 (已调整避免分次上传被阻)
- **fileUpload**: 单文件50MB，总200MB，单次50文件，1分钟20次
- **imageProcessing**: 批量100张，并发5个任务，1分钟50次 (已调整)  
- **pdfGeneration**: 单PDF200页，并发3个，5分钟10次
- **externalApiCalls**: API调用1分钟50次，重试3次
- **perIpStrict**: 建议启用，1分钟300次 (已调整)，超限封5分钟

### 开发环境推荐配置
- 将所有限制放宽2-3倍
- perIpStrict保持禁用状态
- 可以通过环境变量临时调整