# 安全配置文件 - 控制跨域、CSRF保护和多层访问限制
# Nacos Data ID: security.yaml
# Group: image-to-pdf

# ========================================
# 【重要】Rate Limiting 计算方法说明
# ========================================
# 
# 用户完整流程示例：打开网页 -> 上传100张图片 -> 生成PDF
#
# 1. 打开网页：
#    - 获取CSRF token: 1次
#    - 健康检查/配置获取: 1-2次
#    - general计数: 约2次
#
# 2. 上传100张图片：
#    情况A - 分100次上传(每次1张):
#      - fileUpload计数: 100次 (每次调用/api/images/upload算1次)
#      - general计数: 100次 (所有API调用都计入general)
#      - imageProcessing计数: 100次 (每张图片处理算1次)
#    
#    情况B - 1次批量上传(一次性100张):
#      - fileUpload计数: 1次 (只调用1次上传接口)
#      - general计数: 1次 (只有1次API调用)
#      - imageProcessing计数: 100次 (仍需处理100张图片)
#
# 3. 生成PDF：
#    - pdfGeneration计数: 1次
#    - general计数: 1次
#
# 总计算结果：
# - 情况A总计: general=103次 ❌ 超过100次限制，用户会被拒绝
# - 情况B总计: general=4次 ✅ 远低于限制，推荐批量上传
#
# ========================================

# 跨域访问控制
allowedOrigins:
  - "https://your-domain.com"      # 生产环境允许的前端域名
  - "https://www.your-domain.com"  # 带www的主域名  
  - "https://app.your-domain.com"  # 应用子域名
  - "https://api.your-domain.com"  # API子域名

# 安全保护开关
corsEnabled: true  # 是否启用跨域资源共享(CORS)保护
csrfEnabled: true  # 是否启用跨站请求伪造(CSRF)保护

# HMAC身份验证配置 - 防止程序化攻击 (现使用动态密钥)
hmacAuth:
  enabled: true                    # 是否启用HMAC签名验证
  timestampTolerance: 300          # 时间戳容忍度(秒)，防重放攻击，默认5分钟
  requiredParams: []               # 必须参与签名的参数列表

# 移动端客户端认证配置
mobileClientAuth:
  secretKey: "test-key"            # 移动端客户端认证密钥，生产环境必须更换

# 多层访问频率限制配置
rateLimiting:
  
  # 通用API请求限制 - 适用于所有普通接口
  # 注意: 所有API调用(上传、处理、生成PDF等)都计入此限制
  general:
    enabled: true          # 是否启用通用限制
    windowMs: 900000       # 时间窗口15分钟(毫秒)
    maxRequests: 200       # 15分钟内最多200次请求
  
  # 文件上传专用限制 - 防止大量文件上传攻击
  # 计算方式: 每调用一次 /api/images/upload 接口算1次，无论上传多少张图片
  fileUpload:
    enabled: true          # 是否启用文件上传限制
    windowMs: 60000        # 时间窗口1分钟(毫秒)
    maxRequests: 20        # 1分钟内最多20次上传请求
    maxFileSize: 52428800  # 单文件最大50MB(字节)
    maxTotalSize: 209715200 # 单次请求总文件大小最大200MB(字节)
    maxFilesPerRequest: 50 # 单次请求最多50个文件
  
  # 图像处理专用限制 - 控制AI裁剪等计算密集操作
  # 计算方式: 每张图片调用TextIn API处理算1次，与上传次数无关
  imageProcessing:
    enabled: true          # 是否启用图像处理限制
    windowMs: 60000        # 时间窗口1分钟(毫秒)
    maxRequests: 50        # 1分钟内最多50次处理请求 (已调整)
    maxImagesPerBatch: 100 # 单次批量处理最多100张图片
    maxConcurrentJobs: 5   # 同时进行的处理任务最多5个
  
  # PDF生成专用限制 - 控制PDF导出频率
  # 计算方式: 每调用一次 /api/pdf/generate 接口算1次
  pdfGeneration:
    enabled: true          # 是否启用PDF生成限制
    windowMs: 300000       # 时间窗口5分钟(毫秒)
    maxRequests: 10        # 5分钟内最多10次PDF生成
    maxPagesPerPdf: 200    # 单个PDF文件最多200页
    maxConcurrentGeneration: 3 # 同时生成的PDF最多3个
  
  # 外部API调用限制 - 控制对TextIn等第三方服务的调用
  # 计算方式: 每调用一次外部API算1次，包括重试
  externalApiCalls:
    enabled: true          # 是否启用外部API调用限制
    windowMs: 60000        # 时间窗口1分钟(毫秒)
    maxRequests: 50        # 1分钟内最多50次外部API调用
    retryAttempts: 3       # API调用失败时最多重试3次
    retryDelay: 1000       # 重试间隔1秒(毫秒)
  
  # 严格IP访问限制 - 防止单个IP滥用系统资源
  # 计算方式: 按客户端IP统计所有请求次数
  perIpStrict:
    enabled: false         # 是否启用严格IP限制(生产环境建议启用)
    windowMs: 60000        # 时间窗口1分钟(毫秒)
    maxRequests: 300       # 1分钟内单IP最多300次请求 (已调整)
    blockDuration: 300000  # 超限后封锁该IP 5分钟(毫秒)

# ========================================
# 推荐配置策略
# ========================================
#
# 开发环境:
# - general.maxRequests: 500 (宽松限制)
# - perIpStrict.enabled: false (禁用IP限制)
# - 所有限制值可以放宽2-3倍
#
# 生产环境:
# - 建议启用 perIpStrict (enabled: true)
# - 根据实际用户行为调整各项限制
# - 监控日志中的rate limit超限情况进行优化
#
# ========================================