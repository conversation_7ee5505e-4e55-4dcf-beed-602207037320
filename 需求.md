需求的核心是优化在微信内置浏览器中的PDF下载体验。

当前基于`blob:` URL的下载方式在微信中存在问题，当用户使用“在浏览器中打开”功能时，会因页面状态丢失而导致下载失败。

新的需求方案如下：
1.  后端生成PDF后，需提供一个持久化的、可直接访问的HTTP下载链接。
2.  前端的“下载PDF”按钮需区分运行环境：
    *   在普通浏览器中，直接在新标签页打开该PDF链接。
    *   在微信浏览器中，则跳转到一个专门的中间下载页面，并将PDF链接作为URL参数传递。
3.  这个中间下载页面需要具备以下逻辑：
    *   若在微信中打开，则显示提示，引导用户“在浏览器中打开”。
    *   若在普通浏览器中打开，则显示一个“立即下载”按钮，点击后即可下载PDF。

此方案旨在通过一个带参数的中间页面，确保PDF下载链接在从微信切换到外部浏览器的过程中得以保持，从而解决下载失败的问题，提升用户体验。
