#!/bin/bash

# Textin API 测试脚本
# 使用方法: ./test-textin-api.sh YOUR_APP_ID YOUR_SECRET_CODE [IMAGE_PATH]

# 检查参数
if [ $# -lt 2 ] || [ $# -gt 3 ]; then
    echo "使用方法: $0 <APP_ID> <SECRET_CODE> [IMAGE_PATH]"
    echo "示例: $0 your_app_id your_secret_code"
    echo "或者: $0 your_app_id your_secret_code /path/to/image.jpg"
    exit 1
fi

APP_ID="$1"
SECRET_CODE="$2"
IMAGE_PATH="$3"

echo "=== Textin API 可用性测试 ==="
echo "API URL: https://api.textin.com/ai/service/v1/crop_enhance_image"
echo "APP_ID: ${APP_ID:0:4}****"
echo "SECRET: ${SECRET_CODE:0:4}****"
echo

# 确定使用的测试图片
if [ -n "$IMAGE_PATH" ]; then
    # 使用用户提供的图片路径
    if [ ! -f "$IMAGE_PATH" ]; then
        echo "错误: 图片文件不存在: $IMAGE_PATH"
        exit 1
    fi
    TEST_IMAGE="$IMAGE_PATH"
    echo "使用用户提供的图片: $TEST_IMAGE"
else
    # 创建一个简单的测试图片 (1x1 白色像素的PNG)
    TEST_IMAGE="test_image.png"
    echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" | base64 -d > "$TEST_IMAGE"
    echo "创建临时测试图片: $TEST_IMAGE (1x1 白色像素)"
fi

echo "测试图片大小: $(wc -c < "$TEST_IMAGE") bytes"
echo

# 方法1: 使用 multipart/form-data (推荐)
echo "=== 方法1: multipart/form-data 上传 ==="
curl -X POST "https://api.textin.com/ai/service/v1/crop_enhance_image" \
  -H "x-ti-app-id: $APP_ID" \
  -H "x-ti-secret-code: $SECRET_CODE" \
  -F "image=@$TEST_IMAGE" \
  -F "crop=true" \
  -F "dewarp=true" \
  -F "direction_correct=true" \
  -F "blur_reduce=true" \
  -F "quality=90" \
  -w "\n\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -v

echo -e "\n\n"

# 方法2: 使用二进制数据上传
echo "=== 方法2: 二进制数据上传 ==="
RESPONSE_FILE="textin_response.json"
curl -X POST "https://api.textin.com/ai/service/v1/crop_enhance_image" \
  -H "x-ti-app-id: $APP_ID" \
  -H "x-ti-secret-code: $SECRET_CODE" \
  -H "Content-Type: application/octet-stream" \
  --data-binary "@$TEST_IMAGE" \
  -w "\n\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -o "$RESPONSE_FILE" \
  -v

echo -e "\n=== 处理返回结果 ==="
if [ -f "$RESPONSE_FILE" ]; then
    # 检查响应是否成功
    CODE=$(cat "$RESPONSE_FILE" | grep -o '"code":[0-9]*' | cut -d':' -f2)
    if [ "$CODE" = "200" ]; then
        echo "API调用成功！正在提取并保存处理后的图片..."
        
        # 提取base64图片数据
        BASE64_IMAGE=$(cat "$RESPONSE_FILE" | grep -o '"image":"[^"]*' | cut -d'"' -f4)
        
        if [ -n "$BASE64_IMAGE" ]; then
            # 保存为图片文件
            OUTPUT_IMAGE="processed_$(basename "$TEST_IMAGE" .jpg)_$(date +%s).jpg"
            echo "$BASE64_IMAGE" | base64 -d > "$OUTPUT_IMAGE"
            echo "处理后的图片已保存为: $OUTPUT_IMAGE"
            echo "图片大小: $(wc -c < "$OUTPUT_IMAGE") bytes"
            
            # 提取裁剪位置信息
            POSITION=$(cat "$RESPONSE_FILE" | grep -o '"position":\[[^\]]*\]' | cut -d':' -f2)
            echo "检测到的文档边界: $POSITION"
        else
            echo "未找到图片数据"
        fi
    else
        echo "API调用失败，错误码: $CODE"
        cat "$RESPONSE_FILE"
    fi
    
    echo -e "\n完整响应已保存到: $RESPONSE_FILE"
else
    echo "响应文件未生成"
fi

# 清理临时测试文件（只删除自动创建的临时文件）
if [ "$TEST_IMAGE" = "test_image.png" ]; then
    rm -f "$TEST_IMAGE"
    echo "已清理临时测试文件"
fi

echo -e "\n=== 测试完成 ==="
echo
echo "常见错误码说明:"
echo "- 200: 成功"
echo "- 40303: 文件类型不支持 (可能原因: 文件格式、大小、尺寸超限，或API密钥无效)"
echo "- 40101: 参数错误"
echo "- 40301: 签名错误 (API密钥可能无效)"
echo "- 40401: 余额不足"
echo
echo "如果返回40303，可能的原因:"
echo "1. API密钥无效或过期"
echo "2. 文件格式不支持"
echo "3. 文件大小超过50MB"
echo "4. 图片尺寸不在20-10000像素范围内"