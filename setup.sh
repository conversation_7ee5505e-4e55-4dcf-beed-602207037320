#!/bin/bash

# Image to PDF Project Setup Script
echo "🚀 Setting up Image to PDF B/S Architecture Project..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    print_error "Node.js version must be 16 or higher. Current version: $(node -v)"
    exit 1
fi

print_success "Node.js $(node -v) detected"

# Check if pnpm is available, otherwise use npm
if command -v pnpm &> /dev/null; then
    PACKAGE_MANAGER="pnpm"
    print_status "Using pnpm package manager"
else
    PACKAGE_MANAGER="npm"
    print_status "Using npm package manager"
fi

# Setup frontend dependencies
print_status "Installing frontend dependencies..."
if [ "$PACKAGE_MANAGER" = "pnpm" ]; then
    pnpm install
else
    npm install
fi

if [ $? -eq 0 ]; then
    print_success "Frontend dependencies installed"
else
    print_error "Failed to install frontend dependencies"
    exit 1
fi

# Setup backend dependencies
print_status "Installing backend dependencies..."
cd backend

if [ "$PACKAGE_MANAGER" = "pnpm" ]; then
    pnpm install
else
    npm install
fi

if [ $? -eq 0 ]; then
    print_success "Backend dependencies installed"
else
    print_error "Failed to install backend dependencies"
    exit 1
fi

# Build backend
print_status "Building backend..."
npm run build

if [ $? -eq 0 ]; then
    print_success "Backend built successfully"
else
    print_error "Failed to build backend"
    exit 1
fi

# Return to root directory
cd ..

# Create environment files if they don't exist
print_status "Setting up environment files..."

if [ ! -f ".env" ]; then
    cp .env.example .env
    print_warning "Created .env file from .env.example. Please configure your API settings."
fi

if [ ! -f "backend/.env" ]; then
    cp backend/.env.example backend/.env
    print_warning "Created backend/.env file. Please configure your Textin API credentials."
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p backend/temp
mkdir -p backend/logs

print_success "Directories created"

# Set up git hooks (if in a git repository)
if [ -d ".git" ]; then
    print_status "Setting up git hooks..."
    
    # Create pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Pre-commit hook for Image to PDF project

echo "Running pre-commit checks..."

# Check if backend builds successfully
cd backend
npm run build
if [ $? -ne 0 ]; then
    echo "❌ Backend build failed"
    exit 1
fi
cd ..

# Check if frontend builds successfully
npm run build
if [ $? -ne 0 ]; then
    echo "❌ Frontend build failed"
    exit 1
fi

echo "✅ All pre-commit checks passed"
EOF
    
    chmod +x .git/hooks/pre-commit
    print_success "Git hooks set up"
fi

# Display setup completion message
echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Configure your Textin API credentials in backend/.env:"
echo "   - TEXTIN_APP_ID=your_app_id"
echo "   - TEXTIN_SECRET=your_secret_code"
echo ""
echo "2. Start the development servers:"
echo "   Terminal 1 (Backend):  cd backend && npm run dev"
echo "   Terminal 2 (Frontend): npm run dev"
echo ""
echo "3. Or use Docker Compose:"
echo "   docker-compose up --build"
echo ""
echo "🌐 Application URLs:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:3001"
echo "   Health:   http://localhost:3001/health"
echo ""
echo "📚 For more information, see README.md"
echo ""

print_success "Setup script completed!"