image: qa-roc.apuscn.com/frontend/node22_pnpm:v1.0

stages:
  - build
  - deploy

.build: &build_config
  stage: build
  artifacts:
    paths:
      - dist/
    name: '$CI_JOB_NAME'
    expire_in: 20min
  before_script:
    - pnpm i

.prod: &prod_config
  only:
    - tags

.dev: &dev_config
  only:
    - dev
    - development
    - develop

.test: &test_config
  only:
    - test
    - qa

build:prod:
  <<: *build_config
  <<: *prod_config
  script:
    - pnpm run build

deploy:prod-ali:
  stage: deploy
  image: qa-roc.apuscn.com/frontend/fex:latest
  <<: *prod_config
  script:
    - export FEX_ENV=alioss
    - fex deploy ci2
  dependencies:
    - build:prod
  # when: manual
  environment:
    name: prod-ali

build:dev:
  <<: *build_config
  <<: *dev_config
  before_script:
    - pnpm i
  script:
    - pnpm run build --mode=dev

deploy:dev:
  stage: deploy
  image: qa-roc.apuscn.com/frontend/fex:latest
  <<: *dev_config
  script:
    - fex deploy ci
  dependencies:
    - build:dev
  environment:
    name: dev
    url: https://dev-stark.apuscn.com/$CI_PROJECT_NAME/index.html

build:test:
  <<: *build_config
  <<: *test_config
  script:
    - pnpm run build --mode=test

deploy:test:
  stage: deploy
  image: qa-roc.apuscn.com/frontend/fex:latest
  <<: *test_config
  script:
    - fex deploy ci
  dependencies:
    - build:test
  environment:
    name: test
    url: https://test-stark.apuscn.com/$CI_PROJECT_NAME/index.html
