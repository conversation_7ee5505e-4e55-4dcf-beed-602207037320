# Frontend Dockerfile
FROM qa-roc.apuscn.com/library/node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml* ./

# Install dependencies
RUN npm install -g pnpm
RUN pnpm store prune
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN pnpm run build

# Verify build output
RUN ls -la /app/dist/assets/ && \
    echo "Checking for CSS files..." && \
    find /app/dist -name "*.css" -type f | head -10

# Production stage
FROM qa-roc.apuscn.com/library/nginx:alpine

# Copy built files
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 3000

CMD ["nginx", "-g", "daemon off;"]