#!/bin/bash

# Nacos Configuration Setup Script
echo "🔧 Setting up Nacos configuration for Image to PDF project..."

# Configuration
NACOS_HOST=${NACOS_HOST:-localhost:8848}
NACOS_USERNAME=${NACOS_USERNAME:-nacos}
NACOS_PASSWORD=${NACOS_PASSWORD:-nacos}
NAMESPACE=${NACOS_NAMESPACE:-public}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to wait for Nacos to be ready
wait_for_nacos() {
    print_status "Waiting for Nacos server to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://${NACOS_HOST}/nacos/v3/console/health/readiness" > /dev/null; then
            print_success "Nacos server is ready!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts - Nacos not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    print_error "Nacos server is not ready after $max_attempts attempts"
    return 1
}

# Function to create configuration
create_config() {
    local data_id=$1
    local group=$2
    local content=$3
    local content_type=${4:-json}
    
    print_status "Creating configuration: $data_id (group: $group)"
    
    local response=$(curl -s -w "%{http_code}" -X POST \
        "http://${NACOS_HOST}/nacos/v1/cs/configs" \
        -d "dataId=${data_id}" \
        -d "group=${group}" \
        -d "content=${content}" \
        -d "type=${content_type}" \
        -d "tenant=${NAMESPACE}")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        if [ "$body" = "true" ]; then
            print_success "Configuration $data_id created successfully"
            return 0
        else
            print_error "Failed to create configuration $data_id: $body"
            return 1
        fi
    else
        print_error "HTTP error $http_code when creating configuration $data_id"
        return 1
    fi
}

# Function to check if configuration exists
check_config_exists() {
    local data_id=$1
    local group=$2
    
    local response=$(curl -s -w "%{http_code}" \
        "http://${NACOS_HOST}/nacos/v1/cs/configs?dataId=${data_id}&group=${group}&tenant=${NAMESPACE}")
    
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        return 0  # Configuration exists
    else
        return 1  # Configuration doesn't exist
    fi
}

# Main setup function
main() {
    print_status "Starting Nacos configuration setup..."
    print_status "Nacos Server: $NACOS_HOST"
    print_status "Namespace: $NAMESPACE"
    
    # Wait for Nacos to be ready
    if ! wait_for_nacos; then
        print_error "Cannot proceed without Nacos server"
        exit 1
    fi
    
    # Check if user provided Textin API credentials
    if [ -z "$TEXTIN_APP_ID" ] || [ -z "$TEXTIN_SECRET" ]; then
        print_warning "Textin API credentials not provided in environment variables"
        print_warning "Please enter your Textin API credentials:"
        
        read -p "Textin App ID: " TEXTIN_APP_ID
        read -s -p "Textin Secret: " TEXTIN_SECRET
        echo ""
        
        if [ -z "$TEXTIN_APP_ID" ] || [ -z "$TEXTIN_SECRET" ]; then
            print_error "Textin API credentials are required"
            exit 1
        fi
    fi
    
    # Create Textin API configuration
    local textin_config="{\"appId\":\"${TEXTIN_APP_ID}\",\"secret\":\"${TEXTIN_SECRET}\",\"apiUrl\":\"https://api.textin.com/ai/service/v1/crop_enhance_image\"}"
    
    # Check if configuration already exists
    if check_config_exists "textin-api.json" "image-to-pdf"; then
        print_warning "Configuration textin-api.json already exists"
        read -p "Do you want to update it? (y/N): " update_config
        
        if [ "$update_config" != "y" ] && [ "$update_config" != "Y" ]; then
            print_status "Skipping configuration update"
        else
            if create_config "textin-api.json" "image-to-pdf" "$textin_config" "json"; then
                print_success "Textin API configuration updated"
            else
                print_error "Failed to update Textin API configuration"
                exit 1
            fi
        fi
    else
        if create_config "textin-api.json" "image-to-pdf" "$textin_config" "json"; then
            print_success "Textin API configuration created"
        else
            print_error "Failed to create Textin API configuration"
            exit 1
        fi
    fi
    
    echo ""
    print_success "🎉 Nacos configuration setup completed!"
    echo ""
    print_status "📋 Configuration Summary:"
    echo "   • Data ID: textin-api.json"
    echo "   • Group: image-to-pdf"
    echo "   • Namespace: $NAMESPACE"
    echo ""
    print_status "🌐 Access Nacos Console:"
    echo "   • URL: http://${NACOS_HOST}/nacos"
    echo "   • Username: $NACOS_USERNAME"
    echo "   • Password: $NACOS_PASSWORD"
    echo ""
    print_status "🔧 Next Steps:"
    echo "   1. Start your application with NACOS_SERVER_ADDR environment variable"
    echo "   2. Verify configuration status: curl http://localhost:3001/api/config/status"
    echo "   3. Test API connectivity: curl http://localhost:3001/api/config/test-textin"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            NACOS_HOST="$2"
            shift 2
            ;;
        --username)
            NACOS_USERNAME="$2"
            shift 2
            ;;
        --password)
            NACOS_PASSWORD="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --host HOST        Nacos server host (default: localhost:8848)"
            echo "  --username USER    Nacos username (default: nacos)"
            echo "  --password PASS    Nacos password (default: nacos)"
            echo "  --namespace NS     Nacos namespace (default: public)"
            echo "  --help            Show this help message"
            echo ""
            echo "Environment Variables:"
            echo "  TEXTIN_APP_ID     Your Textin API App ID"
            echo "  TEXTIN_SECRET     Your Textin API Secret"
            echo ""
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run main function
main