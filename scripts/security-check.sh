#!/bin/bash

# Security Check Script - Prevent sensitive data in code repository
echo "🔒 Running security check for sensitive data..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

ISSUES_FOUND=0

print_error() {
    echo -e "${RED}[SECURITY ERROR]${NC} $1"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

# Check for potential API keys in files
check_api_keys() {
    echo "Checking for potential API keys..."
    
    # Check for Textin API patterns
    local textin_patterns=(
        "TEXTIN_APP_ID=.*[a-zA-Z0-9]"
        "TEXTIN_SECRET=.*[a-zA-Z0-9]"
        "appId.*:.*['\"][a-zA-Z0-9]{10,}['\"]"
        "secret.*:.*['\"][a-zA-Z0-9]{20,}['\"]"
    )
    
    local excluded_files=(
        "*.example"
        "*.md"
        "security-check.sh"
        "setup-nacos-config.sh"
        "**/node_modules/**"
        "**/.git/**"
        "**/dist/**"
        "**/build/**"
    )
    
    local exclude_args=""
    for pattern in "${excluded_files[@]}"; do
        exclude_args="$exclude_args --exclude='$pattern'"
    done
    
    for pattern in "${textin_patterns[@]}"; do
        local matches=$(eval "grep -r '$pattern' . $exclude_args" 2>/dev/null || true)
        
        if [ ! -z "$matches" ]; then
            print_error "Potential API key found:"
            echo "$matches" | while read -r line; do
                echo "  $line"
            done
            echo ""
        fi
    done
}

# Check for hardcoded secrets in TypeScript/JavaScript files
check_hardcoded_secrets() {
    echo "Checking for hardcoded secrets in source code..."
    
    local secret_patterns=(
        "const.*[aA]pp[Ii]d.*=.*['\"][^'\"]{10,}['\"]"
        "const.*[sS]ecret.*=.*['\"][^'\"]{20,}['\"]"
        "api[Kk]ey.*=.*['\"][^'\"]{20,}['\"]"
        "token.*=.*['\"][^'\"]{30,}['\"]"
    )
    
    for pattern in "${secret_patterns[@]}"; do
        local matches=$(find . -name "*.ts" -o -name "*.js" -o -name "*.tsx" -o -name "*.jsx" | \
                       xargs grep -l "$pattern" 2>/dev/null || true)
        
        if [ ! -z "$matches" ]; then
            print_error "Potential hardcoded secret found in source files:"
            echo "$matches" | while read -r file; do
                echo "  $file"
                grep "$pattern" "$file" | sed 's/^/    /'
            done
            echo ""
        fi
    done
}

# Check .env files for actual values
check_env_files() {
    echo "Checking .env files for actual secrets..."
    
    local env_files=$(find . -name ".env" -not -path "*/node_modules/*" 2>/dev/null || true)
    
    for env_file in $env_files; do
        if [ -f "$env_file" ]; then
            # Check if TEXTIN_APP_ID or TEXTIN_SECRET have actual values
            local app_id_line=$(grep "^TEXTIN_APP_ID=" "$env_file" 2>/dev/null || true)
            local secret_line=$(grep "^TEXTIN_SECRET=" "$env_file" 2>/dev/null || true)
            
            if [[ "$app_id_line" =~ TEXTIN_APP_ID=.+ ]] && [[ ! "$app_id_line" =~ TEXTIN_APP_ID=$ ]]; then
                if [[ ! "$app_id_line" =~ (your_|example_|test_|demo_) ]]; then
                    print_error "Real API credentials found in $env_file"
                    echo "  $app_id_line"
                fi
            fi
            
            if [[ "$secret_line" =~ TEXTIN_SECRET=.+ ]] && [[ ! "$secret_line" =~ TEXTIN_SECRET=$ ]]; then
                if [[ ! "$secret_line" =~ (your_|example_|test_|demo_) ]]; then
                    print_error "Real API credentials found in $env_file"
                    echo "  $secret_line"
                fi
            fi
        fi
    done
}

# Check git history for accidentally committed secrets
check_git_history() {
    if [ -d ".git" ]; then
        echo "Checking git history for potential secrets..."
        
        local secret_patterns=(
            "TEXTIN_APP_ID.*[a-zA-Z0-9]{10,}"
            "TEXTIN_SECRET.*[a-zA-Z0-9]{20,}"
        )
        
        for pattern in "${secret_patterns[@]}"; do
            local matches=$(git log --all --full-history --grep="$pattern" --oneline 2>/dev/null || true)
            if [ ! -z "$matches" ]; then
                print_warning "Potential secrets in git history (commit messages):"
                echo "$matches"
                echo ""
            fi
            
            # Check file contents in git history
            local file_matches=$(git log --all --full-history -p -S "$pattern" --oneline 2>/dev/null || true)
            if [ ! -z "$file_matches" ]; then
                print_warning "Potential secrets in git history (file contents)"
                echo "Run: git log --all --full-history -p -S \"$pattern\" to investigate"
                echo ""
            fi
        done
    fi
}

# Check for proper .gitignore patterns
check_gitignore() {
    echo "Checking .gitignore patterns..."
    
    if [ ! -f ".gitignore" ]; then
        print_warning ".gitignore file not found"
        return
    fi
    
    local required_patterns=(
        ".env"
        "*.env"
        ".env.local"
        "node_modules"
    )
    
    for pattern in "${required_patterns[@]}"; do
        if ! grep -q "^$pattern" ".gitignore" 2>/dev/null; then
            print_warning ".gitignore should include: $pattern"
        fi
    done
}

# Main execution
echo "Starting security check..."
echo ""

check_api_keys
check_hardcoded_secrets
check_env_files
check_git_history
check_gitignore

echo ""
if [ $ISSUES_FOUND -eq 0 ]; then
    print_success "🎉 No security issues found!"
    echo ""
    echo "✅ Security best practices:"
    echo "   • API keys are managed through Nacos"
    echo "   • No secrets in source code"
    echo "   • Proper .gitignore configuration"
    echo "   • Environment files are excluded from git"
    echo ""
    exit 0
else
    echo ""
    print_error "❌ Found $ISSUES_FOUND security issue(s)"
    echo ""
    echo "🔧 Recommended actions:"
    echo "   1. Remove any hardcoded secrets from source code"
    echo "   2. Use Nacos configuration management"
    echo "   3. Ensure .env files contain only example values"
    echo "   4. Update .gitignore to exclude sensitive files"
    echo "   5. If secrets were committed, consider rotating them"
    echo ""
    exit 1
fi