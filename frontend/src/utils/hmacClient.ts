/**
 * 客户端HMAC认证工具类
 * 用于在前端生成HMAC签名，支持动态密钥提取
 */

interface HMACClientConfig {
  staticSecretKey?: string; // 静态密钥（回退用）
}

interface ExtractedKey {
  key: string;
  version: string;
  expiresAt: number;
}

export class HMACClient {
  private config: HMACClientConfig;

  constructor(config: HMACClientConfig = {}) {
    this.config = config;
  }

  /**
   * 从增强的CSRF token中提取HMAC密钥
   */
  extractKeyFromToken(enhancedToken: string): ExtractedKey | null {
    try {
      const parts = enhancedToken.split('.');
      if (parts.length !== 2) {
        console.warn('Invalid enhanced token format');
        return null;
      }
      
      const [csrfToken, obfuscatedKey] = parts;
      
      // 反混淆
      const keyDataEncoded = this.xorObfuscate(obfuscatedKey, csrfToken);
      const keyDataJson = atob(keyDataEncoded); // 浏览器环境使用atob
      const keyData = JSON.parse(keyDataJson);
      
      // 验证数据结构
      if (!keyData.k || !keyData.v || !keyData.e) {
        console.warn('Invalid key data structure');
        return null;
      }
      
      // 检查是否过期
      if (Date.now() > keyData.e) {
        console.warn('Extracted HMAC key expired');
        return null;
      }
      
      return {
        key: keyData.k,
        version: keyData.v,
        expiresAt: keyData.e
      };
    } catch (error) {
      console.error('Failed to extract HMAC key from token:', error);
      return null;
    }
  }

  /**
   * XOR混淆工具函数（与服务端对应）
   */
  private xorObfuscate(data: string, key: string): string {
    try {
      // 从base64解码
      const binaryData = atob(data);
      let result = '';
      
      for (let i = 0; i < binaryData.length; i++) {
        const charCode = binaryData.charCodeAt(i) ^ key.charCodeAt(i % key.length);
        result += String.fromCharCode(charCode);
      }
      
      return btoa(result); // 返回base64编码
    } catch (error) {
      console.error('XOR obfuscation failed:', error);
      return data;
    }
  }

  /**
   * 生成HMAC签名
   */
  async generateSignature(
    params: Record<string, any>,
    method: string,
    path: string,
    secretKey: string
  ): Promise<string> {
    const timestamp = Math.floor(Date.now() / 1000);
    const nonce = this.generateNonce();
    
    // 构建签名字符串
    const signString = this.buildSignString({
      ...params,
      _nonce: nonce
    }, timestamp, method, path);
    
    // 使用Web Crypto API生成HMAC签名
    const encoder = new TextEncoder();
    const keyData = encoder.encode(secretKey);
    const messageData = encoder.encode(signString);
    
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );
    
    const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
    const signatureHex = Array.from(new Uint8Array(signature))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    return signatureHex;
  }

  /**
   * 为请求添加HMAC认证头
   */
  async addAuthHeaders(
    params: Record<string, any>,
    method: string,
    path: string,
    enhancedToken?: string
  ): Promise<Record<string, string>> {
    const timestamp = Math.floor(Date.now() / 1000);
    const nonce = this.generateNonce();
    
    // 优先尝试从token中提取密钥
    let secretKey = this.config.staticSecretKey;
    if (enhancedToken) {
      const extractedKey = this.extractKeyFromToken(enhancedToken);
      if (extractedKey) {
        secretKey = extractedKey.key;
        console.log('Using dynamic HMAC key, version:', extractedKey.version);
      }
    }
    
    if (!secretKey) {
      throw new Error('No HMAC secret key available');
    }
    
    const signature = await this.generateSignature(params, method, path, secretKey);
    
    return {
      'x-auth-token': signature,
      'x-timestamp': timestamp.toString(),
      'x-trace-id': nonce
    };
  }

  /**
   * 构建签名字符串
   */
  private buildSignString(
    params: Record<string, any>,
    timestamp: number,
    method: string,
    path: string
  ): string {
    const allParams = {
      ...params,
      _timestamp: timestamp,
      _method: method.toUpperCase(),
      _path: path
    };
    
    const sortedKeys = Object.keys(allParams).sort();
    const paramString = sortedKeys
      .map(key => `${key}=${this.stringify(allParams[key])}`)
      .join('&');
    
    return paramString;
  }

  /**
   * 参数值标准化为字符串
   */
  private stringify(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  /**
   * 生成随机数（防重放）
   */
  private generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, b => b.toString(16).padStart(2, '0')).join('');
  }
}

/**
 * 使用示例：
 * 
 * // 1. 获取CSRF token（包含HMAC密钥）
 * const response = await fetch('/api/init/session');
 * const { sessionId, csrfToken } = await response.json();
 * 
 * // 2. 创建HMAC客户端
 * const hmacClient = new HMACClient({
 *   staticSecretKey: 'fallback-key' // 可选，用作回退
 * });
 * 
 * // 3. 为API请求添加认证头
 * const requestData = { files: selectedFiles };
 * const authHeaders = await hmacClient.addAuthHeaders(
 *   requestData,
 *   'POST',
 *   '/upload',
 *   csrfToken // 自动从中提取HMAC密钥
 * );
 * 
 * // 4. 发起认证请求
 * const apiResponse = await fetch('/api/images/upload', {
 *   method: 'POST',
 *   headers: {
 *     'Content-Type': 'application/json',
 *     'x-client-id': sessionId,
 *     'x-request-id': csrfToken,
 *     ...authHeaders
 *   },
 *   body: JSON.stringify(requestData)
 * });
 */

export default HMACClient;