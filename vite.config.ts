import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import fs from 'fs'
import path from 'path'

// Function to get SSL certificates (mkcert generated)
function getSSLConfig() {
  const certDir = path.join(__dirname, '.cert')
  const keyPath = path.join(certDir, 'key.pem')
  const certPath = path.join(certDir, 'cert.pem')
  
  // Check if mkcert certificates exist
  if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
    console.log('✅ Using mkcert certificates for HTTPS')
    return { key: keyPath, cert: certPath }
  }
  
  console.log('⚠️  No mkcert certificates found, using Vite default HTTPS')
  return null
}

const sslConfig = getSSLConfig()

export default defineConfig({
  plugins: [react()],
  root: '.',
  base: './',
  
  server: {
    host: 'localhost',
    port: 5173,
    https: sslConfig ? {
      key: fs.readFileSync(sslConfig.key),
      cert: fs.readFileSync(sslConfig.cert)
    } : true, // Fallback to Vite's default if mkcert certs not available
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        // keep /api prefix as backend uses it
        // rewrite: (path) => path
      }
    }
  },
  build: {
    rollupOptions: {
      input: resolve(__dirname, 'index.html')
    },
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  }
})
