apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-to-pdf
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-to-pdf
  template:
    metadata:
      labels:
        app: image-to-pdf
    spec:
      containers:
      - name: frontend
        image: your-registry/image-to-pdf-frontend:server_0.5.5
        ports:
        - containerPort: 3000
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      - name: backend
        image: your-registry/image-to-pdf-backend:server_0.5.5
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3001"
        - name: NACOS_SERVER_ADDR
          value: "zz-nacos3.internalapus.com:31143"
        - name: NACOS_NAMESPACE
          value: "image-to-pdf"
        - name: ALLOWED_ORIGINS
          value: "https://all-pdf-editor.apusai.com,https://image-to-pdf.apusai.com,https://test-image-to-pdf.apuscn.com"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: image-to-pdf
spec:
  selector:
    app: image-to-pdf
  ports:
  - name: http
    port: 80
    targetPort: 3000
  type: LoadBalancer