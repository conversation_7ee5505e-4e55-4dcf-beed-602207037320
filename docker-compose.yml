

services:
  # Frontend service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    depends_on:
      - image-to-pdf-backend-svc
    networks:
      - app-network

  # Backend service
  image-to-pdf-backend-svc:
    build:
      context: ./backend
      dockerfile: Dockerfile
    # The backend is now only accessible through the internal docker network
    ports:
     - "3001:3001"
    # Docker Compose will automatically look for a .env file in the project root
    # and load the variables from it.
    environment:
      - NODE_ENV=production
      - PORT=3001
      # Logging configuration
      - LOG_LEVEL=info
      # Nacos Configuration - now read from the .env file
      - NACOS_SERVER_ADDR=${NACOS_SERVER_ADDR}
      - NACOS_NAMESPACE=${NACOS_NAMESPACE:-public}
      - NACOS_USERNAME=${NACOS_USERNAME:-nacos}
      - NACOS_PASSWORD=${NACOS_PASSWORD:-nacos}
      # Fallback configuration
      - TEXTIN_APP_ID=${TEXTIN_APP_ID:-}
      - TEXTIN_SECRET=${TEXTIN_SECRET:-}
      # Mobile Client Secret moved to Nacos security.yaml -> mobileClientAuth.secretKey
      # Other settings
      - TEMP_DIR=/app/temp
      - MAX_FILE_SIZE=52428800
      - CLEANUP_INTERVAL=3600000
      - ALLOWED_ORIGINS=http://localhost:3000
    volumes:
      - backend-temp:/app/temp
    networks:
      - app-network
    # JSON logging driver for production
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        labels: "service=image-to-pdf-backend,environment=production"
    # Health check for container orchestration
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  app-network:
    driver: bridge

volumes:
  backend-temp:
